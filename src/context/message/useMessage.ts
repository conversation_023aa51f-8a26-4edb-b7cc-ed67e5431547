import { MessageContext, MessageContextType } from '@context/message/MessageContext';
import { useContext } from 'react';

/**
 * Hook to access message functions
 * @returns Message functions: showSuc<PERSON>, showError, showWarning, showInfo, showLoading
 */
export const useMessage = (): MessageContextType => {
  const context = useContext(MessageContext);
  if (context === undefined) {
    throw new Error('useMessage must be used within a MessageProvider');
  }
  return context;
};
