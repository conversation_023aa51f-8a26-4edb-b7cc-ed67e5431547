import FormElement from '@app/components/ui/FormElement';
import { REPETITION_EVENTS } from '@feat-service-requests/const';

import { DateTimeRecurrenceValues, RecurringEventValues } from '@feat-service-requests/types';
import { DatePicker, Select, TimePicker } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import RecurringDescription from './RecurringDescription';
import RecurringEventsModal from './RecurringEventsModal';

interface DateDayPickerProps {
  dateTimeRecurrenceValues: DateTimeRecurrenceValues;
  isDisabled: boolean;
  onSubmitRecurringEvents: (values: RecurringEventValues) => void;
  updateDateTimeRecurrenceValuesByKey: (
    key: keyof DateTimeRecurrenceValues,
    value: DateTimeRecurrenceValues[keyof DateTimeRecurrenceValues]
  ) => void;
}

// We need to handle pluralization - here
const REPETITION_DAYS_SELECT_OPTIONS = Object.values(REPETITION_EVENTS).map((value) => ({
  label: value.charAt(0).toUpperCase() + value.slice(1),
  value,
}));

const DateDayPicker = ({
  isDisabled,
  dateTimeRecurrenceValues,
  onSubmitRecurringEvents,
  updateDateTimeRecurrenceValuesByKey,
}: DateDayPickerProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = (recurringValues: RecurringEventValues) => {
    setIsModalOpen(false);
    onSubmitRecurringEvents(recurringValues);
  };

  const handleCancel = () => {
    setIsModalOpen(false);

    if (!dateTimeRecurrenceValues.endingDate) {
      updateDateTimeRecurrenceValuesByKey('repeat', 'never');
    }
  };

  return (
    <div className={`w-full flex flex-col ${isDisabled && 'opacity-50 pointer-events-none'}`}>
      <div className="flex flex-col  w-full">
        <div className="flex gap-2 mb-2">
          <FormElement label="Starting date" preserveErrorSpace={false}>
            <DatePicker
              value={dateTimeRecurrenceValues.startingDate}
              onChange={(dayJsDate) => {
                updateDateTimeRecurrenceValuesByKey('startingDate', dayJsDate);

                if (dateTimeRecurrenceValues.endingDate && dayJsDate?.isAfter(dateTimeRecurrenceValues.endingDate)) {
                  updateDateTimeRecurrenceValuesByKey('endingDate', null);
                  updateDateTimeRecurrenceValuesByKey('repeat', 'never');
                }
              }}
              format="DD-MM-YYYY"
              placeholder="Select date"
              className="w-full"
              disabledDate={(currentDate) => currentDate && currentDate < dayjs().startOf('day')}
              allowClear={false}
            />
          </FormElement>
        </div>

        <div className="flex gap-2 mb-2">
          <FormElement label="Start Time" preserveErrorSpace={false}>
            <TimePicker
              value={dateTimeRecurrenceValues.fromTime}
              onChange={(time) => {
                updateDateTimeRecurrenceValuesByKey('fromTime', time);
                updateDateTimeRecurrenceValuesByKey('toTime', time?.add(1, 'hour') ?? null);
              }}
              className="w-full"
              format="HH:mm"
              minuteStep={15}
              needConfirm={false}
              allowClear={false}
            />
          </FormElement>

          <FormElement label="End Time" preserveErrorSpace={false}>
            <TimePicker
              className="w-full"
              value={dateTimeRecurrenceValues.toTime}
              onChange={(time) => updateDateTimeRecurrenceValuesByKey('toTime', time)}
              format="HH:mm"
              minuteStep={15}
              needConfirm={false}
              allowClear={false}
              disabled={
                !dateTimeRecurrenceValues.fromTime ||
                dateTimeRecurrenceValues?.fromTime.isAfter(dateTimeRecurrenceValues.toTime)
              }
              disabledTime={() => {
                if (!dateTimeRecurrenceValues.fromTime) return { disabledHours: () => [], disabledMinutes: () => [] };

                const fromHour = dateTimeRecurrenceValues.fromTime.hour();
                const fromMinute = dateTimeRecurrenceValues.fromTime.minute();

                return {
                  disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter((h) => h < fromHour),
                  disabledMinutes: (hour) =>
                    hour === fromHour ? Array.from({ length: 60 }, (_, i) => i).filter((m) => m < fromMinute) : [],
                };
              }}
            />
          </FormElement>

          <FormElement label="Repeat" preserveErrorSpace={false}>
            <Select
              className="w-full"
              options={REPETITION_DAYS_SELECT_OPTIONS}
              value={dateTimeRecurrenceValues.repeat}
              onChange={(value) => {
                updateDateTimeRecurrenceValuesByKey('repeat', value);

                if (value === 'never') {
                  updateDateTimeRecurrenceValuesByKey('endingDate', null);
                  updateDateTimeRecurrenceValuesByKey('repetitionDaysOfTheWeek', []);
                  return;
                }

                if (value === 'daily' || value === 'weekly') {
                  updateDateTimeRecurrenceValuesByKey('frequency', value);

                  if (value === 'weekly') {
                    updateDateTimeRecurrenceValuesByKey('repetitionDaysOfTheWeek', []);
                  }

                  showModal();
                }
              }}
            />
          </FormElement>
        </div>
      </div>

      <RecurringDescription
        startingDate={dateTimeRecurrenceValues.startingDate}
        endingDate={dateTimeRecurrenceValues.endingDate}
        repeat={dateTimeRecurrenceValues.repeat}
        fromTime={dateTimeRecurrenceValues.fromTime}
        toTime={dateTimeRecurrenceValues.toTime}
        interval={dateTimeRecurrenceValues.interval}
        repetitionDaysOfTheWeek={dateTimeRecurrenceValues.repetitionDaysOfTheWeek}
        onEditClick={() => {
          if (dateTimeRecurrenceValues.repeat !== 'never') {
            showModal();
          }
        }}
      />

      <RecurringEventsModal
        key={`${dateTimeRecurrenceValues.startingDate?.format('DD-MM-YYYY')}-${dateTimeRecurrenceValues.repeat}`}
        isOpen={isModalOpen}
        initialValues={{
          startingDate: dateTimeRecurrenceValues.startingDate,
          endingDate: dateTimeRecurrenceValues.endingDate,
          interval: dateTimeRecurrenceValues.interval,
          frequency: dateTimeRecurrenceValues.frequency,
          repetitionDaysOfTheWeek: dateTimeRecurrenceValues.repetitionDaysOfTheWeek,
        }}
        onConfirm={(recurringValues) => {
          handleOk(recurringValues);
        }}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default DateDayPicker;
