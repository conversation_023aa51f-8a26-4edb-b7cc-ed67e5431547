import envConfig, { EnvConfig } from '@app/enviroment/enviroment';
import { TShowFeature } from '@feat-auth/types';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import FeatureContext from './FeaturesContext';

const FeatureProvider = ({ children }: { children: ReactNode }) => {
  const [features, setFeatures] = useState<EnvConfig>();
  const [loading, setLoading] = useState<boolean>(true);

  const showFeature: TShowFeature = useCallback(
    (key) => {
      return features?.getEnvKey(key) as boolean;
    },
    [features]
  );

  // We should request user by authToken (IN OUR EXAMPLE IT'S API_TOKEN) before rendering the application
  useEffect(() => {
    setLoading(false);
    //We will make an api call here to the tenant manager to get the features mainwhile will return loading...
    setFeatures(envConfig);
    setLoading(false);
  }, []);
  //Loading Tenant Metadata

  if (loading) return <></>;

  return (
    <FeatureContext.Provider
      value={{
        showFeature,
      }}
    >
      {children}
    </FeatureContext.Provider>
  );
};

export default FeatureProvider;
