import { LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import { CommonListResponse } from '@app/types/table.types';
import { Button, Divider, GetRef, Input, InputRef, Select, Spin, theme } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import { ComponentType, ReactNode, UIEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IoIosAdd } from 'react-icons/io';
export type TSearchSelectNewValuesProps = {
  allowNewValue?: boolean;
  newValueText?: string | ((value: string) => ReactNode);
  newValueCb?: (isNew: boolean, value?: string) => void; // <-- updated here
};

export type SearchSelectProps<T> = {
  data: CommonListResponse<T>;
  value: DefaultOptionType[] | string | undefined;
  ajaxSize?: number;
  searchTerm?: string;
  disabled?: boolean;
  isClearable?: boolean;
  readOnly?: boolean;
  placeholder?: string;
  OptionComponent?: ComponentType<T>;
  clearSearchOnSelect?: boolean;
  mode?: 'multiple' | 'tags';
  notFoundContent?: ReactNode; // Add support for custom no data text
  style?: React.CSSProperties;
  canAddNew?: boolean;
  priorityOrder?: DefaultOptionType[];
  getId: (item: T) => string | number;
  getLabel: (item: T) => string | React.ReactNode;
  onSearchChange: (value: string) => void;
  onValueChange?: (item?: T | T[] | string) => void;
  handleScrollSetData: (searchValue: string, ajaxSize: number, ajaxIndex: number) => Promise<void>;
  onValueSelect?: (item: T) => void;
  onValueDeselect?: (item: number | string) => void;
  onClear?: () => void;
  optionRender?: (option: T) => ReactNode;
  optionDisable?: (item: T) => boolean;
  onCreateNew?: (input: string) => void;
  tagRender?: (props: CustomTagProps) => React.ReactElement;
} & TSearchSelectNewValuesProps;

const SearchSelect = <T,>({
  ajaxSize = 5,
  data,
  value,
  searchTerm,
  disabled = false,
  isClearable = true,
  readOnly = false,
  placeholder = 'Select',
  mode,
  allowNewValue = false,
  newValueText = 'Value not found, please add manually',
  priorityOrder,
  canAddNew,
  style = {},
  notFoundContent = 'No data', // Support custom no data text
  onValueChange,
  getId,
  getLabel,
  handleScrollSetData,
  onSearchChange,
  onValueSelect,
  onValueDeselect,
  optionDisable,
  tagRender,
  onCreateNew,
  newValueCb = () => {},
}: SearchSelectProps<T>) => {
  const ContainerHeight = 256; // Set dropdown max height

  const [fetching, setFetching] = useState(false);
  const ajaxIndex = useRef<number>(0);
  const [searchValue, setSearchValue] = useState<string>(searchTerm ?? '');
  type SelectRefType = GetRef<typeof Select>; // BaseSelectRef
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<InputRef>(null);
  const selectRef = useRef<SelectRefType>(null);

  const { useToken } = theme;

  // Why a reusable pure component consume context ? Not all project have the same authentication way?
  const { token } = useToken();

  //TODO :  useEffects are not needed - its a not a sync with 3rd party. - Tha means that states issues exists - and useEffect sync that state

  useEffect(() => {
    onSearchChange(searchValue);
    if (searchValue.length == 1 || searchValue.length == 2 || searchValue.length == 0) setFetching(false);
  }, [searchValue, onSearchChange]);

  useEffect(() => {
    setFetching(false);
  }, [data]);

  const handleScrollEnd = useCallback(
    async (e: UIEvent<HTMLElement>) => {
      const shouldScroll = Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - ContainerHeight) <= 1;

      const shouldIncrementIndex = ajaxIndex.current * ajaxSize < data.TotalResults;

      if (shouldScroll && shouldIncrementIndex) {
        try {
          setFetching(true);
          ajaxIndex.current++;
          await handleScrollSetData(searchValue, ajaxSize, ajaxIndex.current);
        } finally {
          setFetching(false);
        }
      }
    },
    [searchValue, ajaxSize, handleScrollSetData, data.TotalResults]
  );

  const items = useMemo(() => {
    return data?.Results?.map((item) => ({
      data: item,
      key: getId(item),
      label: getLabel(item),
      value: getId(item),
      disabled: optionDisable ? optionDisable(item) : false,
    }));
  }, [data, getId, getLabel, optionDisable]);

  const changePriority = useCallback(
    (optionA: DefaultOptionType, optionB: DefaultOptionType) => {
      if (priorityOrder) {
        const indexA = priorityOrder.findIndex((po) => po.value === optionA.value);
        const indexB = priorityOrder.findIndex((po) => po.value === optionB.value);

        if (indexA !== -1 && indexB !== -1) return indexA - indexB;
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;
        return String(optionA.label || '').localeCompare(String(optionB.label || ''));
      }
      return String(optionA.label || '').localeCompare(String(optionB.label || ''));
    },
    [priorityOrder]
  );

  const addNewValue = useCallback(() => {
    if (!fetching && !items.length && onValueChange) {
      onValueChange(searchValue);
      setSearchValue('');
      newValueCb(true, searchValue); // <-- pass the value here
    }
  }, [fetching, items, onValueChange, searchValue, newValueCb]);
  return (
    <>
      <Select<T>
        tagRender={tagRender}
        data-testid="wpSearchField"
        notFoundContent={allowNewValue ? <></> : notFoundContent}
        ref={selectRef}
        style={{ ...style, width: '100%' }}
        mode={mode}
        options={items}
        placeholder={placeholder}
        filterSort={changePriority}
        allowClear={isClearable}
        onOpenChange={(dd) => {
          !dd && setSearchValue('');

          setTimeout(() => {
            dd && inputRef.current?.focus();
          }, 300);
        }}
        value={value as T}
        onPopupScroll={handleScrollEnd}
        size="large"
        disabled={disabled}
        onSelect={(id) => {
          const selected = data.Results.find((x) => getId(x) === id);
          selected && onValueSelect && onValueSelect(selected);
          newValueCb(false);
        }}
        onClear={() => {
          newValueCb(false);
        }}
        onDeselect={(id) => onValueDeselect && onValueDeselect(id as number)}
        onChange={(x) => {
          if (Array.isArray(x)) {
            onValueChange &&
              onValueChange(
                data.Results.filter(
                  (rItem) => Array.isArray(x) && x.find((sItem: string | number) => getId(rItem) === sItem)
                ) as T
              );
          } else {
            onValueChange && onValueChange(data.Results.find((item) => getId(item) === x));
          }
          setSearchValue('');
          newValueCb(false);
        }}
        popupRender={(menu) => (
          <div>
            <Input
              onKeyDown={(e) => {
                fetching && e.stopPropagation();

                e.key === 'Backspace' && e.stopPropagation();
                if (e.key === 'Enter') addNewValue();
              }}
              readOnly={readOnly}
              ref={inputRef}
              size="middle"
              onChange={(val) => {
                setFetching(true);
                setSearchValue(val.currentTarget.value);
                ajaxIndex.current = 0;
              }}
              placeholder="Search"
              className="w-full"
              value={searchValue}
              allowClear
              suffix={
                <span className="flex w-full place-self-end" style={{ color: token.colorTextPlaceholder }}>
                  {items?.length ?? 0}
                  {' / '}
                  {data?.TotalResults ?? 0}
                </span>
              }
              prefix={
                <SearchOutlined
                  style={{
                    color: token.colorIcon,
                    fontSize: token.fontSizeSM,
                  }}
                />
              }
            />
            <Divider style={{ margin: '8px 0' }} />
            {allowNewValue && !items?.length && (
              <>
                <div className="flex justify-center items-center text-sm font-light">
                  {typeof newValueText === 'string' ? <div>{newValueText}</div> : newValueText(searchValue as string)}
                  <div className="font-bold ml-2 italic">
                    <Button size="small" type="primary" onClick={addNewValue} icon={<IoIosAdd />}></Button>
                  </div>
                </div>
                <Divider style={{ margin: '8px 0' }} />
              </>
            )}
            <>{menu}</>
            <div ref={loadMoreRef} style={{ height: 4, background: 'transparent' }}></div>
            {fetching && (
              <div
                className="bottom-0 left-0 absolute w-full justify-center items-center flex gap-4 py-2 "
                style={{
                  backgroundColor: token.colorInfoBg + 'BF',
                  borderRadius: token.borderRadiusOuter,
                }}
              >
                Load More
                <Spin indicator={<LoadingOutlined spin />} size="small" />
              </div>
            )}
            <div>
              {/* Search input ... */}
              <Divider style={{ margin: '8px 0' }} />
              {canAddNew && onCreateNew && searchValue?.trim() && (
                <div
                  style={{
                    padding: 8,
                    textAlign: 'center',
                    cursor: 'pointer',
                    borderTop: '1px solid #f0f0f0',
                    color: '#1890ff',
                  }}
                  onMouseDown={(e) => e.preventDefault()}
                  onClick={() => onCreateNew(searchValue)}
                >
                  <IoIosAdd /> Create new: <b>{searchValue}</b>
                </div>
              )}
              {/* loading spinner ... */}
            </div>
          </div>
        )}
      ></Select>
    </>
  );
};

export default SearchSelect;
