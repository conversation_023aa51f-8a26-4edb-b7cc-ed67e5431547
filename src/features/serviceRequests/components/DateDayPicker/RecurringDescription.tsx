import { Dayjs } from 'dayjs';
import { FiEdit2 } from 'react-icons/fi';

interface RecurringDescriptionProps {
  startingDate: Dayjs;
  endingDate: Dayjs | null;
  repeat: 'daily' | 'weekly' | 'never';
  fromTime: Dayjs;
  toTime: Dayjs;
  interval: number;
  repetitionDaysOfTheWeek: number[]; // 0..6 (Sun..Sat)
  onEditClick: () => void;
}

const DAYS_TRANSLATIONS_FROM_NUMBER = {
  0: 'Sunday',
  1: 'Monday',
  2: 'Tuesday',
  3: 'Wednesday',
  4: 'Thursday',
  5: 'Friday',
  6: 'Saturday',
} as const;

// ---------- Helpers for the summary ----------
type DOW = 0 | 1 | 2 | 3 | 4 | 5 | 6;

const plural = (n: number, one: string, many: string) => (n === 1 ? one : many);

const formatDate = (d?: Dayjs | null) => (d ? d.format('DD-MM-YYYY') : '—');
const formatTime = (t?: Dayjs | null) => (t ? t.format('HH:mm') : '—');

const uniqSorted = (arr: number[]) => Array.from(new Set(arr)).sort((a, b) => a - b);

const isAllDays = (days: number[]) => {
  const u = uniqSorted(days);
  return u.length === 7 && u.every((d, i) => d === i);
};

const isWeekdays = (days: number[]) => {
  const s = new Set(days);
  return [1, 2, 3, 4, 5].every((d) => s.has(d)) && !s.has(0) && !s.has(6);
};

const isWeekend = (days: number[]) => {
  const s = new Set(days);
  return s.size === 2 && s.has(0) && s.has(6);
};

const joinDays = (days: number[], map: Record<DOW, string>) => {
  const names = uniqSorted(days).map((d) => map[d as DOW]);
  if (names.length <= 2) return names.join(' and ');
  return `${names.slice(0, -1).join(', ')} and ${names[names.length - 1]}`;
};

function summarizeEvent(params: {
  repeat: 'never' | 'daily' | 'weekly';
  interval: number;
  startingDate?: Dayjs | null;
  endingDate?: Dayjs | null;
  fromTime?: Dayjs | null;
  toTime?: Dayjs | null;
  repetitionDaysOfTheWeek: number[];
  DAYS_TRANSLATIONS_FROM_NUMBER: Record<DOW, string>;
}) {
  const {
    repeat,
    interval,
    startingDate,
    endingDate,
    fromTime,
    toTime,
    repetitionDaysOfTheWeek,
    DAYS_TRANSLATIONS_FROM_NUMBER,
  } = params;

  // Validate dates
  if (!startingDate) return 'Start date is not set.';
  if (endingDate && startingDate.isAfter(endingDate, 'day')) {
    return `Start date ${formatDate(startingDate)} is after end date ${formatDate(endingDate)}.`;
  }

  // Time phrase (handles missing & overnight)
  let timePhrase = '';
  if (fromTime && toTime) {
    const overnight = toTime.isBefore(fromTime);
    timePhrase = ` from ${formatTime(fromTime)} to ${formatTime(toTime)}${overnight ? ' next day' : ''}`;
  } else if (fromTime) {
    timePhrase = ` starting at ${formatTime(fromTime)}`;
  } else if (toTime) {
    timePhrase = ` ending at ${formatTime(toTime)}`;
  }

  // Range phrase
  const rangePhrase = endingDate
    ? ` from ${formatDate(startingDate)} to ${formatDate(endingDate)}`
    : ` from ${formatDate(startingDate)} onwards`;

  if (repeat === 'never') {
    return `This event occurs once on ${formatDate(startingDate)}${timePhrase || ''}.`;
  }

  if (repeat === 'daily') {
    const every = interval === 1 ? 'every day' : `every ${interval} ${plural(interval, 'day', 'days')}`;
    return `This event repeats ${every}${rangePhrase}${timePhrase}.`;
  }

  // Weekly
  const days = repetitionDaysOfTheWeek ?? [];
  if (days.length === 0) {
    return `Weekly repetition has no days selected. Please pick at least one day.`;
  }

  // Shortcuts
  if (isAllDays(days)) {
    // If interval==1 and all days → "every day"
    const every = interval === 1 ? 'every day' : `every ${interval} ${plural(interval, 'week', 'weeks')} (all days)`;
    return `This event repeats ${interval === 1 ? 'every day' : every}${rangePhrase}${timePhrase}.`;
  }

  if (isWeekdays(days)) {
    const every = interval === 1 ? 'every weekday (Mon–Fri)' : `every ${interval} weeks on weekdays (Mon–Fri)`;
    return `This event repeats ${every}${rangePhrase}${timePhrase}.`;
  }

  if (isWeekend(days)) {
    const every = interval === 1 ? 'every weekend (Sat–Sun)' : `every ${interval} weeks on weekends (Sat–Sun)`;
    return `This event repeats ${every}${rangePhrase}${timePhrase}.`;
  }

  // Normal weekly list
  const dayList = joinDays(days, DAYS_TRANSLATIONS_FROM_NUMBER as Record<DOW, string>);
  const every = interval === 1 ? 'every week' : `every ${interval} ${plural(interval, 'week', 'weeks')}`;
  return `This event repeats ${every} on ${dayList}${rangePhrase}${timePhrase}.`;
}

// ---------- Component ----------
const RecurringDescription = ({
  startingDate,
  endingDate,
  repeat,
  fromTime,
  toTime,
  interval,
  repetitionDaysOfTheWeek,
  onEditClick,
}: RecurringDescriptionProps) => {
  const description = summarizeEvent({
    repeat,
    interval,
    startingDate,
    endingDate,
    fromTime,
    toTime,
    repetitionDaysOfTheWeek,
    DAYS_TRANSLATIONS_FROM_NUMBER: DAYS_TRANSLATIONS_FROM_NUMBER as Record<DOW, string>,
  });

  return (
    <div
      className={`group relative w-full rounded-lg border border-gray-200 bg-white shadow-sm
                  p-2 pr-10 transition-all duration-200
                  ${endingDate ? 'cursor-pointer hover:shadow-md hover:border-gray-300' : ''}`}
      onClick={endingDate ? onEditClick : undefined}
      role={endingDate ? 'button' : undefined}
      tabIndex={endingDate ? 0 : -1}
      onKeyDown={(e) => {
        if (!endingDate) return;
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onEditClick?.();
        }
      }}
    >
      {/* Slim left accent */}
      <span
        aria-hidden
        className="pointer-events-none absolute inset-y-1 left-2 w-px rounded-full bg-gradient-to-b from-indigo-400 to-blue-500"
      />

      {/* Description */}
      <p className="ml-4 text-[13px] leading-snug text-gray-700">{description}</p>

      {/* Edit icon — always shown when endingDate, faint by default, stronger on hover/focus */}
      {endingDate && (
        <button
          type="button"
          aria-label="Edit event"
          className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full border border-gray-200 bg-white p-1.5 shadow-sm
                     opacity-60 transition-all duration-150
                     group-hover:opacity-100 focus:opacity-100 group-hover:scale-105 focus:scale-105"
          onClick={(e) => {
            e.stopPropagation();
            onEditClick?.();
          }}
        >
          <FiEdit2 className="h-3.5 w-3.5 text-gray-700" />
        </button>
      )}
    </div>
  );
};

export default RecurringDescription;
