import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import { StateCreator } from 'zustand';

export type CaregiversSlice = {
  caregivers: CaregiverWithServices[];
  setCaregivers: (caregivers: CaregiverWithServices[]) => void;
  availableCaregivers: CaregiverWithServices[];
  setAvailableCaregivers: (caregivers: CaregiverWithServices[]) => void;
};

export const createCaregiversSlice: StateCreator<CaregiversSlice> = (set) => ({
  caregivers: [],
  setCaregivers: (caregivers) => set({ caregivers }),
  availableCaregivers: [],
  setAvailableCaregivers: (availableCaregivers) => set({ availableCaregivers }),
});
