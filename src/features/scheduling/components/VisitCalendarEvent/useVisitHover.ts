import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { useState } from 'react';

type UseVisitHoverProps = {
  visit: VisitPopulated;
};

/**
 * Hook that manages visit hover state for popover content
 */
export function useVisitHover({ visit }: UseVisitHoverProps) {
  const [hoveredVisit, setHoveredVisit] = useState<VisitPopulated | null>(null);

  const handleMouseEnter = () => setHoveredVisit(visit);
  const handleMouseLeave = () => setHoveredVisit(null);

  return {
    hoveredVisit,
    handleMouseEnter,
    handleMouseLeave,
  };
}
