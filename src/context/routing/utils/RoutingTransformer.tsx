import { RoutesStructureWithStringKeys } from '@app/types/routing.types';
import { Suspense } from 'react';
import { Navigate, Outlet, RouteObject } from 'react-router-dom';

const RoutingTransformer = (routesConfig: RoutesStructureWithStringKeys) => {
  const transformed = Object.keys(routesConfig)
    .filter((routeKey) => routesConfig[routeKey].show) // Only include routes marked as "show: true"
    .map((routeKey) => {
      const routeItem = routesConfig[routeKey];
      //
      // If the route has nested routes, create an Outlet and recursively transform child routes
      if (routeItem.routes) {
        return {
          path: routeKey,
          element: (
            <Suspense fallback={<div>Loading...</div>}>
              <Outlet />
            </Suspense>
          ),
          children: [
            ...RoutingTransformer(routeItem.routes),
            { path: '*', element: <Navigate to="/404" replace /> }, // Fallback route
          ], // Recursively process child routes
        };
      }

      // If it's a direct route, return it
      if (routeItem.route) {
        return {
          path: routeKey,
          element: <routeItem.route />,
        };
      }

      return null; // Skip if neither `routes` nor `route` exist
    })
    .filter(Boolean) as RouteObject[]; // Remove null values
  return transformed;
};

export default RoutingTransformer;
