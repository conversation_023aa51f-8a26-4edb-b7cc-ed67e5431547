import React from 'react';

/**
 * Icon component type for menu items
 */
export type IconComponent = React.ComponentType<React.SVGProps<SVGSVGElement>>;

/**
 * Menu item structure with recursive children support
 */
export interface MenuItem {
  /** Unique identifier for the menu item */
  key: string | number;
  /** Optional icons for filled and outline states */
  icon?: {
    filled: IconComponent;
    outline: IconComponent;
  };
  /** Display label for the menu item */
  label: string;
  /** Optional nested children menu items */
  children?: MenuItem[];
}

/**
 * Props for menu components
 */
export interface MenuProps {
  items: MenuItem[];
  depth?: number;
}

/**
 * Props for individual menu item components
 */
export interface MenuItemProps {
  item: MenuItem;
  isActive: boolean;
  hasChildren: boolean;
  hasActiveChild: boolean;
  index: number;
  depth: number;
  onNavigate: (key: string | number) => void;
}

/**
 * Extended props for expanded menu items
 */
export interface ExpandedMenuItemProps extends MenuItemProps {
  isOpen: boolean;
  isSidebarCollapsed: boolean;
  onToggle: (key: string | number) => void;
}
