import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Transfer } from 'antd';
import { useEffect, useState } from 'react';

interface ServicesSelectCaregiverProps {
  value?: unknown; // form integration may pass unknown
  onChange: (val: unknown) => void;
  error?: string;
}

export function ServicesSelectCaregiver({ value, onChange }: ServicesSelectCaregiverProps) {
  const { openNotification } = useNotifications();
  const services = useSchedulingStore((state) => state.services);
  const setServices = useSchedulingStore((state) => state.setServices);

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // Fetch services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await ApiClient.serviceApi.services.getServicesServicesGet();
        setServices(response.data.data);
      } catch (err) {
        openNotification('topRight', {
          title: 'Services',
          description: 'Failed to fetch services.',
          type: 'Warning',
        });
      }
    };
    fetchServices();
  }, [openNotification, setServices]);

  // Update selected keys when value or therapies change
  useEffect(() => {
    if (!Array.isArray(value) || services.length === 0) {
      setSelectedKeys([]);
      return;
    }

    // Build a map of serviceId -> service
    const servicesMap = new Map(services.map((t) => [String(t.serviceId), t]));

    let keys: string[] = [];

    if (typeof value[0] === 'object' && value[0] !== null && 'serviceId' in value[0]) {
      // case 1: array of objects with serviceId
      keys = value
        .map((v: Service) => {
          const key = String(v.serviceId);
          return servicesMap.has(key) ? key : null;
        })
        .filter((k): k is string => k !== null);
    } else {
      // case 2: array of primitives (string | number)
      keys = (value as (string | number)[])
        .map((v) => {
          const key = String(v);
          return servicesMap.has(key) ? key : null;
        })
        .filter((k): k is string => k !== null);
    }

    setSelectedKeys(keys);
  }, [value, services]);

  return (
    <Transfer
      dataSource={services.map((t) => ({
        key: String(t.serviceId),
        name: t.name,
      }))}
      titles={['Services', 'Selected']}
      targetKeys={selectedKeys}
      onChange={(keys) => {
        console.log('keys', keys);
        onChange(keys.map((k) => String(k)));
      }}
      render={(item) => item.name}
      listStyle={{ width: 500, height: 500 }}
      showSearch
      filterOption={(inputValue, item) => item.name.toLowerCase().includes(inputValue.toLowerCase())}
    />
  );
}
