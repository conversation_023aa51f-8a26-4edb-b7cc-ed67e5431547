// This file has been renamed to urlParamUtils.ts. Please use that file instead.

// Utility for setting and removing URL params based on an object of keys/values

export function setParams(
  baseUrl: string,
  params: Record<string, string | number | boolean | undefined | null>
): string {
  const url = new URL(baseUrl, window.location.origin);

  // If params is empty, clear all search params
  if (Object.keys(params).length === 0) {
    url.search = '';
    return url.toString();
  }

  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null || value === '') {
      url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, String(value));
    }
  });
  return url.toString();
}

export function removeParams(baseUrl: string, keys: string[]): string {
  const url = new URL(baseUrl, window.location.origin);
  keys.forEach((key) => {
    url.searchParams.delete(key);
  });
  return url.toString();
}
