import { ApiClient } from '@api/api-configuration';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';

/**
 * Fetches available caregivers for a specific visit
 * @param activeVisit - The visit to find caregivers for
 * @returns Promise with available caregivers data
 */
export const fetchAvailableCaregivers = async (activeVisit: VisitPopulated) => {
  if (!activeVisit) return [];

  try {
    const caregivers = await ApiClient.caregiverApi.caregivers.caregiversAvailableCaregiversAvailableGet({
      from: activeVisit.startTime,
      to: activeVisit.endTime,
      service_ids: activeVisit.services.map((s) => s.id),
    });

    console.log({ caregivers: caregivers.data });
    return caregivers.data;
  } catch (error) {
    console.error('Error fetching caregivers', error);
    return [];
  }
};
