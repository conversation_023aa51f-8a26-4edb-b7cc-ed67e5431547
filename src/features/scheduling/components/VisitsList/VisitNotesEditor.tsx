import { CheckOutlined, CloseOutlined, EditOutlined } from '@ant-design/icons';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { countLines } from '@app/utils/countLines';
import { useMessage } from '@context/message';
import { Button } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useState } from 'react';
import './VisitNotesEditor.scss';

type VisitNotesEditorProps = {
  visit: VisitPopulated;
  onNotesUpdate?: (visitId: number, notes: string) => Promise<void>;
};

/**
 * Editable notes component
 * Shows formatted notes by default, becomes editable on hover/click
 */
export function VisitNotesEditor({ visit, onNotesUpdate }: VisitNotesEditorProps) {
  const { showError } = useMessage();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [notes, setNotes] = useState(() => {
    // Combine client and service request notes
    const clientNotes = visit.client.notes || '';
    const serviceNotes = visit.serviceRequest.notes || '';
    return `${clientNotes}${clientNotes && serviceNotes ? '\n\n' : ''}${serviceNotes}`.trim();
  });

  const handleEdit = () => {
    setIsEditing(true);
    // Clear the notes for fresh input
    setNotes('');
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset to original combined notes
    const clientNotes = visit.client.notes || '';
    const serviceNotes = visit.serviceRequest.notes || '';
    setNotes(`${clientNotes}${clientNotes && serviceNotes ? '\n\n' : ''}${serviceNotes}`.trim());
  };

  const handleSave = async () => {
    if (!notes.trim()) {
      showError('Please enter some notes');
      return;
    }

    setIsLoading(true);
    try {
      if (onNotesUpdate) {
        await onNotesUpdate(visit.id, notes.trim());
      }
      setIsEditing(false);
    } catch (error) {
      showError('Failed to update notes');
      console.error('Notes update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get display notes
  const displayNotes = (() => {
    let notes = '';
    const clientNotes = visit.client.notes || '';
    const serviceNotes = visit.serviceRequest.notes || '';
    const visitNotes = visit.notes || '';

    if (clientNotes)
      notes += `Client notes:\n${clientNotes}${serviceNotes ? '\n---------------------\nService Notes:\n' : ''}${serviceNotes}`;
    if (visitNotes) notes += `${notes ? '\n---------------------\nVisit Notes:\n' : ''}${visitNotes}`;

    return notes.trim();
  })();

  if (isEditing) {
    return (
      <div className="my-2">
        <div className="text-sm mb-2">Σημειώσεις</div>
        <div className="rounded-sm p-3 bg-white/20">
          <div className="flex flex-col gap-2">
            <TextArea
              id="visitcard-textarea"
              rows={4}
              className="w-full border !bg-white/30 !border-white/50 !border-dashed rounded-md !text-gray-100 !text-xs"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Εισάγετε σημειώσεις..."
              autoFocus
              onWheel={(e) => {
                // Stop propagation to prevent parent scrolling
                e.stopPropagation();
              }}
              onMouseDown={(e) => {
                // Stop propagation to prevent unwanted parent interactions
                e.stopPropagation();
              }}
              style={{
                // Ensure the textarea can scroll independently
                overflowY: 'auto',
              }}
            />

            <div className="flex justify-end gap-1">
              <Button
                size="small"
                className="!bg-white/30 !border-none !text-white hover:!bg-white/20"
                icon={<CloseOutlined />}
                onClick={handleCancel}
                disabled={isLoading}
              />
              <Button
                size="small"
                className="!bg-app-primary !border-none !text-white hover:!bg-app-primary/60"
                type="primary"
                icon={<CheckOutlined />}
                onClick={handleSave}
                loading={isLoading}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!displayNotes) {
    return (
      <div className="my-2">
        <div className="text-sm mb-2">Σημειώσεις</div>
        <div
          className="group relative border border-transparent hover:border-white/50 hover:border-dashed rounded-md px-2 py-2 cursor-pointer transition-all duration-200 bg-white/20"
          onClick={handleEdit}
        >
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-400 italic">Κάντε κλικ για προσθήκη σημειώσεων...</p>
            <EditOutlined className="text-gray-400 transition-opacity duration-200 ml-2 text-xs" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="my-2">
      <div className="flex items-center justify-between px-1 pb-1">
        <div className="text-sm ">Σημειώσεις</div>
        <EditOutlined className="text-gray-400 ml-2 text-xs mt-1 flex-shrink-0" />
      </div>
      <div
        className="group relative border border-transparent hover:border-white/50 hover:border-dashed rounded-md  cursor-pointer transition-all duration-200 bg-white/20"
        onClick={handleEdit}
      >
        <div className="flex items-start justify-between">
          <TextArea
            id="visitcard-textarea"
            rows={countLines(displayNotes, 40)}
            className="w-full h-20 border-none !bg-transparent !text-app-white !text-xs resize-none cursor-pointer"
            value={displayNotes}
            readOnly
            onWheel={(e) => {
              // Allow scrolling within the textarea but prevent bubbling
              e.stopPropagation();
            }}
            onClick={(e) => {
              // When clicking on textarea, trigger edit mode
              e.stopPropagation();
              handleEdit();
            }}
            style={{
              // Ensure the textarea can scroll independently
              overflowY: 'auto',
              pointerEvents: 'auto', // Allow interactions
            }}
          />
        </div>
      </div>
    </div>
  );
}
