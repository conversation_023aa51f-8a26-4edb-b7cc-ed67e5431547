import { Error404 } from '@app/features/errors/components/Error404';
import { Error500 } from '@app/features/errors/components/Error500';
import { ErrorsLayout } from '@app/features/errors/ErrorsLayout';
import { Navigate, Route, Routes } from 'react-router-dom';

const ErrorsRoutes = () => (
  <Routes>
    <Route element={<ErrorsLayout />}>
      <Route path="404" element={<Error404 />} />
      <Route path="500" element={<Error500 />} />
      {/* The following route will match any other path under "/errors/" */}
      <Route path="/*" element={<Navigate to="/404" />} />
    </Route>
  </Routes>
);

export default ErrorsRoutes;
