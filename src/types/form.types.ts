type FieldType =
  | 'text'
  | 'email'
  | 'password'
  | 'date'
  | 'select'
  | 'multiple-select'
  | 'checkbox'
  | 'radiogroup'
  | 'number'
  | 'textarea'
  | 'list'
  | 'address'
  | 'custom';

export type FieldComponentProps<TValue> = {
  value: TValue;
  onChange: (val: TValue) => void;
  error?: unknown; // or string | boolean, etc.
};

// Address-specific types for the autocomplete component
export type AddressValue = {
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  formatted_address: string;
  place_id: string;
  geoLocation?: {
    lat: number;
    lng: number;
  };
};

export type AddressFieldProps = FieldComponentProps<AddressValue | string> & {
  apiKey?: string;
  placeholder?: string;
  types?: string[];
  language?: string;
  region?: string;
  onCartAddressUpdate?: (address: AddressValue) => void;
  onPatientUpdate?: (addressData: Partial<AddressValue>) => void;
};

export type FieldConfig<TValue, TComponentProps extends FieldComponentProps<TValue> = FieldComponentProps<TValue>> = {
  name: string;
  label?: string;
  type: FieldType;
  width?: 'third' | 'half' | 'twoThirds' | 'full';
  rules?: Record<string, unknown>;
  options?: { label: string; value: unknown }[];
  component?: React.ComponentType<TComponentProps>; // typed props
  componentProps?: Omit<TComponentProps, keyof FieldComponentProps<TValue>>; // Additional props excluding base ones
  placeholder?: string;
  disabled?: boolean;
  hidden?: boolean;
};

export type FieldGroup = {
  title?: string; // Optional section header
  description?: string; // Optional tooltip/help
  fields: FieldConfig<unknown>[];
  hideSubmit?: boolean;
};
