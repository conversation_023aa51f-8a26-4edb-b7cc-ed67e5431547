import React, { ReactElement } from 'react';
import { Modal } from 'antd';

type FiltersModalProps = {
  open: boolean;
  onClose: () => void;
  footer: ReactElement[];
  children: React.ReactNode;
};

const FiltersModal = ({ open, onClose, footer, children }: FiltersModalProps) => {
  return (
    <Modal open={open} onCancel={onClose} footer={footer}>
      {children}
    </Modal>
  );
};

export default FiltersModal;
