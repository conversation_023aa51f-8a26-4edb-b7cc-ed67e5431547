import { ApiClient } from '@api/api-configuration';
import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { CreateDoctor } from './CreateDoctor';

export const EditDoctor = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /doctors/:id/edit
  const [data, setData] = useState<Doctor>();
  const { openNotification } = useNotifications();

  useEffect(() => {
    const fetchDoctor = async () => {
      try {
        if (id) {
          const response = await ApiClient.doctorsApi.doctors.getSingleDoctorDoctorsDoctorIdGet(Number(id));
          setData(response.data);
        }
      } catch (err) {
        openNotification('topRight', {
          title: `Doctor`,
          description: 'Failed to fetch doctor',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);

    fetchDoctor();
  }, [id, openNotification, state]);

  const doctorData = state || data;

  if (!doctorData) return <div>Loading...</div>;
  return <CreateDoctor data={doctorData} />;
};
