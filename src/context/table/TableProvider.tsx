/* eslint-disable react-hooks/exhaustive-deps */
import { getStoredColumns } from '@app/features/table/TableColumnsList/helpers';
import { ExtendedTableColumnType, PageResultsOption } from '@app/types/table.types';
import { isNullOrUndefined } from '@app/utils/isNullOrUndefined';
import { parseSorting, SortObject, stringifySorting } from '@app/utils/parseSorting';
import { useUrlParams } from '@context/urlParams/UrlParamProvider';
import { TableProps } from 'antd';
import { FilterValue, TablePaginationConfig } from 'antd/es/table/interface';
import { isEqual } from 'lodash';
import React, { createContext, ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { AnyObject } from 'yup';

export interface TableFilters {
  [key: string]: string | number | boolean | undefined | null;
}

export interface TableFilter {
  Id: number;
  label: string;
  value: boolean;
}

export type TableParams = Partial<{
  pageIndex: number;
  search: string;
  showArchived: boolean;
  pageSize: number;
  sorting: string | undefined;
}>;

interface TableContextType {
  filters: TableFilters;
  setFilter: (key: string, value: string | number | boolean | undefined | null) => void;
  setFilters: (filters: TableFilters) => void;
  filterStates: TableFilter[];
  setFilterStates: React.Dispatch<React.SetStateAction<TableFilter[]>>;
  initialFilters: TableFilters;
  initialStatusFilters: TableFilter[];
  handleTableChange: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    sorter: any,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    extra: any
  ) => void;
  columns: ExtendedTableColumnType<unknown>[];
  setColumns: React.Dispatch<React.SetStateAction<ExtendedTableColumnType<unknown | undefined>[]>>;
  handleColumnsCustomisation: (d: ExtendedTableColumnType<unknown>[]) => void;
  data?: Array<
    AnyObject & {
      LeaveBalance?: { Employee: { Person: { Id: number } } };
      Request?: { LeaveBalance?: { Employee: { Person: { Id: number } } } };
    }
  >;
}

const TableContext = createContext<TableContextType | undefined>(undefined);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const TableProvider = <T extends Record<string, any>>({
  children,
  initialFilters,
  storageKey,
  storageKeyColumns,
  initialStatusFilters = [],
  totalSizeData = 0,
  pageSize = 25,
  fetchData,
  cols = [],
}: {
  children: ReactNode;
  initialFilters: T;
  storageKey: string;
  storageKeyColumns: string;
  initialStatusFilters?: TableFilter[];
  totalSizeData?: number;
  pageSize?: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cols: any[];
  fetchData?: (params: Record<string, string>) => void;
}) => {
  const { params: urlParams, setParams } = useUrlParams();
  const tableParams = useRef<{ pageSize: number; pageIndex: number; sorting: string }>({
    pageSize,
    pageIndex: 0,
    sorting: initialFilters.sorting ?? '',
  });
  const [columns, setColumns] = useState<ExtendedTableColumnType<unknown>[]>(cols);
  const [filters, setFiltersState] = useState<TableFilters>(getInitialFilters);
  const [filterStates, setFilterStates] = useState<TableFilter[]>(initialStatusFilters);
  const hasInitialized = useRef(false);
  function getInitialFilters() {
    if (Object.keys(urlParams).length > 0) {
      console.log('[TABLE] [GET INITIAL FILTERS] Using URL params:', urlParams);
      return { ...initialFilters, ...urlParams };
    }
    try {
      const stored = localStorage.getItem(`tableParams-${storageKey}`);
      if (stored) {
        console.log('[TABLE] [GET INITIAL FILTERS] Using stored params:', JSON.parse(stored));
        return { ...initialFilters, ...JSON.parse(stored) };
      }
    } catch {
      console.warn('[TABLE] [GET INITIAL FILTERS] Failed to parse stored params');
    }
    console.log('[TABLE] [GET INITIAL FILTERS] Using default initial filters');
    return initialFilters;
  }

  useEffect(() => {
    const prevColumns = getStoredColumns<T>(storageKeyColumns);
    const newColumnMap = new Map(columns.map((col) => [col.key, col]));

    //remove missing cols and update previous cols that exists in new columns
    const updatedCols: ExtendedTableColumnType<unknown>[] = prevColumns
      .filter((prevCol) => newColumnMap.has(prevCol.key))
      .map((prevCol) => {
        const newCol = newColumnMap.get(prevCol.key)!;
        const hidden = !isNullOrUndefined(prevCol.hidden) ? prevCol.hidden : newCol.hidden;
        return { ...newCol, hidden };
      });

    //append new columns

    const prevKeys = new Set(prevColumns.map((col) => col.key));
    const appendedCols = columns
      .filter((col) => !prevKeys.has(col.key))
      .map((col) => ({ ...col, hidden: typeof col.hidden === 'undefined' ? false : col.hidden }));

    const finalCols = [...updatedCols, ...appendedCols];

    setColumns(finalCols);
    console.log('Columns table', prevColumns, cols);
  }, [storageKeyColumns]);

  useEffect(() => {
    const sorting = urlParams.sorting ? parseSorting(urlParams.sorting) : [];

    setColumns((cols) =>
      cols.map((col) => {
        const sortItem = sorting.find((s) => s.key === col.key);
        return {
          ...col,
          sortOrder: sortItem ? (sortItem.value === 'asc' ? 'ascend' : 'descend') : undefined,
        };
      })
    );
    console.log('Columns 1', cols);
  }, [urlParams.sorting]);

  useEffect(() => {
    try {
      const stored = localStorage.getItem(`tableParams-${storageKey}`);
      console.log('[TABLE]', { stored });
      if (stored) {
        //TODO: Compare cahced params with developer compoennt params
        console.log('[TABLE] stored value');

        const parsed = JSON.parse(stored);
        // Check if parsed object is empty
        if (Object.keys(parsed).length > 0) {
          setParams({ ...parsed }, { isTableParam: true });
        } else {
          console.log('[TABLE] stored value is empty, using defaults');
          const initial = { ...initialFilters, ...tableParams.current };
          console.log('[TABLE] not stored value initial filters', initial);
          setParams(initial, { isTableParam: true });
        }
      } else {
        console.log('[TABLE] not stored value');
        const initial = { ...initialFilters, ...tableParams.current };
        console.log('[TABLE] not stored value initial filters', initial);
        setParams(initial, { isTableParam: true });
      }
      hasInitialized.current = true;
    } catch {
      console.warn('[TABLE] [USE EFFECT INIT] Cannot initialise table');
    }
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const clean = (obj: Record<string, any>) => {
    return Object.fromEntries(
      Object.entries(obj).filter(([k, v]) => {
        if (v === undefined || v === null || v === '') return false;

        // Special case: searchTerm must be > 2 chars
        if (k === 'searchTerm' && typeof v === 'string' && v.length <= 2) {
          return false;
        }

        return true;
      })
    );
  };
  useEffect(() => {
    const filtered = clean(filters);
    const urlFiltered = clean(urlParams);
    const merged = { ...urlFiltered, ...filtered };
    localStorage.setItem(`tableParams-${storageKey}`, JSON.stringify(merged));
  }, [filters, storageKey, urlParams]);
  console.log('[PARAMS 3]', urlParams);
  useEffect(() => {
    // const filtersAsStrings = Object.fromEntries(
    //   Object.entries(filters).map(([k, v]) => [k, v == null ? '' : String(v)])
    // );
    // const urlParamsAsStrings = Object.fromEntries(
    //   Object.entries(urlParams).map(([k, v]) => [k, v == null ? '' : String(v)])
    // );

    const merged = { ...initialFilters, ...urlParams };

    setFiltersState(merged);
    if (fetchData && Object.keys(urlParams).length > 0) {
      fetchData(clean(urlParams));
    }
  }, [urlParams, initialFilters]);

  const setFilter = useCallback((key: string, value: string | number | boolean | undefined | null) => {
    console.log('[TABLE] [SET FILTER]', key, '=', value);
    setFiltersState((prev) => ({ ...prev, [key]: value }));
  }, []);

  const setFilters = useCallback((newFilters: TableFilters) => {
    console.log('[TABLE] [SET FILTERS]', newFilters);
    setFiltersState(newFilters);
  }, []);

  const handleColumnsCustomisation = useCallback((col: ExtendedTableColumnType<unknown>[]) => {
    setColumns(col);
    localStorage.setItem(storageKeyColumns, JSON.stringify(col));
  }, []);

  const handlePaginate = (action: 'next' | 'prev' | 'first' | 'last' | number, val?: SortObject[] | []) => {
    const { pageIndex, pageSize } = tableParams.current;
    let goTo: number;
    console.log('[TABLE] [PAGINATE]', action);
    switch (action) {
      case 'next':
        goTo = Math.min(pageIndex + 1, totalSizeData);
        break;
      case 'prev':
        goTo = Math.max(pageIndex - 1, 0);
        break;
      case 'first':
        goTo = 1;
        break;
      case 'last':
        goTo = Math.floor(totalSizeData / Number(pageSize));
        break;
      default:
        goTo = action;
        break;
    }
    console.log('[TABLE] [PAGINATE] Navigating to page:', goTo);
    setParams({ pageIndex: goTo, sorting: val?.join(','), pageSize: pageSize } as unknown as Partial<T>, {
      isTableParam: true,
    });
  };

  const handleChangePageSize = (e: PageResultsOption) => {
    console.log('[TABLE] [CHANGE PAGE SIZE]', e);
    setParams({ pageSize: e.value } as unknown as Partial<T>, { isTableParam: true });
  };

  const handleTableChange: TableProps['onChange'] = (pagination, _, sorter) => {
    // Normalize sorter into array form
    const sorterArray = Array.isArray(sorter) ? sorter : sorter?.columnKey ? [sorter] : [];

    console.log('[TABLE] RAW SORTER:', sorter);
    console.log('[TABLE] NORMALIZED SORTER ARRAY:', sorterArray);

    const newSorts: SortObject[] = sorterArray
      .filter((s) => s.order === 'ascend' || s.order === 'descend')
      .map((s) => ({
        key: String(s.columnKey),
        value: s.order === 'ascend' ? 'asc' : 'desc',
      }));

    const newSortingString = stringifySorting(newSorts);
    const oldSortingString = tableParams.current.sorting;

    console.log('[SORTING] OLD:', oldSortingString);
    console.log('[SORTING] NEW:', newSortingString);
    console.log('[COMPARE] isEqual:', isEqual(parseSorting(oldSortingString), newSorts));

    // Only update if changed
    tableParams.current.sorting = newSortingString;
    setParams({ sorting: newSortingString } as unknown as Partial<T>, { isTableParam: true });

    // Pagination: update page index if changed
    if (pagination.current && pagination.current - 1 !== tableParams.current.pageIndex) {
      tableParams.current.pageIndex = pagination.current - 1;
      handlePaginate(tableParams.current.pageIndex, newSorts);
    }

    // Page size: update if changed
    if (pagination.pageSize && pagination.pageSize !== tableParams.current.pageSize) {
      tableParams.current.pageSize = pagination.pageSize;
      handleChangePageSize({
        label: String(pagination.pageSize),
        value: pagination.pageSize,
      });
    }

    console.log('[TABLE PARAMS]', tableParams.current);
  };

  return (
    <TableContext.Provider
      value={{
        filters,
        filterStates,
        initialFilters,
        initialStatusFilters,
        columns,
        setFilter,
        setFilters,
        setFilterStates,
        handleColumnsCustomisation,
        handleTableChange,
        setColumns,
      }}
    >
      {children}
    </TableContext.Provider>
  );
};

export function useTable() {
  const context = useContext(TableContext);
  if (!context) {
    throw new Error('useTable must be used within a TableProvider');
  }
  return context;
}
