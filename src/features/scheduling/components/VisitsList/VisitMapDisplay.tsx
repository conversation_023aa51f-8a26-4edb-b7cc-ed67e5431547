import { Client } from '@api/READ_ONLY/client_api/Api';

type LocationUtilsProps = {
  client: Client | number | undefined;
};

/**
 * Extracts latitude and longitude from client data
 * @param client - Client object or ID
 * @returns Coordinates array [lat, lng] or null if not available
 */
export function getLatLngFromClient(client: Client | number | undefined): [number, number] | null {
  if (!client || typeof client === 'number') return null;

  const { geoLat, geoLng } = client;
  if (typeof geoLat === 'number' && typeof geoLng === 'number') {
    return [geoLat, geoLng];
  }
  return null;
}

/**
 * Component that renders a Google Maps iframe
 */
export function VisitMapDisplay({ client }: LocationUtilsProps) {
  const coordinates = getLatLngFromClient(client);
  console.log({ client, coordinates });
  if (!coordinates) return null;

  const [lat, lng] = coordinates;

  return (
    <div className="my-3">
      <div
        className="mt-2 text-app-primary-600 underline text-sm"
        onClick={(e) => {
          e.stopPropagation();
          window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
        }}
      >
        <iframe
          className="w-full h-40 rounded-md border pointer-events-none"
          loading="lazy"
          src={`https://maps.google.com/maps?q=${lat},${lng}&z=15&output=embed`}
          title="Χάρτης"
        />
      </div>
    </div>
  );
}
