/**
 * RecursiveMenu Module
 *
 * A modular and maintainable navigation menu component system
 * with performance optimizations and TypeScript support.
 */

// Main component
export { default } from './RecursiveMenu';

// Sub-components (for individual use if needed)
export { CollapsedMenuItem } from './CollapsedMenuItem';
export { ExpandedMenuItem } from './ExpandedMenuItem';
export { MenuIcon } from './MenuIcon';

// Types and utilities (for external customization)
export { useMenuState, useNavigation } from './hooks';
export type { ExpandedMenuItemProps, MenuItem, MenuItemProps, MenuProps } from './types';
export { createDropdownItems, getLeftClass, getMenuItemClasses, getPaddingLeftClass, isChildActive } from './utils';
