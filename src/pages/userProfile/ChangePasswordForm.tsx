import PasswordToggle from '@app/components/ui/PasswordToggle';
import { Input, Typography } from 'antd';
import { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

const { Text } = Typography;

export type ChangePasswordFormValues = {
  old_password: string;
  new_password: string;
  confirm_password: string;
};

const ChangePasswordForm = () => {
  const { control } = useFormContext<ChangePasswordFormValues>();
  const [passwordVisibility, setPasswordVisibility] = useState({
    old_password: false,
    new_password: false,
    confirm_password: false,
  });

  const handlePasswordToggle = (field: keyof ChangePasswordFormValues, visible: boolean) => {
    setPasswordVisibility((prev) => ({ ...prev, [field]: visible }));
  };

  return (
    <div className="flex flex-col justify-center items-center gap-2 ">
      <div className="flex flex-col gap-8 w-1/2">
        {(['old_password', 'new_password', 'confirm_password'] as const).map((field) => (
          <Controller
            key={field}
            name={field}
            control={control}
            render={({ field: controllerField }) => (
              <Input
                {...controllerField}
                placeholder={
                  field === 'old_password'
                    ? 'Current Password'
                    : field === 'new_password'
                      ? 'New Password'
                      : 'Confirm New Password'
                }
                type={passwordVisibility[field] ? 'text' : 'password'}
                suffix={<PasswordToggle onToggle={(visible) => handlePasswordToggle(field, visible)} />}
              />
            )}
          />
        ))}
      </div>

      <div className="flex flex-col mt-4 w-1/2">
        <Text style={{ color: 'rgba(58, 53, 65, 0.6)' }}>Password Requirements</Text>
        <Text style={{ color: 'rgba(58, 53, 65, 0.6)' }}>
          <ul style={{ gap: 4 }}>
            <li>Minimum 8 characters long - the more, the better</li>
            <li>At least one lowercase & one uppercase character</li>
            <li>At least one number & one special character</li>
          </ul>
        </Text>
      </div>
    </div>
  );
};

export default ChangePasswordForm;
