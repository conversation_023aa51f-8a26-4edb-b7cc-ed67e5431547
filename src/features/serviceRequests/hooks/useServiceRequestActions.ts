/* eslint-disable @typescript-eslint/no-unused-vars */
import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import { ServiceRequestCreateReq, ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import { VisitCreate } from '@api/READ_ONLY/visits_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { DateTimeRecurrenceValues, ServiceRequestExtraDetails } from '@feat-service-requests/types';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import { Dayjs } from 'dayjs';

interface UseCreateServiceRequestProps {
  patient: Client | undefined;
  notes: string;
  dateTimeRecurrenceValues: DateTimeRecurrenceValues;
  isValidToCreateANewAAServiceRequest: boolean;
  resetAllValuesToDefault: () => void;
  serviceRequestExtraDetails: ServiceRequestExtraDetails;
  rruleString: string;
}

// The whole useServiceRequestActions.ts file needs to change it does to much.

// ALSO leave for now those combine and DateAndTime functions and the creation of the rrule it can be improved. For now make it work as expected

const combineDateAndTime = ({
  startingDate,
  endingDate,
  fromTime,
  toTime,
}: {
  startingDate: Dayjs;
  endingDate: Dayjs | null;
  fromTime: Dayjs;
  toTime: Dayjs;
}) => {
  if (!startingDate) return {};

  if (!endingDate)
    return {
      fromDate: startingDate.hour(fromTime.hour()).minute(fromTime.minute()).second(0).millisecond(0),
      toDate: toTime ? startingDate.hour(toTime.hour()).minute(toTime.minute()).second(0).millisecond(0) : undefined,
    };

  return {
    fromDate: startingDate.hour(fromTime.hour()).minute(fromTime.minute()).second(0).millisecond(0),
    toDate: endingDate.hour(toTime.hour()).minute(toTime.minute()).second(0).millisecond(0),
  };
};

export const useCreateServiceRequest = ({
  dateTimeRecurrenceValues,
  patient,
  notes,
  isValidToCreateANewAAServiceRequest,
  serviceRequestExtraDetails,
  resetAllValuesToDefault,
  rruleString,
}: UseCreateServiceRequestProps) => {
  const { openNotification } = useNotifications();

  const createNewVisitsRequestMutation = useMutation({
    mutationFn: ({ payload }: { payload: VisitCreate }) => ApiClient.visitsApi.visits.createVisitVisitsPost(payload),
    onSuccess: () => {
      openNotification('topRight', {
        title: `Service Request`,
        description: 'Visit created successfully. ',
        type: 'Success',
      });

      resetAllValuesToDefault();
    },
    onError: (error) => {
      console.error(error);
    },
  });

  const createNewServicesRequestMutation = useMutation({
    mutationFn: ({ payload }: { payload: ServiceRequestCreateReq }) =>
      ApiClient.serviceRequestsApi.serviceRequests.createRequestServiceRequestsPost(payload),
    onSuccess: (res: AxiosResponse<ServiceRequestResponse>) => {
      const { data } = res;

      // console.log({ data });
      if (!patient) return;

      /**
       * TODO: the address need to be changed with proprity. If the user selecs and address in service request form then we need to send this address here
       * else we use the patient's address
       */
      const { street, city, postalCode } = patient;

      createNewVisitsRequestMutation.mutate({
        payload: {
          clientId: data.client.clientId,
          startTime: data.fromDate,
          endTime: data.toDate,
          serviceRequestId: data.serviceRequestId,
          notes: notes.trim() ?? '',
          street: street || '',
          city: city || '',
          postalCode: postalCode || '',
        },
      });
    },
    onError: (error) => {
      console.error(error);
    },
  });

  // It sucks can be greatly improved
  const handleOnCreateButtonClick = () => {
    // No patientId is selected or the form is not valid
    if (!isValidToCreateANewAAServiceRequest || !patient?.clientId) return;

    if (
      !dateTimeRecurrenceValues.startingDate ||
      !dateTimeRecurrenceValues.fromTime ||
      !dateTimeRecurrenceValues.toTime
    )
      return;

    const { fromDate, toDate } = combineDateAndTime({
      startingDate: dateTimeRecurrenceValues.startingDate,
      endingDate: dateTimeRecurrenceValues.endingDate,
      fromTime: dateTimeRecurrenceValues.fromTime,
      toTime: dateTimeRecurrenceValues.toTime,
    });

    createNewServicesRequestMutation.mutate({
      payload: {
        clientId: patient.clientId,
        serviceIds: serviceRequestExtraDetails.services.map((s) => s.id),
        fromDate: fromDate?.format('YYYY-MM-DDTHH:mm:ss') ?? '',
        toDate: toDate?.format('YYYY-MM-DDTHH:mm:ss') ?? '',
        notes: notes.trim() ?? '',
        rrule: rruleString,
      },
    });
  };

  return {
    actions: {
      handleOnCreateButtonClick,
    },
    isActionInProgress: createNewServicesRequestMutation.isPending || createNewVisitsRequestMutation.isPending,
  };
};
