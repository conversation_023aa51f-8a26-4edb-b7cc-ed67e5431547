import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

const firebaseConfig = {
  apiKey: 'AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8',
  authDomain: 'homecare-2498a.firebaseapp.com',
  projectId: 'homecare-2498a',
  storageBucket: 'homecare-2498a.firebasestorage.app',
  messagingSenderId: '1063363203978',
  appId: '1:1063363203978:web:ee1a9263e338d61fa6ae73',
  measurementId: 'G-69C67NLG31',
};

const firebaseApp = initializeApp(firebaseConfig);
const messaging = getMessaging(firebaseApp);

export const getOrRegisterServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    const serviceWorker = await window.navigator.serviceWorker.getRegistration('/firebase-push-notification-scope');
    if (serviceWorker) return serviceWorker;
    return window.navigator.serviceWorker.register('/firebase-messaging-sw.js', {
      scope: '/firebase-push-notification-scope',
    });
  }
  throw new Error('The browser doesn`t support service worker.');
};

export const getFirebaseToken = () =>
  getOrRegisterServiceWorker().then((serviceWorkerRegistration) =>
    getToken(messaging, {
      vapidKey: 'BBjgIqTaqEawNM2_WxLcz5XC8z_U2zmnz8e1PsXl-fTXFf18OpToxYvonFVt81beldq1LDw0rOYGzPTWdyerOYk',
      serviceWorkerRegistration,
    })
  );

export const onForegroundMessage = () =>
  new Promise((resolve, reject) => {
    onMessage(messaging, (payload) => {
      console.log('Foreground notification received:', payload);
      if (payload?.notification) {
        resolve(payload);
      } else {
        reject('No notification payload received.');
      }
    });
  });
