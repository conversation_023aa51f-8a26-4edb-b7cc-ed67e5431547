import { useAuthContext } from '@context/auth/useAuthContext';
import useUiContext from '@context/ui/useUiContext';
import React from 'react';
import { FiLogOut } from 'react-icons/fi';
import UserDropdown from '../../UserDropdown';

const BottomAvatar: React.FC = () => {
  const { isSidebarCollapsed } = useUiContext();
  const { user } = useAuthContext();
  const { logout } = useAuthContext();

  return (
    <div className="w-full px-4 py-2 flex items-center justify-between bg-app-gray-light hover:bg-app-gray-2 transition">
      {/* Left: Avatar + Text */}
      <div className="flex items-center gap-3 overflow-hidden">
        <UserDropdown />
        {/* Only show name/email if sidebar is not collapsed */}
        {!isSidebarCollapsed && (
          <div className="flex flex-col overflow-hidden">
            <span className="text-sm font-semibold text-gray-800 truncate">{user.fullname}</span>
            <span className="text-xs text-gray-500 truncate">{user.email}</span>
          </div>
        )}
      </div>

      {/* Right: Logout Icon */}
      <button
        className="text-gray-400 hover:text-app-primary transition ml-2 border-0"
        onClick={() => {
          logout();
        }}
        title="Sign out"
      >
        <FiLogOut size={18} />
      </button>
    </div>
  );
};

export default BottomAvatar;
