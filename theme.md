# Theming Guide

## Official Color Palette

Our app uses a consistent color palette to ensure visual harmony and a polished user experience. Below are the main colors and their roles:

## Color Name Usage Hex Code

Primary Main interactive elements, buttons, links `#6253e1`
Secondary Accent elements, highlights `#04befe`
Background (Light) Page backgrounds, containers `#F5F5F5`
Background (Alt) Alternative backgrounds, cards `#F1F2F7`
Text (Primary) Main body text `#1f1f1f`
Text (Secondary) Placeholder text, muted text `#7B7B7B`
Text (Heading) Headings, important labels `#333333`
Alert / Error Alerts, errors (defined separately) TBD
Borders Dividers, borders `#E0E0E0` (soft), `#D9D9D9` (divider)

## How We Configure Theming

Tailwind CSS Theme Configuration
We extend the default Tailwind theme by configuring CSS variables that use Tailwind’s default color tokens, allowing us to keep consistency and leverage Tailwind’s utility classes:

styles.css

```
@import 'tailwindcss';
@import 'simplebar-react/dist/simplebar.min.css';

@theme {
 colors.)
  --color-app-gray-1: var(--color-neutral-100);
  --color-app-gray-2: var(--color-neutral-200);

  --color-app-primary: #6253e1;
}
```

## Mixing Colors

This CSS snippet demonstrates how to generate a dynamic color palette based on a single primary color using the color-mix() function in the srgb color space. By blending the base color (--color-app-primary) with white and black at varying intensities, we create a smooth scale of tints (lighter shades) and shades (darker tones).

How It Works
Tints (100–400): These are created by mixing the primary color with increasing amounts of white. Lower percentages of the base color result in lighter hues.

Base (500): The original primary color, unchanged.

Shades (600–950): These are formed by mixing the primary color with black in increasing amounts, making the color progressively darker.

This dynamic system allows for easy theming and color consistency across your app by defining only one primary color. You can then derive the rest of the palette automatically, making your design more maintainable and adaptable to different themes (e.g., dark mode, brand customization).

```
  --color-app-primary-100: color-mix(in srgb, var(--color-app-primary) 10%, white 90%);
  --color-app-primary-100: color-mix(in srgb, var(--color-app-primary) 20%, white 80%);
  --color-app-primary-200: color-mix(in srgb, var(--color-app-primary) 40%, white 60%);
  --color-app-primary-300: color-mix(in srgb, var(--color-app-primary) 60%, white 40%);
  --color-app-primary-400: color-mix(in srgb, var(--color-app-primary) 80%, white 20%);
  --color-app-primary-500: var(--color-app-primary); /* Base shade */
  --color-app-primary-600: color-mix(in srgb, var(--color-app-primary) 80%, black 20%);
  --color-app-primary-700: color-mix(in srgb, var(--color-app-primary) 60%, black 40%);
  --color-app-primary-800: color-mix(in srgb, var(--color-app-primary) 40%, black 60%);
  --color-app-primary-900: color-mix(in srgb, var(--color-app-primary) 20%, black 80%);
  --color-app-primary-950: color-mix(in srgb, var(--color-app-primary) 10%, black 90%);
```

We create a new color called app-gray-1 can be used for `bg-app-gray-light`, `border-app-gray-1` and -2 etc. and we assigned the tailwind `neutral-100` and `neutral-200` accordingly on it `--color-neutral-100` and `--color-neutral-200` are Tailwind’s color tokens

We create a new color called app-primary can be used for `bg-app-primary` `border-app-primary` and we assigned the tailwind `neutral-100` and `neutral-200` accordingly on it

We map these to our app-specific CSS variables (e.g., --color-app-gray-1) so that styles throughout the app can reference these variables.

Custom colors like `--color-app-primary` are defined explicitly.

you can learn more about tailwind theming here color are not that can be themed. you can create custom size variables and more

##### learn more here

https://tailwindcss.com/docs/theme

##### Using tailwind directives and functions

https://tailwindcss.com/docs/functions-and-directives

css

```
.select2-dropdown {
  @apply rounded-b-lg shadow-md; // use @apply to apply tailwind classes to a new class
}
```

## Ant Design (antd) Theme Object

We customize Ant Design components by overriding the ThemeConfig tokens and component-level styles:

ts

```
import { ThemeConfig } from 'antd';

export const appTheme: ThemeConfig = {
// the global tokens
  token: {
    colorIcon: '#E0E0E0',
    colorText: '#000000',
    colorPrimary: '#6253e1',
    colorBgContainer: '#F5F5F5',
    fontFamily: `'Josefin Sans', sans-serif`,
  },
  // component specific tokens
  components: {
  // themed button example

     Button: {
       colorPrimary: 'var(--color-app-primary)', //here we use the previousy created tailwind color
       colorText: '#7B7B7B', // here we use a hard coded color try to avoid this unless is specific only for this component
       textHoverBg: 'var(--color-app-danger)', // here we use tailwind default color token red 500
       colorBgContainer: 'transparent', // css colors red green transparent
     },
    // Other component overrides...
  },
};
```

This ensures Ant Design UI components visually integrate with our app’s design language.

## Guidelines for Selecting or Defining New Colors

Reference Existing Palette: Always try to use or derive colors from the existing palette to maintain consistency.

Use Semantic Naming: Define new colors with clear, purpose-driven names (e.g., `--color-app-success`, `--color-app-warning`).

Leverage Tailwind Tokens: If you need variations of gray or other neutrals, use Tailwind’s default variables (`--color-neutral-xxx`).

Check Contrast & Accessibility: Ensure that new colors meet accessibility standards (contrast ratio ≥ 4.5:1 for text).

Update Theme Files: Add new colors to both Tailwind CSS variables and the Ant Design theme object if they affect components.

## How to Apply Theming When Building a New Page

##### 1. Use Predefined CSS Variables

Reference the CSS variables in your styles or inline styles:

css

```
background-color: var(--color-app-primary);
color: var(--color-app-gray-1);
```

##### 2. Use Ant Design Themed Components

When using Ant Design components, the global appTheme configuration applies automatically if properly setup with your ConfigProvider:

tsx

```
import { ConfigProvider } from 'antd';
import { appTheme } from './path-to-theme';

<ConfigProvider theme={appTheme}>
  <YourPageComponent />
</ConfigProvider>
```

This will apply the customized tokens to all Ant Design components.

##### 3. Use Custom Styled Components / Hooks

If you need special styles, use the provided useStyle hook or create your own to ensure styling adheres to theming:

ts

```
import { createStyles } from 'antd-style';

export const useStyle = createStyles(({ prefixCls, css }) => ({
  buttonGradient: css`
    background: linear-gradient(135deg, #6253e1, #04befe);
    color: white;
  `,
}));
```

##### 4. Avoid Hardcoding Colors

Do not hardcode hex colors directly in components. Always use the defined variables or theme tokens to maintain consistency.

## Summary

- Use the official color palette defined in Tailwind CSS variables for neutral colors
- Use hex color codes for brand colors (the ones that don't exist)
- Define new colors based on existing palette and accessibility.
- Use CSS variables and Ant Design theme tokens consistently.
- Wrap your app or pages in `ConfigProvider` with appTheme.
- Use the useStyle hook or similar abstractions for custom styles.
- Always document any additions or changes to theming here.
- Avoid creating new classes and oveririding antd classes use antd theme object
- Avoid dynamic tailwind classes `bg-[#4f5ff6]`, `p-[5px]`, use the nearest like `p-1`

## Tools

find the nearest tailwind color
https://nearest-tailwind-color.netlify.app/
https://find-nearest-tailwind-colour.netlify.app/
