import { ApiClient } from '@api/api-configuration';
import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import ServiceRequestTable from '@feat-serviceRequests/components/ServiceRequestTable/ServiceRequestTable';
import { getServiceRequestTableColumns } from '@feat-serviceRequests/components/ServiceRequestTable/serviceRequestTableColumns.config';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};

const filters = [
  { Id: 0, label: 'Pending', value: false },
  { Id: 1, label: 'Approved', value: false },
  { Id: 2, label: 'Rejected', value: false },
  { Id: 3, label: 'In Progress', value: false },
  { Id: 4, label: 'Completed', value: false },
];

export const ServiceRequestsPage = () => {
  const columns: () => ExtendedTableColumnType<ServiceRequestResponse>[] = useCallback(
    () => getServiceRequestTableColumns(),
    []
  );
  const setServiceRequests = useSchedulingStore((state) => state.setServiceRequests);
  const setLoading = useSchedulingStore((state) => state.setLoadingServiceRequests);

  return (
    <TableProvider
      storageKey="serviceRequestTable"
      storageKeyColumns="serviceRequestTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={filters}
      fetchData={async ({ searchTerm }) => {
        setLoading(true);
        try {
          const response =
            await ApiClient.serviceRequestsApi.serviceRequests.searchServiceRequestsServiceRequestsSearchGet({
              query: searchTerm,
            });
          setServiceRequests(response.data.data);
        } catch (error) {
          console.error('Error fetching service requests:', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <ServiceRequestTable />
    </TableProvider>
  );
};
