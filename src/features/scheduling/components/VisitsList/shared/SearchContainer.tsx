// import { ForwardedRef, JSX } from 'react';

// type SearchParams<F> = {
//   searchText: string;
//   filters: F;
// };
// type FiltersComponentProps<F> = {
//   filters: F;
//   setFilters: React.Dispatch<React.SetStateAction<F>>;
// };

// type SearchContainerProps<F> = {
//   title: JSX.Element;
//   placeholder: string;
//   fetchData?: (params: SearchParams<F>) => void;
//   FiltersComponent?: React.ComponentType<FiltersComponentProps<F>>;
//   ref?: ForwardedRef<HTMLDivElement>;
// };

const SearchContainer = () => {
  // const SearchContainer = <F,>({ title, placeholder, fetchData, FiltersComponent, ref }: SearchContainerProps<F>) => {

  // const [searchText, setSearchText] = useState('');
  // const [filters, setFilters] = useState<F>(() => ({}) as F);
  // const [openModal, setOpenModal] = useState(false);

  // const debouncedFetch = useMemo(() => {
  //   return debounce((params: SearchParams<F>) => {
  //     if (fetchData) fetchData(params);
  //   }, 500);
  // }, [fetchData]);

  // useEffect(() => {
  //   debouncedFetch({ searchText, filters });
  //   return () => debouncedFetch.cancel();
  // }, [searchText, filters]);

  // const handleOpenFilters = () => {
  //   setOpenModal(!openModal);
  // };

  // return (
  //   <div ref={ref}>
  //     <SearchFilter
  //       searchText={searchText}
  //       setSearchText={() => {}}
  //       onOpenFilters={FiltersComponent && handleOpenFilters}
  //       title={title}
  //       placeholder={placeholder}
  //     />
  //     {/* {FiltersComponent && (
  //       <FiltersModal open={openModal} onClose={handleOpenFilters} footer={[]}>
  //         <FiltersComponent filters={filters} setFilters={setFilters} />
  //       </FiltersModal>
  //     )} */}
  //   </div>
  // );
  return <></>;
};

export default SearchContainer;
