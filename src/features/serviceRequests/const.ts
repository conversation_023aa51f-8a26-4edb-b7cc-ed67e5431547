/* Repetition events represents the repeat dropdown. display values.
 the only reason this is here is because the outer daily and weekly do not have interval.
*/
export const REPETITION_EVENTS = {
  NEVER: 'never',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  // CUSTOM: 'custom',
} as const;

/*
Frequency represents the frequency dropdown depending on the selected interval.
 For example if repetition event is CUSTOM and frequency is daily, with an interval of 2, it will repeat every 2 days.
 If repetition event is CUSTOM and frequency is weekly, with an interval of 2, it will repeat every 2 weeks. for the selected week days
*/
export const FREQUENCY_OPTIONS = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
} as const;
