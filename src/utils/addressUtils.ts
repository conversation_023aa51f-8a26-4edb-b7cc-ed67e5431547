/**
 * Address formatting utilities for consistent address display
 */

export interface AddressInfo {
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

/**
 * Formats an address object into a readable string
 * @param address - Address object with street, city, state, zip, country
 * @returns Formatted address string or '-' if no address data
 */
export const formatAddress = (address?: AddressInfo): string => {
  if (!address) return '-';

  const { street, city, state, zip, country } = address;

  // Return '-' if no address components are available
  if (!street && !city && !state && !zip && !country) {
    return '-';
  }

  const addressParts: string[] = [];

  // Add street if available
  if (street) {
    addressParts.push(street);
  }

  // Add city, state, zip combination
  const locationParts: string[] = [];
  if (city) locationParts.push(city);
  if (state) locationParts.push(state);
  if (zip) locationParts.push(zip);

  if (locationParts.length > 0) {
    addressParts.push(locationParts.join(' '));
  }

  // Add country if available and different from default
  if (country && country.toLowerCase() !== 'usa' && country.toLowerCase() !== 'us') {
    addressParts.push(country);
  }

  return addressParts.length > 0 ? addressParts.join(', ') : '-';
};

/**
 * Gets address information with fallback logic
 * Tries primary address first, then falls back to secondary address
 * @param primaryAddress - Primary address object
 * @param fallbackAddress - Fallback address object
 * @returns Best available address or '-'
 */
export const getAddressWithFallback = (primaryAddress?: AddressInfo, fallbackAddress?: AddressInfo): string => {
  // Try primary address first
  const primaryFormatted = formatAddress(primaryAddress);
  if (primaryFormatted !== '-') {
    return primaryFormatted;
  }

  // Fall back to secondary address
  return formatAddress(fallbackAddress);
};
