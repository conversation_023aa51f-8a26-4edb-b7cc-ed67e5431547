import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { lazy, Suspense } from 'react';

// Lazy Load UserProfile Component
const LazyUserProfile = lazy(() => import('../pages/userProfile/UserProfile'));

// Route Component
const EditProfileRoute = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Password Details">
      <LazyUserProfile />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  EditProfileRoute,
};
