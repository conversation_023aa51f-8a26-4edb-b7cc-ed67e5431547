import { TableColumnGroupType, TableColumnType } from 'antd';

export type TableParams = Partial<{
  pageIndex: number;
  search: string;
  showArchived: boolean;
  pageSize: number;
  sorting: string | undefined;
}>;
export type ExtendedTableColumnType<T> = (TableColumnType<T> | TableColumnGroupType<T>) & {
  default?: boolean;
  subKey?: string;
  type?: 'string' | 'number' | 'date' | 'boolean' | 'custom';
  transformer?: (params: unknown) => string;
  children?: ExtendedTableColumnType<T>[];
};
export type PageResultsOption = { label: string; value: number };
export type CommonListResponse<T = object> = {
  Results: T[];
  TotalResults: number;
};
