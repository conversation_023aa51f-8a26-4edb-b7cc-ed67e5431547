import { Caregiver, Service } from '@api/READ_ONLY/caregiver_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Typography } from 'antd';
import { TruncatedList } from '../../../../components/common/TruncatedList';

export type CaregiverTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: T[keyof T], record: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getCaregiverTableColumns = (): CaregiverTableColumnType<Caregiver>[] => [
  {
    key: 'Name',
    title: 'Name',
    width: 220,

    render: (_, caregiver) => (
      <ClientDisplay firstName={caregiver.firstName || ''} lastName={caregiver.lastName || ''} />
    ),
  },
  {
    dataIndex: 'dateOfBirth',
    key: 'dateOfBirth',
    title: 'Date of Birth',
    width: 150,
    align: 'center',
    render: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
    ),
  },
  // {
  //   dataIndex: 'gender',
  //   key: 'gender',
  //   title: 'Gender',
  //   width: 100,
  //   align: 'center',
  //   render: (gender) => <Typography>{typeof gender === 'string' ? gender : '-'}</Typography>,
  // },
  {
    dataIndex: 'phone',
    key: 'phone',
    title: 'Phone',
    width: 150,
    render: (phone) => <Typography>{(phone as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'email',
    key: 'email',
    title: 'Email',
    width: 200,
    render: (email) => <Typography>{(email as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'username',
    key: 'username',
    title: 'Username',
    width: 140,
    render: (username) => <Typography>{username as string}</Typography>,
  },

  // {
  //   dataIndex: ['baseAddress', 'street'],
  //   key: 'baseAddress.street',
  //   title: 'Street',
  //   width: 140,
  //   render: (_, record) => <Typography>{record??.street || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['baseAddress', 'city'],
  //   key: 'baseAddress.city',
  //   title: 'City',
  //   width: 120,
  //   render: (_, record) => <Typography>{record?.baseAddress?.city || '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'coverageAreas',
  //   key: 'coverageAreas',
  //   title: 'Coverage Areas',
  //   width: 180,
  //   render: (areas) =>
  //     Array.isArray(areas) && areas.length > 0
  //       ? areas.map((area) => <Tag key={area as Key}>{area as string}</Tag>)
  //       : '-',
  // },
  // {
  //   dataIndex: 'travelRadius',
  //   key: 'travelRadius',
  //   title: 'Travel Radius (km)',
  //   width: 160,
  //   align: 'center',
  //   render: (radius) => <Typography>{(radius as string) ?? '-'}</Typography>,
  // },
  {
    dataIndex: 'services',
    key: 'Services',
    title: 'Services',
    width: 250,
    render: (services) => (
      <TruncatedList
        items={(services as Service[]).map((service) => ({
          id: service.serviceId,
          name: service.name,
        }))}
        maxItems={3}
        separator=", "
      />
    ),
  },
  // {
  //   dataIndex: 'languagesSpoken',
  //   key: 'languagesSpoken',
  //   title: 'Languages',
  //   width: 160,
  //   render: (langs) =>
  //     Array.isArray(langs) && langs.length > 0 ? langs.map((l) => <Tag key={l as Key}>{l as string}</Tag>) : '-',
  // },
  // {
  //   dataIndex: 'rating',
  //   key: 'rating',
  //   title: 'Rating',
  //   width: 100,
  //   align: 'center',
  //   render: (rating) => <Typography>{typeof rating === 'number' ? rating.toFixed(1) : '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'active',
  //   key: 'active',
  //   title: 'Active',
  //   width: 80,
  //   align: 'center',
  //   render: (active) => <Checkbox checked={!!active} disabled />,
  // },
  // {
  //   dataIndex: 'createdAt',
  //   key: 'createdAt',
  //   title: 'Created At',
  //   width: 180,
  //   render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  // },
  // {
  //   dataIndex: 'updatedAt',
  //   key: 'updatedAt',
  //   title: 'Updated At',
  //   width: 180,
  //   render: (date) => (date ? new Date(date as string).toLocaleString() : '-'),
  // },
];
