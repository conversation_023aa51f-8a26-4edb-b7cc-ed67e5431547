/// <reference types="google.maps" />
import { Loader } from '@googlemaps/js-api-loader';

/**
 * Configure the loader once in your app.
 */
const loader = new Loader({
  //TODO: Make api key dynamic
  apiKey: 'AIzaSyB9L48t5hpYMifA6FuzsmhXjAyYuthUpwA',
  version: 'weekly',
  libraries: ['places', 'geocoding'],
});

/**
 * Keep one session token per user-search flow (billing best practice).
 * Call resetPlacesSession() after a user makes a final selection.
 */
let sessionToken: google.maps.places.AutocompleteSessionToken | null = null;

function ensureSessionToken() {
  if (!sessionToken) {
    sessionToken = new google.maps.places.AutocompleteSessionToken();
  }
  return sessionToken!;
}

export function resetPlacesSession() {
  sessionToken = null;
}

/**
 * Structured address components for easy form population
 */
export interface ParsedAddress {
  // Basic components
  street?: string; // Street number + route
  city?: string; // Locality or administrative_area_level_2
  state?: string; // Administrative_area_level_1
  country?: string; // Country
  postalCode?: string; // Postal code

  // Additional details
  streetNumber?: string; // Just the number
  route?: string; // Just the street name
  neighborhood?: string; // Sublocality

  // Original data
  formattedAddress: string;
  location: google.maps.LatLngLiteral;
  placeId: string;
  types: string[];

  // Validation flags
  hasStreetAddress: boolean;
  isCompleteAddress: boolean;
}

/**
 * Parse Google address components into structured data
 */
function parseAddressComponents(components: google.maps.GeocoderAddressComponent[]): Partial<ParsedAddress> {
  const parsed: Partial<ParsedAddress> = {};

  for (const component of components) {
    const types = component.types;
    const longName = component.long_name;

    if (types.includes('street_number')) {
      parsed.streetNumber = longName;
    } else if (types.includes('route')) {
      parsed.route = longName;
    } else if (types.includes('locality')) {
      parsed.city = longName;
    } else if (types.includes('administrative_area_level_2') && !parsed.city) {
      parsed.city = longName;
    } else if (types.includes('administrative_area_level_1')) {
      parsed.state = longName;
    } else if (types.includes('country')) {
      parsed.country = longName;
    } else if (types.includes('postal_code')) {
      parsed.postalCode = longName;
    } else if (types.includes('sublocality') || types.includes('neighborhood')) {
      parsed.neighborhood = longName;
    }
  }

  // Combine street number and route
  if (parsed.streetNumber && parsed.route) {
    parsed.street = ` ${parsed.route} ${parsed.streetNumber}`;
  } else if (parsed.route) {
    parsed.street = parsed.route;
  }

  return parsed;
}

/**
 * Convert place address components to geocoder format
 */
function convertToGeocoderComponents(
  placeComponents: google.maps.places.AddressComponent[]
): google.maps.GeocoderAddressComponent[] {
  return placeComponents.map((component) => ({
    long_name: component.longText || '',
    short_name: component.shortText || '',
    types: component.types,
  }));
}

/**
 * 1) Fetch suggestions with the AutocompleteSuggestion API.
 */
export async function getSuggestions(input: string): Promise<google.maps.places.AutocompleteSuggestion[]> {
  await loader.importLibrary('places');

  ensureSessionToken();

  const { suggestions } = await google.maps.places.AutocompleteSuggestion.fetchAutocompleteSuggestions({
    input,
    sessionToken: sessionToken!,
    includedPrimaryTypes: ['street_address'],
    // Allow all types - cities, addresses, etc.
  });

  return suggestions;
}

/**
 * 2) Given a chosen suggestion, resolve it to structured address data.
 *    - Parse address components for easy form population
 *    - Handle cases where user selects city/region without street address
 */
export async function resolveSuggestionToAddress(
  suggestion: google.maps.places.AutocompleteSuggestion
): Promise<ParsedAddress> {
  await loader.importLibrary('places');
  await loader.importLibrary('geocoding');

  if (!suggestion.placePrediction) {
    throw new Error('Invalid suggestion: missing place prediction');
  }

  // Turn prediction into a Place and fetch detailed fields
  const place = suggestion.placePrediction.toPlace();
  await place.fetchFields({
    fields: ['id', 'formattedAddress', 'location', 'types', 'addressComponents'],
  });

  if (!place.location) {
    throw new Error('Place has no location data');
  }

  const location = {
    lat: place.location.lat(),
    lng: place.location.lng(),
  };

  const types = place.types || [];
  const formattedAddress = place.formattedAddress || '';

  // Check if this is already a street-level address
  const hasStreetAddress = types.some((type) => ['street_address', 'premise', 'subpremise'].includes(type));

  let addressComponents: google.maps.GeocoderAddressComponent[] = [];
  let parsedComponents: Partial<ParsedAddress> = {};

  if (place.addressComponents) {
    // Convert place address components to geocoder format
    addressComponents = convertToGeocoderComponents(place.addressComponents);
    parsedComponents = parseAddressComponents(addressComponents);
  } else if (!hasStreetAddress) {
    // If it's just a city/region, try reverse geocoding to get more details
    try {
      const geocoder = new google.maps.Geocoder();
      const geocodeResp = await geocoder.geocode({
        location: place.location,
      });

      if (geocodeResp.results[0]) {
        addressComponents = geocodeResp.results[0].address_components;
        parsedComponents = parseAddressComponents(addressComponents);
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
    }
  }

  // Determine if this is a complete address (has street + city + country)
  const isCompleteAddress = Boolean(
    (parsedComponents.street || parsedComponents.route) && parsedComponents.city && parsedComponents.country
  );

  return {
    ...parsedComponents,
    formattedAddress,
    location,
    placeId: place.id || '',
    types,
    hasStreetAddress,
    isCompleteAddress,
  };
}

/**
 * Helper function to check if an address has enough detail for saving
 */
export function isAddressSaveable(address: ParsedAddress): boolean {
  // At minimum, we need city and country
  return Boolean(address.city && address.country);
}

/**
 * Format address for display in different contexts
 */
export function formatAddressForDisplay(address: ParsedAddress, format: 'short' | 'full' = 'full'): string {
  if (format === 'short') {
    if (address.street && address.city) {
      return `${address.street}, ${address.city}`;
    }
    if (address.city && address.country) {
      return `${address.city}, ${address.country}`;
    }
    return address.formattedAddress;
  }

  return address.formattedAddress;
}

/**
 * Create a saveable address object for your database/API
 */
export function createSaveableAddress(address: ParsedAddress) {
  return {
    street: address.street || '',
    city: address.city || '',
    state: address.state || '',
    country: address.country || '',
    postalCode: address.postalCode || '',
    neighborhood: address.neighborhood || '',
    formattedAddress: address.formattedAddress,
    latitude: address.location.lat,
    longitude: address.location.lng,
    placeId: address.placeId,
    // Metadata
    hasStreetAddress: address.hasStreetAddress,
    isCompleteAddress: address.isCompleteAddress,
  };
}
