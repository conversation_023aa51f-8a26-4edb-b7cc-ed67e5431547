import { MenuItem } from './types';

/**
 * Padding left class mappings for different menu depths
 */
const PADDING_LEFT_CLASSES = {
  0: 'pl-0',
  1: 'pl-8',
  2: 'pl-16',
  3: 'pl-24',
  4: 'pl-28',
  5: 'pl-32',
} as const;

/**
 * Left position class mappings for depth indicators
 */
const LEFT_POSITION_CLASSES = {
  0: 'left-2',
  1: 'left-2',
  2: 'left-[60px]',
} as const;

/**
 * Gets the appropriate padding left class based on menu depth
 * @param depth - The current depth level of the menu
 * @returns Tailwind CSS class for padding left
 */
export const getPaddingLeftClass = (depth: number): string => {
  return PADDING_LEFT_CLASSES[depth as keyof typeof PADDING_LEFT_CLASSES] || 'pl-32';
};

/**
 * Gets the appropriate left position class for depth indicators
 * @param depth - The current depth level of the menu
 * @returns Tailwind CSS class for left positioning
 */
export const getLeftClass = (depth: number): string => {
  return LEFT_POSITION_CLASSES[depth as keyof typeof LEFT_POSITION_CLASSES] || 'left-[60px]';
};

/**
 * Recursively checks if any child menu item is currently active
 * @param children - Array of child menu items
 * @param pathname - Current route pathname
 * @returns True if any child is active, false otherwise
 */
export const isChildActive = (children: MenuItem[], pathname: string): boolean => {
  return children.some((child) => {
    if (child.key === pathname) return true;
    if (child.children) return isChildActive(child.children, pathname);
    return false;
  });
};

/**
 * Creates dropdown menu items structure for Ant Design Dropdown component
 * @param children - Array of child menu items
 * @returns Formatted dropdown items for Ant Design
 */
export const createDropdownItems = (children: MenuItem[]) =>
  children.map((child) => ({
    key: child.key,
    label: child.label,
    children: child.children?.map((nested) => ({
      key: nested.key,
      label: nested.label,
    })),
  }));

/**
 * Generates common CSS classes for menu items based on state
 * @param isActive - Whether the menu item is currently active
 * @param isCollapsed - Whether the sidebar is collapsed
 * @returns Object containing common CSS classes
 */
export const getMenuItemClasses = (isActive: boolean, isCollapsed: boolean = false) => {
  const baseClasses = 'flex items-center justify-center cursor-pointer transition-all duration-200';
  const sizeClasses = isCollapsed ? 'w-12 h-12 rounded-xl' : '';
  const stateClasses = isActive
    ? 'bg-app-primary/10 text-app-primary'
    : 'hover:bg-app-gray-light hover:text-app-primary text-app-text-light';

  return {
    container: `${baseClasses} ${sizeClasses} ${stateClasses}`,
    icon: isActive ? 'text-app-primary' : 'text-app-text-light hover:text-app-primary',
    label: isActive ? 'text-app-primary font-medium' : 'text-app-text-light hover:text-app-primary',
  };
};
