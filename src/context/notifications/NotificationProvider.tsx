import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import { NotificationPlacement } from 'antd/es/notification/interface';
import React, { useCallback } from 'react';
import NotificationsContext from './NotificationContext';

export type ToastOptions = {
  title: string;
  description: string;
  type: 'Light' | 'Danger' | 'Success' | 'Warning' | 'Info';
  duration?: number;
};

type Props = {
  children: React.ReactNode;
};

const NotificationProvider = ({ children }: Props) => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = useCallback(
    (placement: NotificationPlacement, data: ToastOptions) => {
      api.info({
        message: data.title,
        description: data.description,
        placement,
        duration: data.duration,
        icon: {
          Success: <CheckCircleOutlined className="  text-green-500 " />,
          Danger: <CloseCircleOutlined className="text-app-danger" />,
          Info: <CloseCircleOutlined className="text-blue-500" />,
          Warning: <CloseCircleOutlined className="text-yellow-500" />,
          Light: <CloseCircleOutlined />,
        }[data.type],
        style: {
          width: 300,
        },
      });
    },
    [api]
  );

  return (
    <NotificationsContext.Provider
      value={{
        openNotification,
      }}
    >
      {contextHolder}
      {children}
    </NotificationsContext.Provider>
  );
};
export default NotificationProvider;
