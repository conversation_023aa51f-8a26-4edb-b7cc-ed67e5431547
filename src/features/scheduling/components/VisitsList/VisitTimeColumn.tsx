import dayjs from 'dayjs';

type VisitTimeColumnProps = {
  startTime: string;
  endTime: string;
};

/**
 * Component that displays the time column for a visit
 * Shows start and end times in HH:mm format
 */
export function VisitTimeColumn({ startTime, endTime }: VisitTimeColumnProps) {
  return (
    <div className="w-14 text-xs flex flex-col justify-center text-right pr-2">
      <div className="text-app-text-dark">{dayjs(startTime).format('HH:mm')}</div>
      <div className="text-app-text-light">{dayjs(endTime).format('HH:mm')}</div>
    </div>
  );
}
