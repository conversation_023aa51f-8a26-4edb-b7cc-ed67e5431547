import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { capitalizeString } from '@app/utils/fullNameCapilatizeFirst';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';
import { DateTimeDisplay } from '../../../../components/common/DateTimeDisplay';
import { getAddressWithFallback } from '../../../../utils/addressUtils';

export type VisitTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: unknown, record: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'scheduled':
      return 'blue';
    case 'in_progress':
      return 'processing';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

export const getVisitTableColumns = (): VisitTableColumnType<VisitPopulated>[] => [
  {
    key: 'Client',
    title: 'Client',
    width: 180,
    render: (_, visit?: VisitPopulated) => (
      <div>
        {visit?.client && (
          <ClientDisplay firstName={visit.client.firstName || ''} lastName={visit.client.lastName || ''} />
        )}
      </div>
    ),
  },
  {
    key: 'Caregivers',
    title: 'Caregivers',
    width: 200,
    render: (_, visit?: VisitPopulated) => (
      <>
        {visit?.caregivers?.map((caregiver) => (
          <ClientDisplay
            key={caregiver.caregiverId}
            firstName={caregiver.firstName || ''}
            lastName={caregiver.lastName || ''}
          />
        )) || '-'}
      </>
    ),
  },
  {
    key: 'visitTime',
    title: 'Time',

    align: 'center',
    width: 150,
    render: (_, visit?: VisitPopulated) => (
      <DateTimeDisplay
        startTime={visit?.startTime}
        endTime={visit?.endTime}
        showDateSeparately
        timeClassName="text-gray-600"
      />
    ),
  },
  {
    key: 'services',
    title: 'Services',

    align: 'center',
    width: 150,
    render: (_, visit?: VisitPopulated) => (
      <div>
        {visit?.services?.map((service) => (
          <Tag key={service.id} style={{ marginBottom: 2 }}>
            {service.service?.name || 'Service'}
          </Tag>
        )) || '-'}
      </div>
    ),
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: 'Status',
    align: 'center',
    width: 120,
    render: (status) => (
      <Tag color={getStatusColor(status as string)}>{((status as string) || '').replace('_', ' ').toUpperCase()}</Tag>
    ),
  },
  {
    key: 'address',
    title: 'Address',

    align: 'center',
    width: 200,
    render: (_, visit?: VisitPopulated) => {
      // Create address objects for visit and client with proper null handling
      const visitAddress = {
        street: visit?.street || undefined,
        city: visit?.city || undefined,
        zip: visit?.postalCode || undefined,
      };

      const clientAddress = visit?.client
        ? {
            street: visit.client.street || undefined,
            city: visit.client.city || undefined,
            zip: visit.client.postalCode || undefined,
          }
        : undefined;

      const formattedAddress = getAddressWithFallback(visitAddress, clientAddress);

      return <Typography.Text ellipsis>{capitalizeString(formattedAddress)}</Typography.Text>;
    },
  },
  {
    dataIndex: 'notes',
    key: 'notes',
    title: 'Notes',

    align: 'center',
    width: 200,
    render: (notes) => <Typography.Text ellipsis>{(notes as string) || '-'}</Typography.Text>,
  },
  {
    dataIndex: 'createdAt',
    key: 'createdAt',
    title: 'Created',

    align: 'center',
    width: 150,
    render: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
    ),
  },
];
