import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import Table from '@app/features/table/Table';
import { APP_PREFIX, SERVICE_REQUESTS_PAGE } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const ServiceRequestTable = () => {
  const navigate = useNavigate();
  const serviceRequests = useSchedulingStore((state) => state.serviceRequests);
  const loading = useSchedulingStore((state) => state.loadingServiceRequests);

  return (
    <div className="w-full h-full">
      <Table
        localStorageKey="serviceRequestTable"
        rowKey="serviceRequestId"
        data={serviceRequests}
        totalData={serviceRequests.length}
        loadingDataFetch={loading}
        addNavigate={() => navigate(`/${APP_PREFIX}/${SERVICE_REQUESTS_PAGE}/new`)}
        exportComponent={<></>}
        onRowClick={(record: ServiceRequestResponse) => {
          // Navigate to service request details or edit page
          console.log('Service request clicked:', record);
        }}
      />
    </div>
  );
};

export default ServiceRequestTable;
