import { Service, ServiceType } from '@api/READ_ONLY/services_api/Api';
import { StateCreator } from 'zustand';

export type ServicesSlice = {
  services: Service[];
  serviceTypes: ServiceType[];
  setServices: (services: Service[]) => void;
  setServiceTypes: (serviceTypes: ServiceType[]) => void;
};

export const createServicesSlice: StateCreator<ServicesSlice> = (set) => ({
  services: [],
  serviceTypes: [],
  setServices: (services) => set({ services }),
  setServiceTypes: (serviceTypes) => set({ serviceTypes }),
});
