import { Client } from '@api/READ_ONLY/client_api/Api';
import AlertDialog from '@app/components/ui/AlertDialog/AlertDialog';
import FormElement from '@app/components/ui/FormElement';
import { Button } from 'antd';
import { useMemo, useRef, useState } from 'react';
import { MdWorkHistory } from 'react-icons/md';
import AvailableVisits from './components/AvailableVisits/AvailableVisits';
import DateDayPicker from './components/DateDayPicker/DateDayPicker';
import { PatientSearch } from './components/SearchDropdowns/PatientSearch/PatientSearch';
import ServicesSelectionPanel from './components/ServicesSelectionPanel';
import { REPETITION_EVENTS } from './const';
import { useCreateServiceRequest } from './hooks/useServiceRequestActions';
import useServiceRequestDetails from './hooks/useServiceRequestInfo';
import useSuggestedVisits from './hooks/useSuggestedVisits';
import buildRRule from './rruleGenerate';
// Duplication of the type and also under ServicesSelectionPanel.tsx
type ServicesSelectionPanelHandle = {
  reset: () => void;
};

const CreateNewServiceRequestPanel = () => {
  const panelRef = useRef<ServicesSelectionPanelHandle>(null);
  const [isOpen, setIsOpen] = useState(false);

  const [errors, setErrors] = useState<{
    patientIdRequired: string;
    servicesRequired: string;
  }>({
    patientIdRequired: '',
    servicesRequired: '',
  });

  const { isValidToCreateANewAAServiceRequest, dateTime, notes, patient, serviceRequestExtraDetails } =
    useServiceRequestDetails();

  const rrule = buildRRule({
    zoneId: 'Europe/Athens',
    ...dateTime.values,
  });

  const generatedDaysForVisits = useMemo(() => {
    if (!dateTime.values.endingDate) {
      return [dateTime.values.startingDate.format('YYYY-MM-DD')];
    }

    return rrule?.all().map((d) => d.toISOString().slice(0, 10)) ?? [];
  }, [dateTime.values, rrule]);

  const { availableVisits, areVisitsFetching, isValidToFetchAvailableVisits } = useSuggestedVisits({
    generatedDaysForVisits,
    dateTime: {
      fromTime: dateTime.values.fromTime,
      toTime: dateTime.values.toTime,
    },
    caregiverIds: [],
    serviceIds: serviceRequestExtraDetails.value.services.map((service) => service.id),
  });

  const resetAllValuesToDefault = () => {
    patient.resetToDefault();
    notes.resetToDefault();
    dateTime.resetToDefault();
    setIsOpen(false);
    // Clearing the internal state of the services panel
    panelRef.current?.reset();
  };

  const { actions, isActionInProgress } = useCreateServiceRequest({
    notes: notes.value,
    patient: patient.value,
    isValidToCreateANewAAServiceRequest,
    resetAllValuesToDefault,
    dateTimeRecurrenceValues: dateTime.values,
    serviceRequestExtraDetails: serviceRequestExtraDetails.value,
    rruleString: rrule?.toString() || '',
  });

  const totalEstimatedTimeOfServices = useMemo(
    () => serviceRequestExtraDetails.value.services.reduce((total, s) => total + s.estimatedTimeMinutes, 0),
    [serviceRequestExtraDetails.value.services]
  );

  return (
    <div className="w-full max-w-[1440px] mx-auto flex h-full">
      {/* Left panel - Service Request Form */}
      <div className="col-span-6 flex flex-col max-w-[700px] w-full min-w-[400px] pr-6 justify-between border-r border-gray-200">
        <div className="flex flex-col mb-8 max-h-[calc(100vh - 300px)] overflow-y-auto">
          <div
            className={`flex items-center justify-center gap-4 ${isActionInProgress ? 'opacity-50 pointer-events-none' : ''}`}
          >
            <FormElement label="Patient" required error={errors.patientIdRequired}>
              <PatientSearch
                value={patient.value}
                setValue={(selectedPatient) => {
                  patient.updateValue(selectedPatient as Client);
                  setErrors((prev) => ({ ...prev, patientIdRequired: '' }));
                }}
              />
            </FormElement>

            <button
              type="button"
              aria-label="Patient history"
              title="Patient history"
              className="inline-flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-sm
             transition-all duration-200 hover:bg-gray-50 hover:shadow-md active:scale-95"
            >
              <MdWorkHistory className="h-5 w-5" color="var(--color-app-primary)" />
            </button>
          </div>

          <DateDayPicker
            dateTimeRecurrenceValues={dateTime.values}
            isDisabled={isActionInProgress}
            onSubmitRecurringEvents={(dateTimeRecurrenceValues) => {
              dateTime.updateDateTimeOnlyRecurrenceValues(dateTimeRecurrenceValues);

              if (dateTimeRecurrenceValues.frequency === 'weekly') {
                dateTime.updateDateTimeRecurrenceValuesByKey('repeat', REPETITION_EVENTS.WEEKLY);
              }

              if (dateTimeRecurrenceValues.frequency === 'daily') {
                dateTime.updateDateTimeRecurrenceValuesByKey('repeat', REPETITION_EVENTS.DAILY);
              }
            }}
            updateDateTimeRecurrenceValuesByKey={(key, value) => {
              dateTime.updateDateTimeRecurrenceValuesByKey(key, value);
            }}
          />

          <FormElement label="Notes" className="mt-4">
            <textarea
              className={`w-full h-24 border border-gray-300 rounded p-2 resize-none ${isActionInProgress ? 'opacity-50 pointer-events-none' : ''}`}
              value={notes.value}
              onChange={(e) => notes.updateValue(e.target.value)}
            />
          </FormElement>

          <ServicesSelectionPanel
            ref={panelRef}
            value={serviceRequestExtraDetails.value.services}
            onChange={(servicesIds) => {
              serviceRequestExtraDetails.updateValueByKey('services', servicesIds);
              setErrors((prev) => ({ ...prev, servicesRequired: '' }));
            }}
            totalEstimatedTimeOfServicesInMinutes={totalEstimatedTimeOfServices}
            error={errors.servicesRequired}
          />
        </div>

        <Button
          type="primary"
          className="min-h-[42px]"
          onClick={() => {
            // First check for errors - if ok continue
            if (!isValidToCreateANewAAServiceRequest) {
              setErrors({
                patientIdRequired: !patient.value ? 'Patient is required' : '',
                servicesRequired:
                  serviceRequestExtraDetails.value.services.length === 0 ? 'At least one service is required' : '',
              });
              return;
            }

            const hasAtLeastOnUnavailableVisit = availableVisits.some((visit) => visit.caregivers.length === 0);

            if (hasAtLeastOnUnavailableVisit) {
              setIsOpen(true);
              return;
            }

            // If everything ok it will create the service request and whatever it follows
            actions.handleOnCreateButtonClick();
          }}
          loading={isActionInProgress}
          disabled={isActionInProgress}
        >
          Create
        </Button>
      </div>

      {/* Right panel - Available Visits */}
      <AvailableVisits
        availableVisits={availableVisits}
        areDisabled={isActionInProgress}
        isFetching={areVisitsFetching}
        isValidToFetch={isValidToFetchAvailableVisits}
      />

      <AlertDialog
        isOpen={isOpen}
        title="Are you sure you want to create this service request?"
        description="Not all visits have a caregiver assigned. Do you still wish to continue?"
        cancelText="Cancel"
        confirmationText="Proceed"
        isLoading={isActionInProgress}
        isDisabled={isActionInProgress}
        onCancel={() => setIsOpen(false)}
        onConfirm={() => {
          actions.handleOnCreateButtonClick();
        }}
      />
    </div>
  );
};

export default CreateNewServiceRequestPanel;
