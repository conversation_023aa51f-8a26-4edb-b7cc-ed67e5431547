import { SuggestedVisitGroup } from '@api/READ_ONLY/visits_api/Api';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import SimpleBar from 'simplebar-react';
import VisitCard from './VisitCard';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

interface AvailableVisitProps {
  availableVisits: SuggestedVisitGroup[];
  isFetching: boolean;
  isValidToFetch: boolean;
  areDisabled: boolean;
}

const VisitCardSkeleton = () => (
  <div
    className="
    relative flex flex-col items-center justify-center text-center gap-2
    w-full px-3 py-3 rounded-xl bg-white border border-gray-200 shadow-sm
    animate-pulse
  "
  >
    <span aria-hidden className="absolute left-0 top-1/2 -translate-y-1/2 h-8 w-1 rounded-r bg-gray-200" />

    <div className="h-5 w-24 rounded bg-gray-200" />
  </div>
);

const AvailableVisits = ({ availableVisits, isFetching, isValidToFetch, areDisabled }: AvailableVisitProps) => {
  return (
    <div
      className={`w-full min-w-[200px] max-w-[800px] pl-4 mb-4 ${areDisabled ? 'opacity-50 pointer-events-none' : ''}`}
    >
      <h3 className="text-xl font-bold mb-4">Available Visits</h3>

      {!isValidToFetch && (
        <p className="text-md text-gray-500 mb-4">
          To be able to see the available visits, please select a date, start-end time and at least one service from the
          left panel.
        </p>
      )}

      {availableVisits.length === 0 && !isFetching && isValidToFetch && <div>No available visits</div>}

      {isFetching && (
        <SimpleBar forceVisible="y" autoHide style={{ height: 'calc(100% - 32px)', paddingRight: '12px' }}>
          <div className="grid gap-3 w-full grid-cols-[repeat(auto-fit,minmax(120px,1fr))]">
            {Array.from({ length: 8 }).map((_, index) => (
              // we do not care its the skeleton
              // eslint-disable-next-line react/no-array-index-key
              <VisitCardSkeleton key={index} />
            ))}
          </div>
        </SimpleBar>
      )}

      {!isFetching && (
        <SimpleBar
          forceVisible="y"
          autoHide
          style={{ height: 'calc(100% - 32px)', paddingRight: '12px', marginRight: '-12px' }}
        >
          <div className="grid gap-3 w-full grid-cols-[repeat(auto-fit,minmax(120px,1fr))]">
            {availableVisits?.length > 0 &&
              availableVisits.map((visit) => (
                <VisitCard
                  key={visit.date}
                  date={visit.date}
                  isAvailable={visit.caregivers.length > 0}
                  numberOfAvailableCaregivers={visit.caregivers.length}
                />
              ))}
          </div>
        </SimpleBar>
      )}
    </div>
  );
};

export default AvailableVisits;
