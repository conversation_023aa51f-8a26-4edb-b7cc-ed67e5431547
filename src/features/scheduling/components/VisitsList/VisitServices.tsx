import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';

type VisitServicesProps = {
  services: VisitPopulated['services'];
};

/**
 * Component that displays the services for a visit
 * Shows service notes with duration
 */
export function VisitServices({ services }: VisitServicesProps) {
  if (services.length === 0) return null;

  return (
    <div className="my-2">
      <div className="text-base font-semibold">Υπηρεσίες</div>
      {services.map((service) => (
        <div className="pl-2 text-sm" key={service.id}>
          {service ? `${service.notes} - 45 λεπτά` : 'Άγνωστη υπηρεσία'}
        </div>
      ))}
    </div>
  );
}
