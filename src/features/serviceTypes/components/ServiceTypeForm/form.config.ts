import { FieldGroup } from '@app/types/form.types';

export const serviceTypesFormConfig: FieldGroup[] = [
  {
    fields: [
      { name: 'name', label: 'Name', type: 'text', width: 'full', rules: { required: 'Name is required' } },
      {
        name: 'description',
        label: 'Description',
        type: 'textarea',
        width: 'full',
        rules: { required: 'Name is required' },
      },
    ],
    hideSubmit: true,
  },
];
