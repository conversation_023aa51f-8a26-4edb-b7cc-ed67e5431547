import { CaregiverShift, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { Button, Form, Input, TimePicker } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { MdDelete } from 'react-icons/md';
import { ModalMode } from './ShiftsBoard';

type Props = {
  shiftList: ShiftCreate[] | CaregiverShift[];
  setShiftList: Dispatch<SetStateAction<ShiftCreate[] | CaregiverShift[]>>;
  hideAddButton?: boolean;
  handleDeleteShift?: (id: number, index: number) => void;
  mode: ModalMode;
};

const isCaregiverShift = (shift: ShiftCreate | CaregiverShift): shift is CaregiverShift => {
  return (shift as CaregiverShift).caregiverShiftId !== undefined;
};

const timeToMinutes = (time?: string | null) => {
  if (!time) return null;
  const [h, m] = time.split(':').map(Number);
  return h * 60 + m;
};

const MINUTE_STEP = 15;

const ShiftForm = ({ shiftList, setShiftList, hideAddButton, handleDeleteShift, mode }: Props) => {
  const updateShift = (index: number, newShift: CaregiverShift | ShiftCreate) => {
    const updated = [...shiftList];
    updated[index] = { ...updated[index], ...newShift };
    setShiftList(updated as ShiftCreate[] | CaregiverShift[]);
  };

  const removeShift = (index: number, caregiverShiftId?: number) => {
    console.log('Shift delete', caregiverShiftId);
    // if (!hideAddButton && shiftList.length <= 1) return;
    handleDeleteShift && caregiverShiftId
      ? handleDeleteShift(caregiverShiftId, index)
      : setShiftList(shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[]);
  };

  const addShift = () => setShiftList([...(shiftList as ShiftCreate[]), { periodFrom: '', periodTo: '', notes: '' }]);

  // without memo the function is called even on hover
  const disabledTimesList = useMemo(() => {
    return shiftList.map((shift, index) => {
      // convert “other” shifts to minute tuples for simpmicity in checks
      const otherShifts = shiftList
        .filter((_, i) => i !== index)
        .map((s) => [timeToMinutes(s.periodFrom), timeToMinutes(s.periodTo)] as [number | null, number | null]);

      // ---------- START TIME  ----------
      const getDisabledStartMinutes = (hour: number) => {
        const minutes: number[] = [];
        for (let m = 0; m < 60; m += MINUTE_STEP) {
          const candidate = hour * 60 + m;
          // if this minute overlaps with any other shift we push it to the disabled list
          const overlaps = otherShifts.some(
            ([sStart, sEnd]) => sStart !== null && sEnd !== null && candidate >= sStart && candidate < sEnd
          );
          if (overlaps) minutes.push(m);
        }
        return minutes;
      };

      const getDisabledStartHours = () => {
        const hours: number[] = [];
        for (let h = 0; h < 24; h++) {
          // if all minutes in an hour are disabled then the hour is disabled
          if (getDisabledStartMinutes(h).length === 60 / MINUTE_STEP) hours.push(h);
        }
        return hours;
      };

      // ---------- END TIME ----------
      const selectedStart = timeToMinutes(shift.periodFrom);

      // closest future shift start after selectedStart
      // if no future shift exists -> end of day
      const nextShiftStart =
        otherShifts
          .map(([sStart]) => sStart)
          .filter((s): s is number => s != null && (selectedStart == null || s > selectedStart))
          .sort((a, b) => a - b)[0] ?? 24 * 60;

      const getDisabledEndMinutes = (hour: number) => {
        const minutes: number[] = [];
        for (let m = 0; m < 60; m += MINUTE_STEP) {
          const candidate = hour * 60 + m;

          // A minute is disabled if:
          const disableBecauseNoStart = selectedStart == null; // Start time isn’t selected
          const disableBeforeOrAtStart = selectedStart != null && candidate <= selectedStart; // Minute is before or equal to start
          const disablePastNextShift = candidate > nextShiftStart; // Minute is after the next shift starts

          if (disableBecauseNoStart || disableBeforeOrAtStart || disablePastNextShift) {
            minutes.push(m);
          }
        }

        return minutes;
      };

      const getDisabledEndHours = () => {
        const hours: number[] = [];
        for (let h = 0; h < 24; h++) {
          if (getDisabledEndMinutes(h).length === 60 / MINUTE_STEP) hours.push(h);
        }
        // console.log(`[shift ${index}] disabled hours:`, hours);
        return hours;
      };

      return {
        start: {
          disabledHours: getDisabledStartHours,
          disabledMinutes: getDisabledStartMinutes,
        },
        end: {
          disabledHours: getDisabledEndHours,
          disabledMinutes: getDisabledEndMinutes,
        },
      };
    });
  }, [shiftList]);

  return (
    <Form layout="vertical">
      <div className="px-1 py-4 rounded-md">
        {shiftList.map((shift, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="grid grid-cols-10 gap-0 items-end rounded-md">
            <Form.Item label={index === 0 ? 'Start Time' : undefined} required className="col-span-3 !m-1">
              <TimePicker
                format="HH:mm"
                minuteStep={15}
                needConfirm={false}
                className="w-full"
                value={shift.periodFrom ? dayjs(shift.periodFrom, 'HH:mm') : null}
                onChange={(time) => updateShift(index, { ...shift, periodFrom: time?.format('HH:mm') || '' })}
                disabledTime={() => disabledTimesList[index].start}
              />
            </Form.Item>
            <Form.Item label={index === 0 ? 'End Time' : undefined} required className="col-span-3  !m-1">
              <TimePicker
                format="HH:mm"
                minuteStep={15}
                needConfirm={false}
                className="w-full"
                value={shift.periodTo ? dayjs(shift.periodTo, 'HH:mm') : null}
                onChange={(time) => updateShift(index, { ...shift, periodTo: time?.format('HH:mm') || '' })}
                disabledTime={() => disabledTimesList[index].end}
              />
            </Form.Item>
            <Form.Item label={index === 0 ? 'Notes' : undefined} className="col-span-3  !m-1">
              <Input
                value={shift.notes || ''}
                onChange={(e) => updateShift(index, { ...shift, notes: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="" className="col-span-1 !m-1">
              <Button
                type="text"
                danger
                size="small"
                onClick={() => removeShift(index, isCaregiverShift(shift) ? shift.caregiverShiftId : undefined)}
                disabled={!hideAddButton && mode === 'create' && shiftList.length <= 1}
              >
                <MdDelete className="text-lg" />
              </Button>
            </Form.Item>
          </div>
        ))}
        {!hideAddButton && (
          <Button onClick={addShift} type="default" className="m-1">
            + Add Another Shift
          </Button>
        )}
      </div>
    </Form>
  );
};

export default ShiftForm;
