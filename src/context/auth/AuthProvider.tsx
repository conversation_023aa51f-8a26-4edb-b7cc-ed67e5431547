import { ParsedKeycloakToken, TPermissions } from '@feat-auth/types';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import { AuthContext, defaultUser, UserContextType } from './AuthContext';

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const keycloakAuth = useAuth();

  const [permissions, setPermissions] = useState<TPermissions>([]);
  const [user, setUser] = useState<UserContextType>(defaultUser);

  const accessToken = keycloakAuth.user?.access_token || null;

  useEffect(() => {
    if (!accessToken) return;

    try {
      const payload = JSON.parse(atob(accessToken.split('.')[1])) as ParsedKeycloakToken;
      console.log({ payload });
      //TODO: Check permissions when ready
      //If no permission we cant access the dashboard
      setPermissions(['Teams_Read']);
      // setPermissions(
      //   (payload.permissions as TPermissions) || [
      //     'Teams_Read',
      //     'FinancialReporting_ViewAllBeneficiaries',
      //     'Trips_EditAll',
      //     'Trips_ReadWrite',
      //     'Trips_Read',
      //     'Meetings_EditAll',
      //     'FinancialReporting_Read',
      //     'FinancialReporting_Write',
      //   ]
      // );

      setUser({
        email: payload.email,
        name: payload.name,
        fullname: payload.name || 'N/A',
        userId: Number(payload.attributes.userId) || -1,
      });
    } catch (e) {
      console.error('Failed to parse JWT', e);
    }
  }, [accessToken]);

  const allowIf = useCallback(
    (perms: TPermissions) => perms.every((perm) => permissions.includes(perm)),
    [permissions]
  );

  const logout = () => {
    keycloakAuth.signoutRedirect();
    setTimeout(() => localStorage.removeItem('redirectAfterLogin'), 0);
  };

  return (
    <AuthContext.Provider
      value={{
        auth: accessToken,
        removeAuthentication: keycloakAuth.removeUser,
        allowIf,
        logout,
        permissions,
        user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
