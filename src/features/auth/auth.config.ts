// import { TOKEN_DEFAULT_PERMISSIONS_KEY } from '..';

const AUTH_LOCAL_STORAGE_KEY = 'homecare-token';

const getAuthToken = (): string | null => {
  if (!localStorage) return null;

  const token: string | null = localStorage.getItem(AUTH_LOCAL_STORAGE_KEY);

  if (!token) return null;

  return JSON.parse(token);
};

const setToken = (auth: string) => {
  if (!localStorage) {
    return;
  }
  try {
    const lsValue = JSON.stringify(auth);
    localStorage.setItem(AUTH_LOCAL_STORAGE_KEY, lsValue);
  } catch (error) {
    console.error('AUTH LOCAL STORAGE SAVE ERROR', error);
  }
};

const removeAuthFromStorage = () => {
  if (!localStorage) return;

  try {
    console.log('removeAuth FUNCTION');
    localStorage.removeItem(AUTH_LOCAL_STORAGE_KEY);
  } catch (error) {
    console.error('AUTH LOCAL STORAGE REMOVE ERROR', error);
  }
};

const IsTokenExpired = (): boolean => {
  try {
    const token = getAuthToken();
    if (!token) throw new Error('Token is null or undefined');
    const { exp } = JSON.parse(atob(token.split('.')[1]));
    return exp < Date.now() / 1000;
  } catch (err) {
    console.error('Token is null or undefined');
    return false;
  }
};
function parseJwt<T>(token: string): T | null {
  if (!token) return null;

  try {
    const base64Url = token.split('.')[1];

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    const jsonPayload = decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join('')
    );

    const parsedToken = JSON.parse(jsonPayload);

    return {
      ...parsedToken,
      // permissions: parsedToken[TOKEN_DEFAULT_PERMISSIONS_KEY],
    };
  } catch (error) {
    return null;
  }
}

export { AUTH_LOCAL_STORAGE_KEY, getAuthToken, IsTokenExpired, parseJwt, removeAuthFromStorage, setToken };
