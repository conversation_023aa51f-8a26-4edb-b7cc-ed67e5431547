import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/services_api/Api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/table.types';
import { useCallback, useState } from 'react';

interface ServiceSearchProps {
  disabled?: boolean;
  onServiceSelect: (v: Service | Service[] | string | undefined) => void;
  value: Service | undefined;
  optionDisable?: (item: Service) => boolean;
  selectedValues: Service[];
}

export function ServicesSearch({ disabled = false, onServiceSelect, value, selectedValues }: ServiceSearchProps) {
  const [data, setData] = useState<CommonListResponse<Service>>({
    Results: [],
    TotalResults: 0,
  });

  // const fetchData = async (searchValue?: string, pageSize?: number, pageIndex?: number) => {

  const fetchData = useCallback(async () => {
    const response = await ApiClient.serviceApi.services.getServicesServicesGet();

    // filter out already selected services
    const filtered = response.data.data.filter(
      (service) => !selectedValues.some((sel) => sel.serviceId === service.serviceId)
    );

    const newData = {
      Results: filtered,
      TotalResults: filtered.length,
    };

    setData(newData);
    return newData;
  }, [selectedValues]);

  return (
    <FetchSearchSelect
      data={data}
      disabled={disabled}
      value={value ? `${value.name} ${value.estimatedTimeMinutes} minutes` : undefined}
      // How its possible to return so many types ? Why not a discriminative union if it needs more that a type ?
      onValueChange={(item) => onServiceSelect(item)}
      getId={(o) => o.serviceId}
      getLabel={(item) => `${item.name} ${item.estimatedTimeMinutes} minutes`}
      fetchData={fetchData}
      placeholder="Select Service"
      isClearable={false}
    />
    // <SearchSelect<Service>
    //   onValueChange={(item) => onServiceSelect(item)}
    //   getId={(o) => o.serviceId}
    //   getLabel={(item) => `${item.name} ${item.estimatedTimeMinutes} minutes`}
    //   placeholder="Select Service"
    //   isClearable={false}
    //   data={{ Results: [], TotalResults: 1500 }}
    //   fetchData={() => Promise.resolve({ Results: [], TotalResults: 1500 })} // to avoid TS error
    // />
  );
}
