export function getInitials(fullName: unknown, opts: { fallback?: string; locale?: string } = {}): string {
  const { fallback = '', locale } = opts;

  if (typeof fullName !== 'string') return fallback;

  const cleaned = fullName.replace(/\s+/g, ' ').trim();
  if (!cleaned) return fallback;

  const parts = cleaned.split(' ');
  const first = parts[0] ?? '';
  const last = parts.length > 1 ? parts[parts.length - 1] : '';

  const up = (s: string) => (s ? s[0].toLocaleUpperCase(locale) : '');

  const initials = `${up(first)}${up(last)}`;
  return initials || fallback;
}
