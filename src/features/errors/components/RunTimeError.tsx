import { toAbsoluteUrl } from '@app/utils/toAbsoluteUrl';
import { Button } from 'antd';
import { FC, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const RunTimeError: FC = () => {
  const navigate = useNavigate();
  const [isUpdateError, setIsUpdateError] = useState(false);
  const handleRedirect = () => {
    navigate('/dashboard');
  };
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      const errorMessage = event.message || '';

      // Check if error is due to a missing module or failed lazy load
      if (
        errorMessage.includes('Failed to fetch dynamically imported module') ||
        errorMessage.includes('Loading chunk') ||
        errorMessage.includes('Module not found')
      ) {
        setIsUpdateError(true);
      }
    };

    window.addEventListener('error', handleError);
    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);
  return (
    <div className="bg-gray-700 w-screen h-screen flex justify-center flex-col items-center">
      <img
        data-testid="logoLg"
        alt="Logo"
        src={toAbsoluteUrl('media/logos/logo_dark.png')}
        className="ps-3 h-[75px] px-6"
      />
      {isUpdateError ? (
        <UpdateApp />
      ) : (
        <>
          <h1 className="fw-bolder fs-2hx text-gray-100 mb-4">Oops!</h1>
          <div className="fw-semibold fs-6 text-gray-300 mb-2">Something went wrong.</div>
          <div className="fw-semibold fs-6 text-gray-300 mb-7">The issue has been reported to our team.</div>
          <Button type="primary" onClick={handleRedirect}>
            Back to Dashboard
          </Button>
        </>
      )}
    </div>
  );
};

const UpdateApp = () => {
  const handleRefresh = () => {
    window.location.reload(); // Reload the app to get the latest version
  };
  return (
    <div className="text-center text-gray-100">
      <h2>⚠️ Application Update Required</h2>
      <p>The application has been updated. Please refresh to get the latest version.</p>
      <Button type="primary" onClick={handleRefresh}>
        Reload App
      </Button>
    </div>
  );
};

export { RunTimeError };
