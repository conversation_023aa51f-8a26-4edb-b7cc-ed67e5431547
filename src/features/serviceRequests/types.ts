import { Dayjs } from 'dayjs';
import { FREQUENCY_OPTIONS, REPETITION_EVENTS } from './const';

export type RepetitionEventsTimeFrame = (typeof REPETITION_EVENTS)[keyof typeof REPETITION_EVENTS];

export type RepetitionTimeFrame = (typeof FREQUENCY_OPTIONS)[keyof typeof FREQUENCY_OPTIONS];

export interface DateTimeRecurrenceValues {
  startingDate: Dayjs;
  endingDate: Dayjs | null;
  repeat: RepetitionEventsTimeFrame;
  interval: number;
  frequency: RepetitionTimeFrame;
  repetitionDaysOfTheWeek: number[];
  fromTime: Dayjs;
  toTime: Dayjs;
}

export type RecurringEventValues = Omit<DateTimeRecurrenceValues, 'fromTime' | 'toTime' | 'repeat'>;

export interface ServiceRequestExtraDetails {
  services: {
    id: number;
    estimatedTimeMinutes: number;
  }[];
}

export type DateTimeRecurrenceKeys = keyof DateTimeRecurrenceValues;

export type DateTimeRecurrenceValuesType = DateTimeRecurrenceValues[keyof DateTimeRecurrenceValues];

export type ServiceRequestExtraDetailsKeys = keyof ServiceRequestExtraDetails;

export type ServiceRequestExtraDetailsValues = ServiceRequestExtraDetails[keyof ServiceRequestExtraDetails];
