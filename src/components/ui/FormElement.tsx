interface FormElementProps {
  label: string | React.ReactNode;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
  className?: string;
  preserveErrorSpace?: boolean;
}

const FormElement = ({ label, required, error, children, className, preserveErrorSpace = true }: FormElementProps) => {
  return (
    <div className={`flex flex-col w-full ${className}`}>
      <label className="text-sm font-medium mb-1 flex items-center">
        {required && <span className="text-red-500 mr-1">*</span>}

        <span className={`${required ? '' : 'ml-1'}`}>{label}</span>
      </label>

      <div>{children}</div>

      {preserveErrorSpace && <div className="h-4 ml-3 text-red-500 text-xs">{error || '\u00A0'}</div>}
    </div>
  );
};

export default FormElement;
