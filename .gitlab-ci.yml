stages: 
  - install
  - lint
  - test
  - container
  - deploy


variables:
  CONTAINER_IMAGE: $LOOM_CI_REGISTRY/$LOOM_CI_IMAGENAME
  DOCKER_BUILDKIT: 1  #Enable BuildKit for inline caching

# Step 1: Install dependencies once
install_dependencies:
  stage: install
  image: node:20.12.0-alpine
  script:
    - npm ci
  artifacts:
    paths:
      - node_modules/
  cache:
    key: "$CI_COMMIT_REF_SLUG"
    paths:
      - node_modules/
      - .npm/
  rules:
    # - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_TAG' 


# Step 2: Lint the code
lint:
  stage: lint
  image: node:20.12.0-alpine
  dependencies:
    - install_dependencies
  artifacts:
    paths:
      - node_modules/
      - .npm/
  script:
    - npm run lint
  rules:
    # - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_TAG' 


# Step 3: Run tests
test:
  stage: test
  image: node:20.12.0-alpine
  dependencies:
    - install_dependencies
  artifacts:
    paths:
      - node_modules/
      - .npm/
  script:
    - npm run test
  rules:
    # - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_TAG' 



container-develop:
  stage: container
  only:
    - develop
  image: docker:latest
  services:
    - docker:dind
  variables:
    DOCKER_BUILDKIT: 1
  before_script:
    - docker login $LOOM_CI_REGISTRY -u $LOOM_CI_REGISTRY_USER -p $LOOM_CI_REGISTRY_PASS
  script:
    - docker pull $CONTAINER_IMAGE:develop || true
    - echo $CI_COMMIT_MESSAGE >> VERSION
    - docker build --build-arg NODE_ENV=develop --build-arg APP_VERSION=develop --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from=$CONTAINER_IMAGE:develop -t $CONTAINER_IMAGE:develop .
    - docker push $CONTAINER_IMAGE:develop

container-tag-main:
  stage: container
  only:
    - tags
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - export CONTAINER_IMAGE=$LOOM_CI_REGISTRY/$LOOM_CI_IMAGENAME
    - docker login $LOOM_CI_REGISTRY
      -u $LOOM_CI_REGISTRY_USER
      -p $LOOM_CI_REGISTRY_PASS
  script:
    - docker pull $CONTAINER_IMAGE:$CI_COMMIT_TAG || true
    - echo $CI_COMMIT_MESSAGE >> VERSION
    - docker build
      --build-arg NODE_ENV=production
      --build-arg APP_VERSION=$CI_COMMIT_TAG
      --cache-from $CONTAINER_IMAGE:$CI_COMMIT_TAG
      -t $CONTAINER_IMAGE:$CI_COMMIT_TAG .
    - docker push $CONTAINER_IMAGE:$CI_COMMIT_TAG

deploy-develop:
  stage: deploy
  only:
    - develop
  image: curlimages/curl:latest
  script:
    - curl --fail --location --request POST 'https://loom.konnecta.io:9443/api/webhooks/881ee9cd-ca4f-4dbd-b808-04975526ff49?tag=develop'

