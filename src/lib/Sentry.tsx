import { useEffect } from 'react';

import envConfig from '@app/enviroment/enviroment';
import * as Sentry from '@sentry/react';
import { createRoutesFromChildren, matchRoutes, useLocation, useNavigationType } from 'react-router-dom';
const SENTRY_DNS = envConfig.getEnvKey('SENTRY_DNS');
const SENTRY_ENVIROMENT = envConfig.getEnvKey('SENTRY_ENVIROMENT') as string;
Sentry.captureException(new Error('Test error from client'));
if (SENTRY_DNS) {
  Sentry.init({
    dsn: SENTRY_DNS as string,
    release: SENTRY_ENVIROMENT,
    integrations: [
      // See docs for support of different versions of variation of react router
      // https://docs.sentry.io/platforms/javascript/guides/react/configuration/integrations/react-router/
      Sentry.reactRouterV6BrowserTracingIntegration({
        useEffect,
        useLocation,
        useNavigationType,
        createRoutesFromChildren,
        matchRoutes,
      }),
      Sentry.replayIntegration(),
    ],

    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for tracing.
    tracesSampleRate: 1.0,

    // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
    tracePropagationTargets: [/^\//, /^https:\/\/yourserver\.io\/api/],

    // Capture Replay for 10% of all sessions,
    // plus for 100% of sessions with an error
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
  });
}
