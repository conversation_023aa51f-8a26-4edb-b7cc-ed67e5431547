import { useCallback, useRef } from 'react';

export const SCROLL_THRESHOLD = 1;

interface UseSearchSelectProps {
  onSearch?: (value: string) => void;
  onLoadMore?: () => void;
  hasMore: boolean;
  loading: boolean;
}

export const useSearchSelect = ({ onSearch, onLoadMore, hasMore, loading }: UseSearchSelectProps) => {
  const searchValueRef = useRef('');

  const handleSearch = useCallback(
    (value: string) => {
      searchValueRef.current = value;
      onSearch?.(value);
    },
    [onSearch]
  );

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { currentTarget } = e;
      const isNearBottom =
        currentTarget.scrollTop + currentTarget.clientHeight >= currentTarget.scrollHeight - SCROLL_THRESHOLD;

      if (isNearBottom && hasMore && !loading && onLoadMore) {
        onLoadMore();
      }
    },
    [hasMore, loading, onLoadMore]
  );

  const handleOpenChange = useCallback((open: boolean) => {
    if (!open) {
      searchValueRef.current = '';
    }
  }, []);

  return {
    handleSearch,
    handleScroll,
    handleOpenChange,
  };
};
