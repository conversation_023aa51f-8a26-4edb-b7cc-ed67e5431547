import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { CaregiversSlice, createCaregiversSlice } from './Slices/caregiversSlice';
import { ClientsSlice, createClientsSlice } from './Slices/clientsSlice';
import { createDoctorsSlice, DoctorsSlice } from './Slices/doctorsSlice';
import { createServiceRequestsSlice, ServiceRequestsSlice } from './Slices/serviceRequestsSlice';
import { createServicesSlice, ServicesSlice } from './Slices/servicesSlice';
import { createUiSlice, UiSlice } from './Slices/uiSlice';
import { createVisitsSlice, VisitsSlice } from './Slices/visitsSlice';

type StoreState = VisitsSlice &
  CaregiversSlice &
  ClientsSlice &
  DoctorsSlice &
  ServiceRequestsSlice &
  ServicesSlice &
  UiSlice;

export const useSchedulingStore = create<StoreState>()(
  devtools((...a) => ({
    ...createVisitsSlice(...a),
    ...createCaregiversSlice(...a),
    ...createClientsSlice(...a),
    ...createDoctorsSlice(...a),
    ...createServiceRequestsSlice(...a),
    ...createServicesSlice(...a),
    ...createUiSlice(...a),
  }))
);
