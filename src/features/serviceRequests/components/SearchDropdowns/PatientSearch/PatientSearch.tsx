import { ApiClient } from '@api/api-configuration';
import { Client, ClientCreate } from '@api/READ_ONLY/client_api/Api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/table.types';
import { capitalizeFullNameFirst } from '@app/utils/fullNameCapilatizeFirst';
import { isValidPhone } from '@app/utils/validators/validPhone';
import { VisitLocation } from '@feat-scheduling/components/VisitCalendarEvent/VisitLocation';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Input, Modal } from 'antd';
import { useCallback, useState } from 'react';
import { GrLocation, GrPhone } from 'react-icons/gr';

export function PatientSearch({
  disabled = false,
  setValue,
  value,
  style,
}: {
  disabled?: boolean;
  setValue: (e: string | Client | Client[] | undefined) => void;
  value: Client | undefined;
  style?: React.CSSProperties;
}) {
  const setClients = useSchedulingStore((state) => state.setClients);

  // const clients = useSchedulingStore((state) => state.clients);

  // console.log('PATIENT SEARCH clients', clients);

  const [data, setData] = useState<CommonListResponse<Client>>({
    Results: [],
    TotalResults: 0,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newPatientInput, setNewPatientInput] = useState('');

  /** fetch clients by search term */
  const fetchClients = useCallback(
    async (searchValue?: string, pageSize?: number, pageIndex?: number) => {
      try {
        const clients: Client[] = [];
        const query = {
          ...(searchValue && { query: searchValue }),
          ...(pageSize && { limit: pageSize }),
          ...(pageIndex != null && pageSize && { offset: pageIndex * pageSize }),
        };
        const response = await ApiClient.clientApi.clients.searchClientClientsSearchGet(query);

        setData({
          Results: response.data.data,
          TotalResults: response.data.total,
        });
        setClients(clients);
      } catch (err) {
        console.error(err);
      }
      return {} as CommonListResponse<Client>;
    },
    [setClients]
  );

  /** create a new patient */
  //TODO: We need to revise this
  const handleNewPatient = async () => {
    if (newPatientInput) {
      const newPatient: ClientCreate = {
        firstName: isValidPhone(newPatientInput) ? 'UNKNOWN' : newPatientInput.split(' ')[0] || 'UNKNOWN',
        lastName: isValidPhone(newPatientInput) ? 'UNKNOWN' : newPatientInput.split(' ')[1] || 'UNKNOWN',
        // username: newPatientInput,
        city: 'UNKNOWN',
        phone: isValidPhone(newPatientInput) ? newPatientInput : '**********',
      };

      try {
        const createdClient = await ApiClient.clientApi.clients.createClientClientsPost(newPatient);
        // fetch again to get the new client
        await fetchClients('');

        if (createdClient.data) {
          setValue(createdClient.data);
        }
      } catch (err) {
        console.error('Failed to create patient', err);
      } finally {
        setIsModalOpen(false);
      }
    }
  };

  /** handle when user selects "create new" */
  const handleCreateNew = (input: string) => {
    setNewPatientInput(input);

    setIsModalOpen(true);
  };
  return (
    <>
      <FetchSearchSelect<Client>
        data={data}
        style={{ ...style, height: '100%' }}
        disabled={disabled}
        value={value ? `${value.firstName} ${value.lastName}` : undefined}
        onValueChange={(item) => setValue(item)}
        getId={(item) => item.clientId}
        getLabel={(item) => (
          <div className="flex flex-col text-sm">
            <span className="font-semibold">
              {capitalizeFullNameFirst(item.firstName as string, item.lastName as string)}
            </span>
            <span className="text-gray-500 text-xs flex items-center gap-2">
              <GrPhone /> {item.phone}
            </span>
            <span className="text-gray-400 text-xs flex items-center gap-2">
              <GrLocation />
              <VisitLocation street={item.street || ''} city={item.city || ''} postalCode={item.postalCode || ''} />
            </span>
          </div>
        )}
        fetchData={fetchClients}
        placeholder="Search patient by name or phone"
        canAddNew={true}
        canAdd={true}
        onCreateNew={(input) => handleCreateNew(input)}
      />

      <Modal
        title="Create New Patient"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleNewPatient}
        okText="Create Patient"
      >
        <p>You are about to create a new patient with:</p>

        <Input
          value={newPatientInput}
          placeholder="Name or phone"
          onChange={(e) => {
            console.log(e);
            setNewPatientInput(e.target.value);
          }}
        />
      </Modal>
    </>
  );
}
