import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import DateTimeDisplay from '@app/components/common/DateTimeDisplay';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';

export type ServiceRequestTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: unknown, record: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'pending':
      return 'orange';
    case 'approved':
      return 'green';
    case 'rejected':
      return 'red';
    case 'in_progress':
      return 'blue';
    case 'completed':
      return 'success';
    default:
      return 'default';
  }
};

export const getServiceRequestTableColumns = (): ServiceRequestTableColumnType<ServiceRequestResponse>[] => [
  {
    key: 'client',
    title: 'Client',
    width: 180,
    render: (_, request: ServiceRequestResponse) => (
      <ClientDisplay firstName={request.client.firstName || ''} lastName={request.client.lastName || ''} />
    ),
  },
  {
    key: 'preferredCaregiver',
    title: 'Preferred Caregiver',
    width: 180,
    render: (_, request?: ServiceRequestResponse) => (
      <div>
        {request?.preferredCaregiver ? (
          <>
            <Typography.Text>
              {request.preferredCaregiver.firstName} {request.preferredCaregiver.lastName}
            </Typography.Text>
            <div>
              <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                {request.preferredCaregiver.phone}
              </Typography.Text>
            </div>
          </>
        ) : (
          <Typography.Text type="secondary">No preference</Typography.Text>
        )}
      </div>
    ),
  },
  {
    dataIndex: 'fromDate',
    key: 'fromDate',
    title: 'From Date',
    width: 130,
    render: (_, request) => (
      <DateTimeDisplay
        startTime={request?.fromDate}
        endTime={request?.toDate}
        showDateSeparately
        timeClassName="text-gray-600"
      />
    ),
  },
  {
    key: 'services',
    title: 'Services',
    width: 200,
    render: (_, request?: ServiceRequestResponse) => (
      <div>
        {request?.services?.map((service) => (
          <Tag key={service.serviceId} style={{ marginBottom: 2 }}>
            {service.name}
          </Tag>
        )) || '-'}
      </div>
    ),
  },
  {
    dataIndex: 'status',
    key: 'status',
    title: 'Status',
    width: 120,
    align: 'center',
    render: (status) => (
      <Tag color={getStatusColor(status as string)}>
        {((status as string) || 'PENDING').replace('_', ' ').toUpperCase()}
      </Tag>
    ),
  },
  {
    dataIndex: 'createdAt',
    key: 'createdAt',
    title: 'Created',
    width: 120,
    render: (date) => (
      <DateTimeDisplay startTime={date as string} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
    ),
  },
];
