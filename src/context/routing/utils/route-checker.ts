export const isRouteExist = (key: string, paths: string[]) =>
  paths.some((path) => isMatchingQueryParamsRoute(path, key));

export const isMatchingQueryParamsRoute = (routePattern: string, actualRoute: string) => {
  // Remove query parameters from actualRoute
  const cleanActualRoute = actualRoute.split('?')[0];

  const routeSegments = routePattern.split('/').filter(Boolean);
  const actualSegments = cleanActualRoute.split('/').filter(Boolean);

  if (routeSegments.length !== actualSegments.length) {
    return false;
  }

  return routeSegments.every((segment, index) => {
    return segment.startsWith(':') || segment === actualSegments[index];
  });
};
