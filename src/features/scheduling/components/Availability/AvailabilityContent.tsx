import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { Card, Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

type Props = {
  visit: VisitPopulated | null;
};

const AvailabilityContent: React.FC<Props> = ({ visit }) => {
  if (!visit) return null;
  return (
    <Card
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '4px',
          backgroundColor: '#EAF0FA',
          borderRadius: '15px',
          padding: 0,
        },
      }}
    >
      <Text
        style={{
          fontSize: '12px',
          backgroundColor: 'var(--color-blue-500)',
          color: 'white',
          borderRadius: 10,
          paddingTop: 2,
          paddingBottom: 2,
          paddingRight: 8,
          paddingLeft: 8,
        }}
      >
        {'Caregiver Name'}
      </Text>
      {/* <Text type="secondary" style={{ fontSize: '12px' }}>
        {visit.address || <Text type="secondary">Not Scheduled</Text>}
      </Text> */}
    </Card>
  );
};

export default AvailabilityContent;
