export interface AddressValue {
  address: string;
  city: string;
  country: string;
  zip: string;
  lat: number;
  lng: number;
}

export interface AddressPickerProps {
  value?: AddressValue;
  onChange?: (value: AddressValue) => void;
  apiKey: string;
  language?: string;
  region?: string;
  defaultCenter?: { lat: number; lng: number };
  mapHeight?: number;
  mapOptions?: Record<string, unknown>; // google.maps.MapOptions
  types?: string[];
  componentRestrictions?: Record<string, unknown>; // google.maps.places.ComponentRestrictions
  disabled?: boolean;
}

export interface ParsedAddress {
  address: string;
  city: string;
  country: string;
  zip: string;
}
