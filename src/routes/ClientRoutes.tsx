import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { ClientsPage } from '@pages/clients/ClientListPage';
import { CreateClient } from '@pages/clients/CreateClient';
import { EditClient } from '@pages/clients/EditClient';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ClientTablePage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Patients Management">
      <ClientsPage />
    </HeaderLayout>
  </Suspense>
);
const ClientFormPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Create Patient">
      <CreateClient />
    </HeaderLayout>
  </Suspense>
);
const ClientEditPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Edit Patient">
      <EditClient />
    </HeaderLayout>
  </Suspense>
);
// Export Lazy-Loaded Route
export default {
  ClientFormPage,
  ClientTablePage,
  ClientEditPage,
};
