import { ApiClient } from '@api/api-configuration';
import { Caregiver, CaregiverCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { APP_PREFIX, CAREGIVERS_PREFIX, EDIT_CAREGIVER } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import { transformList } from '@app/utils/transformToList';
import useNotifications from '@context/notifications/useNotificationContext';
import { CaregiverForm } from '@feat-caregivers/components/CaregiverForm/CaregiverForm';
import { useNavigate } from 'react-router-dom';

export const CreateCaregiver = () => {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = async (formData: Caregiver | CaregiverCreate) => {
    try {
      console.log('Form data:', formData);

      const { firstName, email, lastName } = formData;
      if (!firstName) throw new Error('First name is required');
      if (!email) throw new Error('Email is required');
      if (!lastName) throw new Error('Last name is required');

      const createdUser = await ApiClient.usersApi.users.createUserUsersPost({
        email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        roles: ['cgv'],
      });

      const transformedData = {
        ...formData,
        userId: createdUser.data.userId,
        certifications: transformList(formData.certifications),
        skills: transformList(formData.skills),
        specialties: transformList(formData.specialties),
        coverageAreas: transformList(formData.coverageAreas),
        languagesSpoken: transformList(formData.languagesSpoken),
      };
      const response = await ApiClient.caregiverApi.caregivers.createCaregiverCaregiversPost(
        transformedData as CaregiverCreate
      );
      console.log('Response:', response);
      openNotification('topRight', {
        title: `Caregiver`,
        description: 'Caregiver created successfully.',
        type: 'Success',
      });
      const caregiverEditPath = `/${APP_PREFIX}/${CAREGIVERS_PREFIX}/${EDIT_CAREGIVER.replace(':id', String(response.data.caregiverId))}`;
      navigate(caregiverEditPath);
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
    return Promise.resolve();
  };
  return <CaregiverForm onSubmit={onSubmit} />;
};
