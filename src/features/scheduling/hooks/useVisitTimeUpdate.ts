import { ApiClient } from '@api/api-configuration';
import { useMessage } from '@context/message';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useState } from 'react';

export const useVisitTimeUpdate = () => {
  const [isLoading, setIsLoading] = useState(false);
  const updateVisitInStore = useSchedulingStore((state) => state.updateVisit);
  const { showSuccess, showError } = useMessage();

  const updateVisitTime = async (visitId: number, startTime: string, endTime: string) => {
    console.log({ visitId, startTime, endTime });
    setIsLoading(true);
    try {
      await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(visitId, {
        startTime,
        endTime,
      });

      // Update store with the new times
      updateVisitInStore({
        id: visitId,
        startTime,
        endTime,
      });

      // Success feedback
      showSuccess('Visit time updated successfully');
    } catch (error) {
      showError('Failed to update visit time');
      console.error('Visit time update error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    updateVisitTime,
    isLoading,
  };
};
