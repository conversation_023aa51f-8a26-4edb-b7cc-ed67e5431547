import { Divider, InputProps, Select, SelectProps } from 'antd';
import { memo, useMemo } from 'react';
import DefaultFooter from './components/DefaultFooter';
import LoadingIndicator from './components/LoadingIndicator';
import SearchInput from './components/SearchInput';
import { DIVIDER_STYLE } from './constants';
import { useSearchSelect } from './hooks/useSearchSelect';
import { FooterRenderFn } from './types';

type SearchSelectV2Props = {
  inputSearchProps?: InputProps;
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  totalCount?: number;
  currentCount?: number;
  footerRender?: FooterRenderFn;
} & SelectProps;

function SearchSelectV2<T extends string>(props: SearchSelectV2Props) {
  const {
    inputSearchProps,
    onLoadMore,
    hasMore = false,
    loading = false,
    totalCount,
    currentCount,
    footerRender,
    onSearch,
    ...selectProps
  } = props;

  const { handleSearch, handleScroll, handleOpenChange } = useSearchSelect({
    onSearch,
    onLoadMore,
    hasMore,
    loading,
  });

  // Memoize footer props to prevent unnecessary re-renders
  const footerProps = useMemo(
    () => ({
      currentCount,
      totalCount,
      hasMore,
      loading,
    }),
    [currentCount, totalCount, hasMore, loading]
  );

  return (
    <Select<T>
      {...selectProps}
      showSearch={false}
      filterOption={false}
      style={{ ...selectProps.style, width: '100%' }}
      onPopupScroll={handleScroll}
      popupRender={(menu) => (
        <div className="w-full relative">
          <SearchInput inputSearchProps={inputSearchProps} onSearch={handleSearch} />
          <Divider style={DIVIDER_STYLE} />
          {menu}

          <LoadingIndicator loading={loading} />
          <Divider style={DIVIDER_STYLE} />

          {/* Custom footer or default count display */}
          {footerRender ? footerRender(footerProps) : <DefaultFooter {...footerProps} />}
        </div>
      )}
      onOpenChange={handleOpenChange}
    />
  );
}

SearchSelectV2.displayName = 'SearchSelectV2';

export default memo(SearchSelectV2);
