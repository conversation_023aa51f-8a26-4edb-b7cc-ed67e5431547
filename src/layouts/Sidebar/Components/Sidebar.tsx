import useUiContext from '@context/ui/useUiContext';
import { memo, useEffect, useState } from 'react';
import { FiSearch } from 'react-icons/fi';

import UserDropdown from '@app/layouts/UserDropdown';
import SimpleBar from 'simplebar-react';
import BottomAvatar from './BottomAvatar';
import RecursiveMenu from './RecursiveMenu';
import { SidebarLogo } from './SidebarLogo';
import { SidebarSearch } from './SidebarSearch';
type IconComponent = React.ComponentType<React.SVGProps<SVGSVGElement>>;
type MenuItem = {
  key: number;
  icon?: { filled: IconComponent; outline: IconComponent }; // <-- CHANGED  label: string | React.ReactElement;
  label: string;
  children: MenuItem[];
};

type Props = {
  collapseIcon?: React.ReactElement;
  menuItems?: MenuItem[];
};

const Sidebar = ({ menuItems = [] }: Props) => {
  const { isSidebarCollapsed } = useUiContext();
  const [searchValue, setSearchValue] = useState('');
  const [filteredItems, setFilteredItems] = useState<MenuItem[]>(menuItems);

  useEffect(() => {
    setFilteredItems(menuItems);
  }, [menuItems]);

  const filterMenuItems = (items: MenuItem[], query: string): MenuItem[] => {
    return items
      .map((item) => {
        const labelText = typeof item.label === 'string' ? item.label.toLowerCase() : '';
        const matches = labelText.includes(query.toLowerCase());

        const filteredChildren = item.children ? filterMenuItems(item.children, query) : [];

        if (matches || filteredChildren.length > 0) {
          return {
            ...item,
            children: filteredChildren,
          };
        }

        return null;
      })
      .filter(Boolean) as MenuItem[];
  };

  const handleSearchChange = (e: string) => {
    setSearchValue(e);

    if (e.trim() === '') {
      setFilteredItems(menuItems);
    } else {
      const filtered = filterMenuItems(menuItems, e);
      setFilteredItems(filtered);
    }
  };

  return (
    <div
      className={`h-full flex flex-col relative rounded-2xl overflow-hidden ${
        isSidebarCollapsed ? 'w-16' : 'w-80'
      } transition-all duration-300 ease-in-out bg-app-gray-light`}
    >
      {/* Top: Logo + Collapse Button + Search */}
      <div className="flex flex-col gap-4 px-2 py-4">
        <div className={`flex ${isSidebarCollapsed ? 'flex-col-reverse' : 'flex-row'} justify-between items-center`}>
          <SidebarLogo collapsed={isSidebarCollapsed} />
        </div>
        {!isSidebarCollapsed && (
          <div className="px-4 py-2">
            <SidebarSearch
              PrefixIcon={FiSearch}
              onChange={(e) => handleSearchChange(e)}
              initValue=""
              className=" !bg-app-gray"
              placeholder="Search..."
              type="search"
              value={searchValue}
              debounce={0}
            />
          </div>
        )}
      </div>

      <div className="flex-1 min-h-0">
        <SimpleBar autoHide={false} forceVisible={true} className="w-full h-full">
          <div className={'flex-1 ' + (isSidebarCollapsed ? '' : 'pl-8')}>
            {filteredItems.length > 0 ? (
              <RecursiveMenu items={filteredItems} depth={0} />
            ) : (
              <div className="text-center text-gray-400 mt-4">No results</div>
            )}
          </div>
        </SimpleBar>
      </div>
      {/* Bottom: User Info */}
      <div className={`bg-app-gray-light w-full  ${isSidebarCollapsed ? 'w-16 p-2' : 'w-64'}`}>
        {isSidebarCollapsed ? (
          <div className="flex justify-center ">
            <UserDropdown />
          </div>
        ) : (
          <BottomAvatar />
        )}
      </div>
    </div>
  );
};

export default memo(Sidebar);
