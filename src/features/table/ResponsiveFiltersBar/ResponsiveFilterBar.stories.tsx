import { Client } from '@api/READ_ONLY/client_api/Api';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { UrlParamProvider } from '@context/urlParams/UrlParamProvider';
import { Meta, StoryObj } from '@storybook/react-vite';
import { Button } from 'antd';
import { ResponsiveFiltersBar } from './ResponsiveFilterBar';

// Dummy columns for TableProvider
const columns: ExtendedTableColumnType<Client>[] = [
  {
    key: 'Name',
    title: 'Name',
    dataIndex: 'name',
  },
  {
    key: 'Email',
    title: 'Email',
    dataIndex: 'email',
  },
];

// Sample filters
const initialFilters = {
  searchTerm: '',
};

const initialStatusFilters = [
  { Id: 0, label: 'Submitted', value: false },
  { Id: 1, label: 'Approved', value: false },
  { Id: 2, label: 'Rejected', value: false },
];

// Custom buttons

const meta: Meta<typeof ResponsiveFiltersBar> = {
  title: 'Components/ResponsiveFiltersBar',
  component: ResponsiveFiltersBar,
  decorators: [
    (Story) => (
      <UrlParamProvider>
        <TableProvider
          storageKey="storybook-client-table"
          storageKeyColumns="storybook-client-columns"
          initialFilters={initialFilters}
          initialStatusFilters={initialStatusFilters}
          fetchData={() => {}}
          cols={columns}
        >
          <div style={{ padding: 16 }}>
            <Story />
          </div>
        </TableProvider>
      </UrlParamProvider>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ResponsiveFiltersBar>;

export const Default: Story = {
  args: {
    hasAdd: true,
    Export: <Button>Export</Button>,
    addNavigate: () => alert('Navigate to add page'),
    handleReset: () => alert('Reset filters'),
    meModeFilter: <Button>Me Mode</Button>,
    assigneesFilter: <div>Assignees Dropdown</div>,
    dropdownFilter: <div>Status Dropdown</div>,
    columnFilters: true,
    openCustomisation: false,
    setOpenCustomisation: (val: boolean) => {
      console.log('Customisation toggled:', val);
    },
  },
};
