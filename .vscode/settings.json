{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "search.useGlobalIgnoreFiles": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "css.customData": [".vscode/tailwind.json"], "search.exclude": {"**/.git": true, "**/node_modules": true, "**/dist": true}, "[dockerfile]": {"editor.defaultFormatter": "ms-azuretools.vscode-docker"}, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "eslint.enable": true, "eslint.probe": ["javascript", "javascriptreact", "typescript", "typescriptreact", "json", "jsonc", "mdx", "markdown", "html", "vue", "svelte", "astro"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": [{"mode": "auto"}]}