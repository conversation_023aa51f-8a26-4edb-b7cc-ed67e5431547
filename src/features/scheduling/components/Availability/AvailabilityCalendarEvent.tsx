import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { Popover } from 'antd';
import { memo } from 'react';
import AvailabilityContent from './AvailabilityContent';

const AvailabilityCalendarEvent = (visit: VisitPopulated) => {
  return (
    <Popover
      placement="top"
      motion={{ visible: false }}
      content={<AvailabilityContent visit={visit} />}
      arrow={false}
      styles={{
        body: {
          padding: 0,
          backgroundColor: 'transparent',
          boxShadow: '0 6px 4px  rgba(0, 0, 0, 0.2)',
          borderRadius: '15px',
        },
      }}
    >
      <div className={`w-full border border-app-gray-dark rounded-xl h-full bg-app-success/50`}></div>
    </Popover>
  );
};

export default memo(AvailabilityCalendarEvent);
