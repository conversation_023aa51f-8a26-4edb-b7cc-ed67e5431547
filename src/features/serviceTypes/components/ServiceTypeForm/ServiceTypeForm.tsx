import { ServiceType, ServiceTypeCreate } from '@api/READ_ONLY/services_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, SERVICE_LIST, SERVICE_PAGE } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';
import { serviceTypesFormConfig } from './form.config';

type Props = {
  data?: ServiceType;
};
export function ServiceForm({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = async (formData: ServiceTypeCreate) => {
    try {
      console.log(formData);
      openNotification('topRight', {
        title: `Service Type`,
        description: 'Service type created successfully. ',
        type: 'Success',
      });
      console.log(`${APP_PREFIX}/${SERVICE_PAGE}/${SERVICE_LIST}`);
      navigate(`/${APP_PREFIX}/${SERVICE_PAGE}/${SERVICE_LIST}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return <FormWrapper defaultValues={data} fields={serviceTypesFormConfig} onSubmit={onSubmit} />;
}
