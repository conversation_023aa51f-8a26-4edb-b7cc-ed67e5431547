import { FormField } from '@app/features/form/Components/FormFields';
import { FieldConfig, FieldGroup } from '@app/types/form.types';
import { <PERSON><PERSON>, Select } from 'antd';
import { useState } from 'react';
import { DefaultValues, FieldErrors, FieldValues, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';

function getColSpan(width: string = 'full') {
  switch (width) {
    case 'third':
      return 'col-span-12 sm:col-span-4';
    case 'half':
      return 'col-span-12 sm:col-span-6';
    case 'twoThirds':
      return 'col-span-12 sm:col-span-8';
    case 'full':
    default:
      return 'col-span-12';
  }
}

type FormProps<T extends FieldValues> = {
  fields: FieldGroup[];
  onSubmit?: SubmitHandler<T>;
  defaultValues?: DefaultValues<T>;
  extraClasses?: string;
  hideSubmit?: boolean;
};

export function FormWrapper<T extends FieldValues>({
  fields,
  onSubmit,
  defaultValues,
  extraClasses = 'min-h-screen',
  hideSubmit = false,
}: FormProps<T>) {
  const methods = useForm<T>({
    defaultValues,
    mode: 'onSubmit',
  });

  const { id } = useParams();
  const [activeTab, setActiveTab] = useState(fields[0]?.title || '0');
  const errors = methods.formState.errors as FieldErrors<T>;

  // Check if any field in the group has an error
  const hasGroupError = (group: FieldGroup) =>
    group.fields.some((field: FieldConfig<unknown>) => {
      const keys = field.name.split('.');
      let err: unknown = errors;
      for (const key of keys) {
        if (!err || typeof err !== 'object' || !(key in (err as object))) return false;
        err = (err as Record<string, unknown>)[key];
      }
      return !!err && typeof err === 'object' && 'type' in (err as object);
    });

  const renderAllGroups = () => (
    <>
      {fields.map((group, i) => {
        const isActive = activeTab === (group.title || `section-${i}`);
        return (
          <div key={group.title || i} className={`${isActive ? 'block' : 'hidden'} grid grid-cols-12 gap-4 sm:gap-6`}>
            {group.fields.map((field) => (
              <div key={field.name} className={getColSpan(field.width)}>
                <FormField field={field} />
              </div>
            ))}
          </div>
        );
      })}
    </>
  );

  const renderVerticalTabs = () => {
    const activeGroup = fields.find((g) => (g.title || `section-0`) === activeTab);
    return (
      <div className="hidden md:flex flex-col w-48 border-r border-gray-200">
        {fields.map((group, i) => {
          const tabKey = group.title || `section-${i}`;
          const isActive = activeTab === tabKey;
          const hasError = hasGroupError(group);
          return (
            <button
              type="button"
              key={tabKey}
              onClick={() => setActiveTab(tabKey)}
              className={`text-left cursor-pointer px-4 py-3 font-medium border-l-4 transition-colors
                ${
                  isActive
                    ? `border-[var(--color-sky-600)] ${hasError ? 'text-red-500' : 'text-[var(--color-sky-600)]'} bg-gray-50`
                    : hasError
                      ? 'border-red-500 text-red-500 hover:bg-gray-50'
                      : 'border-transparent text-gray-600 hover:text-[var(--color-sky-600)] hover:border-[var(--color-sky-600)] hover:bg-gray-50'
                }
              `}
            >
              {group.title || `Section ${i + 1}`}
            </button>
          );
        })}

        {/* Desktop Submit button */}
        {!hideSubmit && onSubmit && activeGroup && !activeGroup?.hideSubmit && (
          <div className="mt-4 px-4 w-full">
            <Button type="primary" htmlType="submit" size="large" block>
              {id ? 'Save' : 'Create'}
            </Button>
          </div>
        )}
      </div>
    );
  };

  const renderMobileTabs = () => (
    <div className="md:hidden mb-4">
      <Select
        value={activeTab}
        onChange={(value) => setActiveTab(value)}
        className="w-full"
        options={fields.map((group, i) => ({
          label: group.title || `Section ${i + 1}`,
          value: group.title || `section-${i}`,
        }))}
      />
    </div>
  );

  const handleSubmit = methods.handleSubmit(
    (data) => {
      if (onSubmit) onSubmit(data);
    },
    () => {
      const firstErrorGroup = fields.find((g) => hasGroupError(g));
      if (firstErrorGroup) setActiveTab(firstErrorGroup.title || '');
      const firstErrorField = Object.keys(errors)[0];
      if (firstErrorField) {
        const el = document.querySelector(`[name="${firstErrorField}"]`);
        if (el) el.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  );

  const activeGroup = fields.find((g) => (g.title || `section-0`) === activeTab);

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={onSubmit ? handleSubmit : undefined}
        className={`bg-white py-2 overflow-y-auto ${extraClasses} relative flex flex-col md:flex-row`}
      >
        {/* Desktop sidebar */}
        {renderVerticalTabs()}

        {/* Main content including mobile dropdown + content + mobile button */}
        <div className="flex flex-col w-full md:flex-1 md:pl-6">
          {/* Mobile dropdown */}
          {renderMobileTabs()}

          {/* Form content */}
          {renderAllGroups()}

          {/* Mobile Submit button */}
          {!hideSubmit && onSubmit && activeGroup && !activeGroup?.hideSubmit && (
            <div className="md:hidden mt-6 px-4 w-full">
              <Button type="primary" htmlType="submit" size="large" block>
                {id ? 'Save' : 'Create'}
              </Button>
            </div>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
