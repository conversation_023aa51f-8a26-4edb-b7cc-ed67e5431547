import { ApiClient } from '@api/api-configuration';
import { ClientUpdate } from '@api/READ_ONLY/client_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import ChangePasswordForm, { ChangePasswordFormValues } from '@pages/userProfile/ChangePasswordForm';
import { Button } from 'antd';
import React from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';

export const ChangePasswordCaregiver: React.FC = () => {
  const { id } = useParams();
  const { openNotification } = useNotifications();

  const methods = useForm<ChangePasswordFormValues>({
    defaultValues: {
      old_password: '',
      new_password: '',
      confirm_password: '',
    },
  });

  const onSubmit: SubmitHandler<ChangePasswordFormValues> = async (data) => {
    try {
      console.log('submitting', data);
      if (data.new_password === data.confirm_password) {
        if (id) {
          await ApiClient.clientApi.clients.updateClientClientsClientIdPut(Number(id), data as unknown as ClientUpdate);
          openNotification('topRight', {
            title: `Password change`,
            description: 'Password updated successfully.',
            type: 'Success',
          });
        }
      }
    } catch (err) {
      console.error('Updating password err', err);
      openNotification('topRight', {
        title: `Password change`,
        description: 'Password change failed.',
        type: 'Warning',
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <ChangePasswordForm />

      {/* Fixed Save button at bottom right */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button onClick={() => onSubmit(methods.getValues())} type="primary" htmlType="submit" size="large">
          Save
        </Button>
      </div>
    </FormProvider>
  );
};
