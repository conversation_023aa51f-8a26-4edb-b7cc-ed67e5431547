import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { Service } from '@api/READ_ONLY/services_api/Api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/table.types';
import { DefaultOptionType } from 'antd/es/select';
import qs from 'qs';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';

export function ServicesSearch({
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
  query,
  selectedCaregiver,
  style,
}: {
  disabled?: boolean;
  setValue: (v: Service[] | undefined) => void;
  value: Service[] | undefined;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: Service) => boolean;
  query?: {
    offset?: number;
    limit?: number;
  };
  style?: React.CSSProperties;
  selectedCaregiver?: Caregiver;
}) {
  const [fullData, setFullData] = useState<CommonListResponse<Service>>({
    Results: [],
    TotalResults: 0,
  });

  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState(searchValue);

  // 🔑 Debounce searchValue → update debouncedSearch after 300ms
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchValue);
    }, 300);
    return () => clearTimeout(handler);
  }, [searchValue]);

  // Fetch all services if no caregiver is selected
  const fetchAllServices = useCallback(async () => {
    const res = await ApiClient.serviceApi.services.getServicesServicesGet(query, {
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    });

    const initialData = {
      Results: res.data.data,
      TotalResults: res.data.total,
    };

    setFullData(initialData);
    return initialData;
  }, [query]);

  // Decide what to show when caregiver changes
  useEffect(() => {
    if (selectedCaregiver?.services) {
      const caregiverServices = selectedCaregiver.services as Service[];
      setFullData({ Results: caregiverServices, TotalResults: caregiverServices.length });
    } else {
      fetchAllServices();
    }
  }, [selectedCaregiver, fetchAllServices]);

  // 🔑 Filter only in memory with debounce
  const filteredData = useMemo(() => {
    if (!debouncedSearch || debouncedSearch.trim() === '') {
      return fullData;
    }
    if (debouncedSearch.length < 3) {
      return fullData;
    }
    const lowerSearch = debouncedSearch.toLowerCase();
    const filtered = fullData.Results.filter((d) => d.name.toLowerCase().includes(lowerSearch));
    return {
      Results: filtered,
      TotalResults: filtered.length,
    };
  }, [debouncedSearch, fullData]);
  const noopFetch: () => Promise<CommonListResponse<Service> | undefined> = async () => Promise.resolve(undefined);
  console.log('value', value);
  return (
    <FetchSearchSelect<Service>
      data={filteredData}
      disabled={disabled}
      style={style}
      mode="multiple"
      value={value?.map((v) => ({
        label: `${v.name} ${v.estimatedTimeMinutes} minutes`,
        value: v.serviceId,
      }))}
      onValueChange={(items) => {
        const selectedServices: Service[] = Array.isArray(items) ? (items as Service[]) : [];
        setValue(selectedServices);
      }}
      onSearchChange={(val) => setSearchValue(val)} // ✅ debounce handled separately
      getId={(item) => item.serviceId}
      getLabel={(item) => `${item.name} ${item.estimatedTimeMinutes} minutes`}
      fetchData={!selectedCaregiver ? fetchAllServices : noopFetch}
      placeholder="Select Service"
      optionRender={optionRender}
      optionDisable={optionDisable}
      onClear={() => {
        setSearchValue('');
        setValue([]);
      }}
    />
  );
}
