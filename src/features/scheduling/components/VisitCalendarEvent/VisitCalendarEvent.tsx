import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Popover } from 'antd';
import { memo, Ref } from 'react';
import { ConnectDropTarget } from 'react-dnd';
import { useDropZone } from './useDropZone';
import { useVisitHover } from './useVisitHover';
import { VisitAvatar } from './VisitAvatar';
import VisitPopupContent from './VisitContent';

const VisitCalendarEvent = (visit: VisitPopulated) => {
  const activeVisit = useSchedulingStore((state) => state.activeVisit);
  const setActiveVisit = useSchedulingStore((state) => state.setActiveVisit);

  const isActive = Number(activeVisit?.id) === visit.id;
  const hasActiveVisit = activeVisit !== null;
  const shouldDimOthers = hasActiveVisit && !isActive;
  const { isDroppable, dropRef } = useDropZone({ visit, isActive });
  const { hoveredVisit, handleMouseEnter, handleMouseLeave } = useVisitHover({ visit });

  return (
    <Popover
      placement="top"
      motion={{ visible: false }}
      content={<VisitPopupContent visit={hoveredVisit ?? visit} />}
      arrow={false}
      styles={{
        body: {
          padding: 0,
          backgroundColor: 'transparent',
          boxShadow: '0 6px 4px  rgba(0, 0, 0, 0.2)',
          borderRadius: '15px',
        },
      }}
    >
      <div
        ref={dropRef as unknown as Ref<HTMLDivElement> & ConnectDropTarget}
        className={`w-full border border-app-gray-dark rounded-xl h-full flex items-center justify-center group transition-all duration-200 
        ${
          isDroppable
            ? 'bg-app-success/50 !border-2 !border-dashed !border-[#8b8cfa]'
            : isActive
              ? 'bg-app-secondary-light'
              : 'bg-app-gray hover:bg-app-gray-dark'
        }`}
        style={{
          opacity: shouldDimOthers ? 0.35 : 1,
        }}
        onClick={() => setActiveVisit(visit)}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <VisitAvatar visit={visit} />
      </div>
    </Popover>
  );
};

export default memo(VisitCalendarEvent);
