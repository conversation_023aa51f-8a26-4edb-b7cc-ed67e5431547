// Sidebar.stories.tsx
import { Meta, StoryFn } from '@storybook/react-vite';
import { ComponentType, ReactElement } from 'react';
import { FiChevronLeft, FiHome, FiSettings, FiUser } from 'react-icons/fi';
import { MemoryRouter } from 'react-router-dom';
import Sidebar from './Sidebar';

type IconComponent = ComponentType<React.SVGProps<SVGSVGElement>>;

type MenuItem = {
  key: number;
  icon?: { filled: IconComponent; outline: IconComponent }; // <-- CHANGED  label: string | React.ReactElement;
  label: string;
  children: MenuItem[];
};

const iconMap: Record<number, { filled: IconComponent; outline: IconComponent }> = {
  1: { filled: FiHome, outline: FiHome },
  2: { filled: FiUser, outline: FiUser },
  5: { filled: FiSettings, outline: FiSettings },
};

const defaultMenuItems: MenuItem[] = [
  { key: 1, label: 'Home', children: [] },
  {
    key: 2,
    label: 'Users',
    children: [
      { key: 3, label: 'List', children: [] },
      { key: 4, label: 'Add User', children: [] },
    ],
  },
  { key: 5, label: 'Settings', children: [] },
];

interface SidebarWithIconsProps {
  menuItems: MenuItem[];
  collapseIcon?: ReactElement;
}

const SidebarWithIcons = ({ menuItems, collapseIcon }: SidebarWithIconsProps) => {
  const addIcons = (items: MenuItem[]): MenuItem[] =>
    items.map((item) => ({
      ...item,
      icon: iconMap[item.key],
      children: item.children ? addIcons(item.children) : [],
    }));

  const itemsWithIcons = addIcons(menuItems);

  return (
    <MemoryRouter>
      <Sidebar collapseIcon={collapseIcon} menuItems={itemsWithIcons} />
    </MemoryRouter>
  );
};

const meta: Meta<typeof Sidebar> = {
  title: 'Components/Sidebar',
  component: Sidebar,
  argTypes: {
    menuItems: {
      control: 'object',
    },
    collapseIcon: { control: false },
  },
};

export default meta;

const Template: StoryFn<typeof Sidebar> = (args) => (
  <SidebarWithIcons
    collapseIcon={args.collapseIcon}
    menuItems={args.menuItems ?? []} // ✅ avoid undefined
  />
);

export const Default = Template.bind({});
Default.args = {
  collapseIcon: <FiChevronLeft />,
  menuItems: defaultMenuItems,
};
