{"openapi": "3.1.0", "info": {"title": "users", "description": "\nMicroservice for managing home care users.\n\n## Features\n* Create, read, update, and delete user records\n* Secure endpoints with authentication and authorization\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\nAdmin endpoints require additional admin permissions.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/users/roles": {"get": {"tags": ["Roles"], "summary": "Get all roles", "description": "Retrieve a list of all user roles", "operationId": "get_roles_users_roles_get", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RoleResponse"}, "type": "array", "title": "Response Get Roles Users Roles Get"}}}}, "500": {"description": "Internal server error"}}}}, "/users": {"get": {"tags": ["Users"], "summary": "Get all users", "description": "Retrieve a list of all users with pagination support", "operationId": "get_users_users_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Users"], "summary": "Create new user", "description": "Create a new user", "operationId": "create_user_users_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateReq"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/{user_id}": {"get": {"tags": ["Users"], "summary": "Get user by ID", "description": "Retrieve a specific user by their ID", "operationId": "get_user_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user", "description": "Update an existing user's information", "operationId": "update_user_users__user_id__put", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateReq"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete user", "description": "Delete a user by ID", "operationId": "delete_user_users__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"204": {"description": "No content"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/users/search": {"get": {"tags": ["Users"], "summary": "Search users", "description": "Search for users by various criteria", "operationId": "search_users_users_search_get", "parameters": [{"name": "data", "in": "query", "required": true, "schema": {"type": "string", "minLength": 3, "description": "Search query (min 3 characters)", "title": "Data"}, "description": "Search query (min 3 characters)"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSearchPaginationResponse"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "RoleResponse": {"properties": {"name": {"type": "string", "title": "Name"}, "code": {"type": "string", "title": "Code"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "roleId": {"type": "integer", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["name", "code", "roleId"], "title": "RoleResponse"}, "UserCreateReq": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}, "roles": {"items": {"type": "string", "enum": ["adm", "cgv", "clt"]}, "type": "array", "title": "Roles"}}, "type": "object", "required": ["email", "roles"], "title": "UserCreateReq"}, "UserResponse": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}, "userId": {"type": "integer", "title": "Userid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}, "roles": {"items": {"$ref": "#/components/schemas/RoleResponse"}, "type": "array", "title": "Roles"}}, "type": "object", "required": ["email", "userId", "createdAt", "updatedAt", "roles"], "title": "UserResponse"}, "UserSearchPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/UserResponse"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "UserSearchPaginationResponse"}, "UserUpdateReq": {"properties": {"email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "avatarUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatarurl"}, "roles": {"anyOf": [{"items": {"type": "string", "enum": ["adm", "cgv", "clt"]}, "type": "array"}, {"type": "null"}], "title": "Roles"}}, "type": "object", "title": "UserUpdateReq"}, "UsersGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/UserResponse"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "UsersGetAllPaginationResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/users-api/"}], "security": [{"oauth2": []}]}