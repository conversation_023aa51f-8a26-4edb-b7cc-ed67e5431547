/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** MobilityEnum */
export enum MobilityEnum {
  INDEPENDENT = "INDEPENDENT",
  INDEPENDENTWITHASSISTIVEDEVICE = "INDEPENDENT WITH ASSISTIVE DEVICE",
  ASSISTED = "ASSISTED",
  WHEELCHAIR = "WHEELCHAIR",
  BEDBOUND = "BEDBOUND",
}

/** GenderEnum */
export enum GenderEnum {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

/** CaregiverServiceRequestResponse */
export interface CaregiverServiceRequestResponse {
  /** Caregiverid */
  caregiverId: number;
  /** Firstname */
  firstName: string;
  /** Lastname */
  lastName: string;
  /** Phone */
  phone?: string | null;
}

/**
 * Client
 * Model returned from DB (e.g. INSERT … RETURNING * or SELECT)
 */
export interface Client {
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone: string;
  /** Email */
  email?: string | null;
  /** Userid */
  userId?: number | null;
  /** Street */
  street?: string | null;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Clientid */
  clientId: number;
  /** Createdat */
  createdAt?: string | null;
  /** Updatedat */
  updatedAt?: string | null;
}

/**
 * ClientCreate
 * Payload for POST /clients — required fields per SQL (NOT NULL)
 */
export interface ClientCreate {
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone: string;
  /** Email */
  email?: string | null;
  /** Userid */
  userId?: number | null;
  /** Street */
  street?: string | null;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
}

/**
 * ClientUpdate
 * PATCH/PUT payload – all fields optional (dump with exclude_unset=True)
 */
export interface ClientUpdate {
  /** Firstname */
  firstName: string | null;
  /** Lastname */
  lastName: string | null;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone: string | null;
  /** Email */
  email?: string | null;
  /** Userid */
  userId?: number | null;
  /** Street */
  street?: string | null;
  /** City */
  city: string | null;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
}

/** ClientWithHistory */
export interface ClientWithHistory {
  /**
   * Firstname
   * @minLength 1
   */
  firstName: string;
  /**
   * Lastname
   * @minLength 1
   */
  lastName: string;
  /** Dateofbirth */
  dateOfBirth?: string | null;
  gender?: GenderEnum | null;
  /** Nationalid */
  nationalId?: string | null;
  /** Phone */
  phone: string;
  /** Email */
  email?: string | null;
  /** Userid */
  userId?: number | null;
  /** Street */
  street?: string | null;
  /** City */
  city: string;
  /** Postalcode */
  postalCode?: string | null;
  /** Geolat */
  geoLat?: number | null;
  /** Geolng */
  geoLng?: number | null;
  /** Medicalhistory */
  medicalHistory?: string | null;
  /** Medications */
  medications?: string[] | null;
  /** Allergies */
  allergies?: string[] | null;
  mobility?: MobilityEnum | null;
  /** Preferredlanguage */
  preferredLanguage?: string | null;
  /** Favoritecaregivers */
  favoriteCaregivers?: string[] | null;
  /** Notes */
  notes?: string | null;
  /**
   * Active
   * @default true
   */
  active?: boolean;
  /** Clientid */
  clientId: number;
  /** Createdat */
  createdAt?: string | null;
  /** Updatedat */
  updatedAt?: string | null;
  /** Visits */
  visits: VisitHistory[];
}

/**
 * ClientsGetAllPaginationResponse
 * Client with pagination
 */
export interface ClientsGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: Client[];
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/**
 * Service
 * Model representing a Homecare Service offering.
 */
export interface Service {
  /**
   * Serviceid
   * Unique identifier for the service (PK of services table)
   */
  serviceId: number;
  /**
   * Name
   * Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')
   */
  name: string;
  /**
   * Description
   * Detailed description of the service offering
   */
  description?: string | null;
  /**
   * Servicetypeid
   * ID of the service type (foreign key to ServiceType table)
   */
  serviceTypeId: number;
  /**
   * Estimatedtimeminutes
   * Estimated time required to perform the service in minutes
   * @default 0
   */
  estimatedTimeMinutes?: number | null;
  /**
   * Costineuros
   * Estimated cost of the service in Euros
   */
  costInEuros?: number | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

/** VisitHistory */
export interface VisitHistory {
  /** Visitid */
  visitId: number;
  /** Caregivers */
  caregivers?: CaregiverServiceRequestResponse[] | null;
  /** Starttime */
  startTime?: string | null;
  /** Endtime */
  endTime?: string | null;
  /** Completedat */
  completedAt?: string | null;
  /** Createdat */
  createdAt?: string | null;
  /** Updatedat */
  updatedAt?: string | null;
  /** Services */
  services?: Service[] | null;
  /** Attachments */
  attachments?: any[] | null;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/clients-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title clients
 * @version 1.0.0
 * @baseUrl /api/v1/clients-api/
 *
 *
 * Microservice for managing home care clients.
 *
 * ## Features
 * * Create, read, update, and delete client records
 * * Manage client addresses and emergency contacts
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  clients = {
    /**
     * @description Retrieve a list of all clients with pagination support
     *
     * @tags Clients
     * @name GetClientsClientsGet
     * @summary Get all clients
     * @request GET:/clients
     * @secure
     */
    getClientsClientsGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<ClientsGetAllPaginationResponse, HTTPValidationError | void>(
        {
          path: `/clients`,
          method: "GET",
          query: query,
          secure: true,
          format: "json",
          ...params,
        },
      ),

    /**
     * @description Create a new client record with address and optional emergency contact
     *
     * @tags Clients
     * @name CreateClientClientsPost
     * @summary Create new client
     * @request POST:/clients
     * @secure
     */
    createClientClientsPost: (data: ClientCreate, params: RequestParams = {}) =>
      this.request<Client, HTTPValidationError | void>({
        path: `/clients`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Search for clients by name or phone number with pagination support.
     *
     * @tags Clients
     * @name SearchClientClientsSearchGet
     * @summary Search clients
     * @request GET:/clients/search
     * @secure
     */
    searchClientClientsSearchGet: (
      query?: {
        /**
         * Query
         * Free text query
         */
        query?: string | null;
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<ClientsGetAllPaginationResponse, void | HTTPValidationError>(
        {
          path: `/clients/search`,
          method: "GET",
          query: query,
          secure: true,
          format: "json",
          ...params,
        },
      ),

    /**
     * @description Retrieve a specific client by their ID
     *
     * @tags Clients
     * @name GetClientClientsClientIdGet
     * @summary Get client by ID
     * @request GET:/clients/{client_id}
     * @secure
     */
    getClientClientsClientIdGet: (
      clientId: number,
      params: RequestParams = {},
    ) =>
      this.request<Client, void | HTTPValidationError>({
        path: `/clients/${clientId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing client's information
     *
     * @tags Clients
     * @name UpdateClientClientsClientIdPut
     * @summary Update client
     * @request PUT:/clients/{client_id}
     * @secure
     */
    updateClientClientsClientIdPut: (
      clientId: number,
      data: ClientUpdate,
      params: RequestParams = {},
    ) =>
      this.request<Client, void | HTTPValidationError>({
        path: `/clients/${clientId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a client record by ID
     *
     * @tags Clients
     * @name DeleteClientClientsClientIdDelete
     * @summary Delete client
     * @request DELETE:/clients/{client_id}
     * @secure
     */
    deleteClientClientsClientIdDelete: (
      clientId: string,
      params: RequestParams = {},
    ) =>
      this.request<void, void | HTTPValidationError>({
        path: `/clients/${clientId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Retrieve a client along with their complete visit history, with optional date range filtering
     *
     * @tags Clients
     * @name GetClientHistoryClientsHistoryClientIdGet
     * @summary Get a client with their full visit history
     * @request GET:/clients/history/{client_id}
     * @secure
     */
    getClientHistoryClientsHistoryClientIdGet: (
      clientId: string,
      query?: {
        /**
         * Start Date
         * Filter visit history on or after this date
         */
        start_date?: string | null;
        /**
         * End Date
         * Filter visit history on or before this date
         */
        end_date?: string | null;
      },
      params: RequestParams = {},
    ) =>
      this.request<ClientWithHistory, void | HTTPValidationError>({
        path: `/clients/history/${clientId}`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),
  };
  byUser = {
    /**
     * @description Retrieve a client by their user ID
     *
     * @tags Clients
     * @name GetClientByUserIdByUserUserIdGet
     * @summary Get client by user ID
     * @request GET:/by-user/{user_id}
     * @secure
     */
    getClientByUserIdByUserUserIdGet: (
      userId: number,
      params: RequestParams = {},
    ) =>
      this.request<Client, void | HTTPValidationError>({
        path: `/by-user/${userId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
}
