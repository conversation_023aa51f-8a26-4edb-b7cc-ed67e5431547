import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/caregiver_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayjs from 'dayjs';
import { memo, useEffect, useState } from 'react';
import { FieldErrors, FieldValues, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import AvailabilityCalendar from './components/AvailabilityCalendar';
import FiltersPanel from './components/FiltersPanel';

type AvailabilityFormValues = {
  patient: { clientId: string } | null;
  address: string;
  service: Service[];
  caregiver: { caregiverId: string } | null;
  duration: number;
  fromDate: string;
  toDate: string;
};

// Example notification function

const AvailabilityContainer = () => {
  const [duration, setDuration] = useState(30);
  const [currentDate, setCurrentDate] = useState(dayjs());
  const { openNotification } = useNotifications();
  const setCaregivers = useSchedulingStore((state) => state.setCaregivers);

  const methods = useForm<AvailabilityFormValues>({
    defaultValues: {
      patient: undefined,
      address: '',
      service: undefined,
      caregiver: undefined,
      duration: duration,
    },
  });

  const { handleSubmit } = methods;

  const onSubmit: SubmitHandler<AvailabilityFormValues> = async (data) => {
    console.log('data', data);
    openNotification('topRight', {
      title: 'Form Submitted',
      description: 'The availability form was submitted successfully.',
      type: 'Success',
    });
    // Your API logic here...
  };

  const onError = (errors: FieldErrors<FieldValues>) => {
    console.log('form errors', errors);
    openNotification('topRight', {
      title: 'Form Error',
      description: 'Please fix the errors in the form before submitting.',
      type: 'Warning',
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet();
        setCaregivers(response.data.data);
      } catch (err) {
        console.error(err);
      }
    };
    fetchData();
  }, [setCaregivers]);

  return (
    <div className="flex flex-row h-screen gap-4">
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit, onError)} className="flex flex-col h-full gap-4 w-full">
          <FiltersPanel duration={duration} setDuration={setDuration} />
          <AvailabilityCalendar duration={duration} currentDate={currentDate} setCurrentDate={setCurrentDate} />
        </form>
      </FormProvider>
    </div>
  );
};

export default memo(AvailabilityContainer);
