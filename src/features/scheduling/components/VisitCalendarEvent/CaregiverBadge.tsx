import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { Badge } from 'antd';

type CaregiverBadgeProps = {
  caregiver?: NonNullable<VisitPopulated['caregivers']>[0];
};

/**
 * Component that displays a caregiver assignment badge
 */
export function CaregiverBadge({ caregiver }: CaregiverBadgeProps) {
  if (!caregiver) {
    return <Badge status="error" text="Δεν έχει ανατεθεί" style={{ fontSize: '12px' }} />;
  }

  return <Badge status="success" text={`${caregiver.firstName} ${caregiver.lastName}`} style={{ fontSize: '12px' }} />;
}
