import { CloseOutlined } from '@ant-design/icons';
import AppAvatar from '@app/components/ui/Avatar';
import { useVisitCaregiverUpdate } from '@feat-scheduling/hooks/useVisitCaregiverUpdate';
import { Popconfirm } from 'antd';

type CaregiverAvatarProps = {
  fullName: string;
  value: string;
  isSelected?: boolean;
  showRemoveButton?: boolean;
  visitId?: number;
  caregiverId?: number;
  onRemoveComplete?: () => void;
};

/**
 * Enhanced caregiver avatar that wraps AppAvatar with optional remove functionality
 */
export function CaregiverAvatar({
  fullName,
  value,
  isSelected = true,
  showRemoveButton = false,
  visitId,
  caregiverId,
  onRemoveComplete,
}: CaregiverAvatarProps) {
  const { removeCaregiverFromVisit, isLoading } = useVisitCaregiverUpdate();

  const handleRemoveCaregiver = async () => {
    if (!visitId || !caregiverId) return;

    try {
      await removeCaregiverFromVisit(visitId, caregiverId);
      onRemoveComplete?.();
    } catch (error) {
      console.error('Failed to remove caregiver:', error);
    }
  };

  return (
    <div className="relative group">
      <AppAvatar isSelected={isSelected} fullName={fullName} value={value} />

      {showRemoveButton && visitId && caregiverId && (
        <Popconfirm
          title="Remove caregiver?"
          description={`Remove ${fullName} from this visit?`}
          onConfirm={handleRemoveCaregiver}
          okText="Remove"
          cancelText="Cancel"
          okButtonProps={{
            danger: true,
            loading: isLoading,
          }}
          placement="top"
        >
          <div
            className="absolute -top-1 -right-1 w-4 h-4 !bg-red-500 !text-white rounded-full flex items-center justify-center text-[8px] shadow-md hover:!bg-red-600 cursor-pointer transition-colors duration-200"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <CloseOutlined />
          </div>
        </Popconfirm>
      )}
    </div>
  );
}
