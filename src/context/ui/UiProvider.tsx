import { JSX, useState } from 'react';
import UiContext from './UiContext';

interface UiContextProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  isSidebarCollapsed: boolean;
  toggleSidebarCollapse: () => void;
}

const defaultState: UiContextProps = {
  isSidebarOpen: true,
  toggleSidebar: () => {},
  isSidebarCollapsed: false,
  toggleSidebarCollapse: () => {},
};

interface SidebarProviderProps {
  children: JSX.Element | JSX.Element[];
}

const UiProvider = ({ children }: SidebarProviderProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(defaultState.isSidebarOpen);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false); // for desktop collapse to icons
  const toggleSidebar = () => {
    setIsSidebarOpen((prev) => !prev);
  };
  const toggleSidebarCollapse = () => setIsSidebarCollapsed((prev) => !prev);
  return (
    <UiContext.Provider value={{ isSidebarOpen, toggleSidebar, toggleSidebarCollapse, isSidebarCollapsed }}>
      {children}
    </UiContext.Provider>
  );
};

export default UiProvider;
