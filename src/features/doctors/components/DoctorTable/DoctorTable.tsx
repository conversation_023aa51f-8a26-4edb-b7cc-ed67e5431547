import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import Table from '@app/features/table/Table';
import { APP_PREFIX, DOCTOR_PAGE, EDIT_DOCTOR, NEW_DOCTOR } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const DoctorTable = () => {
  const navigate = useNavigate();
  const doctors = useSchedulingStore((state) => state.doctors);
  const loadingDoctors = useSchedulingStore((state) => state.loadingDoctors);

  return (
    <div className="w-full h-full">
      <Table
        rowKey="doctorId"
        data={doctors}
        totalData={doctors.length}
        loadingDataFetch={loadingDoctors}
        addNavigate={() => navigate(`/${APP_PREFIX}/${DOCTOR_PAGE}/${NEW_DOCTOR}`)}
        exportComponent={<></>}
        localStorageKey="DoctorTable"
        onRowClick={(record: Doctor) => {
          const path = `/${APP_PREFIX}/${DOCTOR_PAGE}/${EDIT_DOCTOR.replace(':id', `${record.doctorId}`)}`;
          navigate(path, { state: record });
        }}
      />
    </div>
  );
};

export default DoctorTable;
