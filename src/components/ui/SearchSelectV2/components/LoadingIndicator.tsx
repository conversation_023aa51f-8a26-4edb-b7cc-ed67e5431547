import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { memo } from 'react';

interface LoadingIndicatorProps {
  loading: boolean;
}

const LoadingIndicator = memo(({ loading }: LoadingIndicatorProps) => {
  if (!loading) return null;

  return (
    <div className="absolute bottom-0 left-0 flex justify-center items-center gap-2 py-2 text-xs text-gray-500">
      <Spin indicator={<LoadingOutlined spin />} size="small" />
      Loading more...
    </div>
  );
});

LoadingIndicator.displayName = 'LoadingIndicator';

export default LoadingIndicator;
