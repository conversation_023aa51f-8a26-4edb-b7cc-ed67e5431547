import { ApiClient } from '@api/api-configuration';
import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import { Service } from '@api/READ_ONLY/services_api/Api';
import AppAvatar from '@app/components/ui/Avatar';
import { CommonListResponse } from '@app/types/table.types';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { capitalizeFullNameFirst } from '@app/utils/fullNameCapilatizeFirst';
import { DefaultOptionType } from 'antd/es/select';
import { memo, ReactNode, useCallback, useEffect, useState } from 'react';
import FetchSearchSelect from './FetchSelect/ApiFetchSelect';

type CaregiverOptionProps = {
  firstName: string;
  lastName: string;
};

const CaregiverOption = memo(({ firstName, lastName }: CaregiverOptionProps) => {
  const fullname = `${firstName} ${lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: '' });

  return (
    <div className="flex items-center gap-2">
      <AppAvatar isSelected={true} fullName={fullname} value={fullnameLetters} />
      {capitalizeFullNameFirst(firstName, lastName)}
    </div>
  );
});

CaregiverOption.displayName = 'CaregiverOption';

type CaregiverSearchProps = {
  searchTerm?: string;
  debounce?: number;
  selectedServices?: Service[];
  disabled?: boolean;
  setValue: (e: CaregiverWithServices | CaregiverWithServices[] | undefined | string) => void;
  value: CaregiverWithServices | undefined;
  style?: React.CSSProperties;
  optionRender?: (option: DefaultOptionType) => ReactNode;
  optionDisable?: (item: CaregiverWithServices) => boolean;
  query?: {
    offset?: number;
    limit?: number;
    username?: string | null;
    services?: number[] | null;
    active?: boolean | null;
    city?: string | null;
    skills?: string[] | null;
    languages_spoken?: string[] | null;
    rating_min?: number | null;
    rating_max?: number | null;
  };
};

export function CaregiverSearch({
  selectedServices,
  disabled = false,
  setValue,
  value,
  optionRender,
  optionDisable,
  style,
}: CaregiverSearchProps) {
  const [data, setData] = useState<CommonListResponse<CaregiverWithServices>>({
    Results: [],
    TotalResults: 0,
  });

  const fetchCaregivers = useCallback(
    async (searchValue?: string, pageSize?: number, pageIndex?: number) => {
      try {
        let res;

        if (searchValue && searchValue.trim().length >= 3) {
          const searchQuery = {
            query: searchValue,
            ...(pageSize && { limit: pageSize }),
            ...(pageIndex != null && pageSize && { offset: pageIndex * pageSize }),
          };
          res = await ApiClient.caregiverApi.caregivers.searchCaregiversCaregiversSearchGet(searchQuery);
        } else {
          res = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet({
            services: selectedServices?.map((s) => s.serviceId),
          });
        }

        setData({
          Results: res.data.data,
          TotalResults: res.data.total,
        });

        return {
          Results: res.data.data,
          TotalResults: res.data.total,
        } as CommonListResponse<CaregiverWithServices>;
      } catch (err) {
        console.error(err);
        return { Results: [], TotalResults: 0 };
      }
    },
    [selectedServices]
  );

  useEffect(() => {
    fetchCaregivers();
  }, [selectedServices, fetchCaregivers]);

  // Fix: transform value to expected type for FetchSearchSelect
  // If FetchSearchSelect expects a string (id), pass caregiverId as string
  // If it expects an array for multi-select, adapt accordingly
  const selectValue = value ? String(value.caregiverId) : undefined;

  return (
    <FetchSearchSelect<CaregiverWithServices>
      data={data}
      style={style}
      disabled={disabled}
      value={selectValue}
      onValueChange={(item) => setValue(item)}
      getId={(item) => String(item.caregiverId)}
      getLabel={(item) => <CaregiverOption firstName={item.firstName} lastName={item.lastName} />}
      fetchData={fetchCaregivers}
      placeholder="Select Caregiver"
      optionRender={optionRender}
      optionDisable={optionDisable}
    />
  );
}
