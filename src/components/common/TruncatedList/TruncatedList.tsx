import React, { memo } from 'react';

export interface TruncatedListItem {
  id: string | number;
  name: string;
  [key: string]: unknown;
}

interface TruncatedListProps<T extends TruncatedListItem> {
  /** Array of items to display */
  items: T[];
  /** Maximum number of items to show before truncating */
  maxItems?: number;
  /** Function to extract the display text from an item */
  getDisplayText?: (item: T) => string;
  /** Function to extract the unique key from an item */
  getKey?: (item: T) => string | number;
  /** Separator between items */
  separator?: string;
  /** Custom className for styling */
  className?: string;
  /** Custom styling for individual items */
  itemClassName?: string;
  /** Custom styling for the "more" indicator */
  moreClassName?: string;
  /** Custom render function for individual items */
  renderItem?: (item: T, index: number) => React.ReactNode;
  /** Custom render function for the "more" indicator */
  renderMore?: (hiddenCount: number) => React.ReactNode;
}

/**
 * TruncatedList Component
 *
 * A reusable component for displaying a list of items with truncation.
 * Shows the first N items and displays "X more" for remaining items.
 *
 * Features:
 * - Configurable truncation limit
 * - Custom separators and styling
 * - Custom render functions for items and "more" indicator
 * - TypeScript support with generics
 * - Memoized for performance
 *
 * @example
 * ```tsx
 * <TruncatedList
 *   items={services}
 *   maxItems={3}
 *   getDisplayText={(service) => service.name}
 *   getKey={(service) => service.serviceId}
 * />
 * ```
 */
const TruncatedListComponent = <T extends TruncatedListItem>({
  items,
  maxItems = 3,
  getDisplayText = (item) => item.name,
  getKey = (item) => item.id,
  separator = ', ',
  className = '',
  itemClassName = '',
  moreClassName = '',
  renderItem,
  renderMore,
}: TruncatedListProps<T>) => {
  // Return null if no items
  if (!items || items.length === 0) {
    return <span className={className}>-</span>;
  }

  // Get visible items and hidden count
  const visibleItems = items.slice(0, maxItems);
  const hiddenCount = Math.max(0, items.length - maxItems);

  return (
    <span className={className}>
      {visibleItems.map((item, index) => (
        <span key={getKey(item)} className={itemClassName}>
          {renderItem ? renderItem(item, index) : getDisplayText(item)}
          {index < visibleItems.length - 1 && separator}
        </span>
      ))}

      {hiddenCount > 0 && (
        <span className={moreClassName}>{renderMore ? renderMore(hiddenCount) : `…${hiddenCount} more`}</span>
      )}
    </span>
  );
};

// Memoized component with display name for better debugging
TruncatedListComponent.displayName = 'TruncatedList';

// Export memoized component with proper typing
export const TruncatedList = memo(TruncatedListComponent) as <T extends TruncatedListItem>(
  props: TruncatedListProps<T>
) => React.ReactElement;

export default TruncatedList;
