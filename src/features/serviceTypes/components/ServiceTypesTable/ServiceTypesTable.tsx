import { ApiClient } from '@api/api-configuration';
import { ServiceTypeCreate } from '@api/READ_ONLY/services_api/Api';
import Table from '@app/features/table/Table';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Button, Input, Modal } from 'antd';
import { useState } from 'react';

type ServiceTypeModalState = {
  open: boolean;
  data: Partial<ServiceTypeCreate> & { serviceTypeId?: number };
};

const ServiceTypeTable = () => {
  const [isModalOpen, setIsModalOpen] = useState<ServiceTypeModalState>({
    open: false,
    data: {},
  });
  const serviceTypes = useSchedulingStore((state) => state.serviceTypes);
  const loading = useSchedulingStore((state) => state.loadingServiceTypes);
  const setServiceTypes = useSchedulingStore((state) => state.setServiceTypes);
  const { openNotification } = useNotifications();

  const openModal = (record?: ServiceTypeCreate) => {
    console.log('record', record);
    setIsModalOpen({ open: true, data: record || {} });
  };

  const onSubmit = async (formData: Partial<ServiceTypeCreate> & { serviceTypeId?: number }) => {
    try {
      if (formData.serviceTypeId) {
        // update
        await ApiClient.serviceApi.serviceTypes.updateServiceTypeServiceTypesServiceTypeIdPut(
          formData.serviceTypeId,
          formData as ServiceTypeCreate
        );

        openNotification('topRight', {
          title: 'Service Type',
          description: 'Service type updated successfully.',
          type: 'Success',
        });
      } else {
        // create
        await ApiClient.serviceApi.serviceTypes.createServiceTypeServiceTypesPost(formData as ServiceTypeCreate);

        openNotification('topRight', {
          title: 'Service Type',
          description: 'Service type created successfully.',
          type: 'Success',
        });
      }

      setIsModalOpen({ open: false, data: {} });
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: 'Service Type',
          description: 'Operation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    } finally {
      const response = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
      setServiceTypes(response.data.data);
    }
  };

  return (
    <div className="w-full h-full">
      <Modal
        title={isModalOpen.data.serviceTypeId ? 'Edit Service Type' : 'Create New Service Type'}
        open={isModalOpen.open}
        onCancel={() => setIsModalOpen({ open: false, data: {} })}
        footer={
          <Button color="primary" onClick={() => onSubmit(isModalOpen.data)}>
            {isModalOpen.data.serviceTypeId ? 'Update' : 'Create'}
          </Button>
        }
      >
        <div className="flex gap-5 flex-col">
          <Input
            size="large"
            placeholder="Name"
            value={isModalOpen.data.name ?? ''}
            onChange={(e) =>
              setIsModalOpen((prev) => ({
                ...prev,
                data: { ...prev.data, name: e.target.value },
              }))
            }
          />
          <Input.TextArea
            className="mt-4"
            placeholder="Description"
            value={isModalOpen.data.description ?? ''}
            onChange={(e) =>
              setIsModalOpen((prev) => ({
                ...prev,
                data: { ...prev.data, description: e.target.value },
              }))
            }
          />
        </div>
      </Modal>

      <Table
        rowKey={'serviceTypeId'}
        data={serviceTypes}
        totalData={serviceTypes.length}
        loadingDataFetch={loading}
        addNavigate={() => openModal()}
        exportComponent={<></>}
        localStorageKey="ServiceTypeTable"
        onRowClick={(record) => openModal(record)}
      />
    </div>
  );
};

export default ServiceTypeTable;
