import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { MenuItem } from './types';

/**
 * Custom hook to manage menu state (open/closed items)
 * Handles the logic for opening parent menus when a child is active
 */
export const useMenuState = (items: MenuItem[], pathname: string) => {
  const [openItems, setOpenItems] = useState<Record<string | number, boolean>>({});

  /**
   * Recursively opens parent menu items if they contain the active route
   * @param menuItems - Array of menu items to check
   * @param currentPath - Current route pathname
   * @returns True if any item or child contains the active path
   */
  const openParentsOfActive = useCallback((menuItems: MenuItem[], currentPath: string): boolean => {
    let hasActiveChild = false;

    for (const item of menuItems) {
      // If this item is the active route, return true
      if (item.key === currentPath) return true;

      // If this item has children, check them recursively
      if (item.children?.length) {
        const childHasActive = openParentsOfActive(item.children, currentPath);
        if (childHasActive) {
          // Open this parent item since it contains an active child
          setOpenItems((prev) => ({ ...prev, [item.key]: true }));
          hasActiveChild = true;
        }
      }
    }
    return hasActiveChild;
  }, []);

  // Effect to open parent menus when pathname changes
  useEffect(() => {
    openParentsOfActive(items, pathname);
  }, [items, openParentsOfActive, pathname]);

  /**
   * Toggles the open/closed state of a menu item
   * @param key - The key of the menu item to toggle
   */
  const toggleItem = useCallback((key: string | number) => {
    setOpenItems((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  return { openItems, toggleItem };
};

/**
 * Custom hook for navigation functionality
 * Provides navigation methods and current pathname
 */
export const useNavigation = () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();

  /**
   * Navigates to a new route only if it's different from the current one
   * @param pathKey - The route key to navigate to
   */
  const navigateIfDifferent = useCallback(
    (pathKey: string | number) => {
      const target = String(pathKey);
      if (pathname !== target) {
        navigate(target);
      }
    },
    [pathname, navigate]
  );

  return { pathname, navigateIfDifferent };
};
