import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Tag, Typography } from 'antd';

export type DoctorTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: unknown, record: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getDoctorTableColumns = (): DoctorTableColumnType<Doctor>[] => [
  {
    key: 'Name',
    title: 'Name',
    width: 200,
    render: (_, doctor) => <ClientDisplay firstName={doctor.firstName || ''} lastName={doctor.lastName || ''} />,
  },
  {
    dataIndex: 'email',
    key: 'email',
    title: 'Email',
    width: 200,
    render: (email) => <Typography.Text>{(email as string) || '-'}</Typography.Text>,
  },
  {
    dataIndex: 'phone',
    key: 'phone',
    title: 'Phone',
    width: 150,
    render: (phone) => <Typography.Text>{(phone as string) || '-'}</Typography.Text>,
  },
  {
    dataIndex: 'licenseNumber',
    key: 'licenseNumber',
    title: 'License Number',
    width: 150,
    render: (license) => <Typography.Text>{(license as string) || '-'}</Typography.Text>,
  },
  {
    dataIndex: 'isActive',
    key: 'isActive',
    title: 'Status',
    width: 100,
    align: 'center',
    render: (isActive) => <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Active' : 'Inactive'}</Tag>,
  },
];
