# React + TypeScript + Vite Tempalte

## Commit Message Convention

We use **Conventional Commits** to keep our git history readable, enable changelog automation, and enforce consistent commit messages across the team.

### Allowed Prefixes (Types)

Use one of the following types as the prefix in your commit message:

- `feat`: a new feature
- `fix`: a bug fix
- `docs`: changes to documentation only
- `style`: formatting only (no logic changes)
- `refactor`: refactoring code without behavior change
- `test`: adding or updating tests
- `chore`: maintenance, tooling, or dependencies
- `perf`: performance improvements

### Commit Format

````bash
<type>(optional-scope): <short summary>

<optional detailed description (at least 20 characters)>

``` feat(auth): add password reset flow

Implements backend token validation and reset email support.
````
