import { Spin } from 'antd';
import { useAuth } from 'react-oidc-context';

export const ProtectedWrapper = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuth();
  console.log('Auth state:', auth);

  if (auth.isLoading)
    return (
      <div className="justify-center items-center w-screen h-screen flex">
        <Spin spinning={true} />
      </div>
    );

  if (auth.error) {
    console.error(auth.error);
    localStorage.removeItem('oidc_access_token');
    return <div>Authentication error</div>;
  }

  if (!auth.isAuthenticated) {
    localStorage.removeItem('oidc_access_token');
    auth.signinRedirect(); // <--- this triggers redirect to Keycloak
    return null;
  }
  localStorage.setItem('oidc_access_token', auth.user?.access_token || '');
  return <>{children}</>;
};
