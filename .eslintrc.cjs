// eslint-disable-next-line no-undef
module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'react-hooks', 'prettier'],
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'prettier',
    'plugin:storybook/recommended',
  ],
  settings: {
    react: {
      version: 'detect',
    },
  },
  env: {
    browser: true,
  },
  rules: {
    'prettier/prettier': 'error', // Για να δίνει error αν δεν είναι formatted
    'react/react-in-jsx-scope': 'off', // Δεν χρειάζεται react import σε Vite + React 17+
    '@typescript-eslint/explicit-function-return-type': 'off', // Αν δεν θες πάντα να δηλώνεις return types
    '@typescript-eslint/no-explicit-any': 'error', // Προειδοποίηση για χρήση any
    'react-hooks/exhaustive-deps': 'error', // Για τα deps του useEffect
    'react/no-array-index-key': 'error',
  },
};
