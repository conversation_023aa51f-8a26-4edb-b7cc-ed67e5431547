import { TAllowIf, TPermissions } from '@feat-auth/types';
import { createContext } from 'react';

export type UserContextType = {
  email: string;
  name: string;
  fullname: string;
  userId: number;
};

export type AuthContextProps = {
  auth: string | null;
  removeAuthentication: () => void;
  allowIf: TAllowIf;
  logout: () => void;
  permissions: TPermissions;
  user: UserContextType;
};

export const defaultUser: UserContextType = {
  email: '',
  name: '',
  fullname: '',
  userId: -1,
};

export const initAuthContextPropsState: AuthContextProps = {
  auth: '',
  removeAuthentication: () => {},
  allowIf: () => false,
  logout: () => {},
  permissions: [],
  user: defaultUser,
};

export const AuthContext = createContext<AuthContextProps>(initAuthContextPropsState);
AuthContext.displayName = 'Auth';
