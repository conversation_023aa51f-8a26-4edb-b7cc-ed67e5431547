import { JSX, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const RequireAuth = ({ children }: { children: JSX.Element }) => {
  // const { removeAuthentication } = useAuthContext();
  // const { openNotification } = useNotifications();
  const location = useLocation();

  useEffect(() => {
    // 🔒 prevent double handling
    const preventChecking = ['auth/callback', '404'];
    if (preventChecking.some((path) => location.pathname.includes(path))) {
      return;
    }
    //! ACTIVATE THIS FOR AUTH
    // if (IsTokenExpired()) {
    //   openNotification('topRight', {
    //     title: 'Session Ended',
    //     description: 'Your session has ended. Please sign in again to continue.',
    //     type: 'Warning',
    //   });

    //   removeAuthentication();
    // }
  }, [location.pathname]);

  return children;

  //! ACTIVATE THIS FOR AUTH
  // return IsTokenExpired() ? <></> : children;
};

export default RequireAuth;
