import { AntdIconProps } from '@ant-design/icons/lib/components/AntdIcon';
import { ForwardRefExoticComponent, RefAttributes } from 'react';

export interface MenuItem {
  label: string; // The display label of the menu item
  key: number; // Unique key for the menu item, used for identification
  show: boolean; // Whether the item should be displayed
  icon?: ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & RefAttributes<HTMLSpanElement>>;
  children?: MenuItem[]; // Optional nested menu items for submenus
}

export type ScrollProps = {
  forceVisible: boolean;
  autoHide: boolean;
  className: string;
};
