import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import { ResponsiveFiltersBar } from './ResponsiveFilterBar';

describe('ResponsiveFiltersBar', () => {
  it('renders mobile filter drawer and opens it on click', () => {
    global.innerWidth = 500; // simulate mobile
    const { getByRole, getByText } = render(<ResponsiveFiltersBar />);

    // Simulate Filter button click
    const filterButton = getByRole('button', { name: /filter/i });
    fireEvent.click(filterButton);

    expect(getByText('Filters')).toBeInTheDocument(); // drawer title
  });

  it('renders meModeFilter and assigneesFilter when provided', () => {
    render(
      <ResponsiveFiltersBar
        meModeFilter={<div data-testid="me-mode">MeMode</div>}
        assigneesFilter={<div data-testid="assignees">Assignees</div>}
      />
    );

    expect(screen.getByTestId('me-mode')).toBeInTheDocument();
    expect(screen.getByTestId('assignees')).toBeInTheDocument();
  });

  // it('calls addNavigate when Add button is clicked', () => {
  //   const mockAddNavigate = jest.fn();
  //   render(<ResponsiveFiltersBar hasAdd={true} addNavigate={mockAddNavigate} />);

  //   const addButton = screen.getByRole('button', { name: /add/i });
  //   fireEvent.click(addButton);

  //   expect(mockAddNavigate).toHaveBeenCalled();
  // });

  // it('calls handleReset when Reset button is clicked', () => {
  //   global.innerWidth = 500; // simulate mobile
  //   const mockReset = jest.fn();
  //   const { getByRole } = render(<ResponsiveFiltersBar handleReset={mockReset} />);

  //   fireEvent.click(getByRole('button', { name: /filter/i }));
  //   // fireEvent.click(getByText(/reset filters/i).parentElement?.querySelector('button')!);

  //   expect(mockReset).toHaveBeenCalled();
  // });

  // it('toggles column customisation when toggle is clicked', () => {
  //   const mockSetOpen = jest.fn();
  //   render(<ResponsiveFiltersBar columnFilters openCustomisation={false} setOpenCustomisation={mockSetOpen} />);

  //   fireEvent.click(screen.getByRole('button', { name: /toggle columns/i }));
  //   expect(mockSetOpen).toHaveBeenCalledWith(true);
  // });
});
