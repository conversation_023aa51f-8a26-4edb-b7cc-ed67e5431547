import { exec } from 'child_process';
import fs from 'fs';
import fsp from 'fs/promises';
import http from 'http';
import https from 'https';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const OUTPUT_ROOT = path.resolve(__dirname, 'src/api/READ_ONLY');
// const BASE_API_URL = 'https://test.disco.inlecom.gr:7443';

// const apiTargets: { url: string; name: string }[] = [
//   { url: `${BASE_API_URL}/client-api/openapi.json`, name: 'client_api' },
//   { url: `${BASE_API_URL}/caregiver-api/openapi.json`, name: 'caregiver_api' },
//   { url: `${BASE_API_URL}/service-api/openapi.json`, name: 'services_api' },
//   { url: `${BASE_API_URL}/visit-api/openapi.json`, name: 'visits_api' },
//   { url: `${BASE_API_URL}/service-request-api/openapi.json`, name: 'service_request_api' },
//   { url: `${BASE_API_URL}/user-api/openapi.json`, name: 'users_api' },
//   { url: `${BASE_API_URL}/notification-api/openapi.json`, name: 'notifications_api' },
//   { url: `${BASE_API_URL}/doctor-api/openapi.json`, name: 'doctors_api' },
// ];
const BASE_API_URL = 'https://test.disco.inlecom.gr:7443/api/v1';

const apiTargets: { url: string; name: string }[] = [
  { url: `${BASE_API_URL}/clients-api/openapi.json`, name: 'client_api' },
  { url: `${BASE_API_URL}/caregivers-api/openapi.json`, name: 'caregiver_api' },
  { url: `${BASE_API_URL}/services-api/openapi.json`, name: 'services_api' },
  { url: `${BASE_API_URL}/visits-api/openapi.json`, name: 'visits_api' },
  { url: `${BASE_API_URL}/requests-api/openapi.json`, name: 'service_request_api' },
  { url: `${BASE_API_URL}/users-api/openapi.json`, name: 'users_api' },
  { url: `${BASE_API_URL}/notifications-api/openapi.json`, name: 'notifications_api' },
  { url: `${BASE_API_URL}/doctors-api/openapi.json`, name: 'doctors_api' },
];
// Download file helper
function downloadFile(url: string, dest: string): Promise<void> {
  const proto = url.startsWith('https') ? https : http;

  return new Promise((resolve, reject) => {
    proto
      .get(url, (res) => {
        if (!res || res.statusCode !== 200) {
          res?.resume();
          return reject(new Error(`Failed to download. Status code: ${res?.statusCode}`));
        }

        const fileStream = fs.createWriteStream(dest);
        res.pipe(fileStream);

        fileStream.on('finish', () => {
          fileStream.close();
          resolve();
        });

        fileStream.on('error', (err: NodeJS.ErrnoException) => {
          fs.unlink(dest, () => reject(err));
        });
      })
      .on('error', (err: Error) => reject(err));
  });
}

// Promisified exec
function execPromise(cmd: string): Promise<{ stdout: string; stderr: string }> {
  return new Promise((resolve, reject) => {
    exec(cmd, (error, stdout, stderr) => {
      if (error) reject(error);
      else resolve({ stdout, stderr });
    });
  });
}

async function generateApiClient(apiName: string, openapiUrl: string): Promise<void> {
  const apiDir = path.join(OUTPUT_ROOT, apiName);
  await fsp.mkdir(apiDir, { recursive: true });

  const openapiPath = path.join(apiDir, 'openapi.json');
  console.log(`[${apiName}] Downloading OpenAPI spec from: ${openapiUrl}`);
  await downloadFile(openapiUrl, openapiPath);
  console.log(`[${apiName}] Downloaded spec to: ${openapiPath}`);

  const cmd = `npx swagger-typescript-api generate -p "${openapiPath}" -o "${apiDir}" --axios`;
  console.log(`[${apiName}] Running swagger-typescript-api...`);
  const { stdout, stderr } = await execPromise(cmd);

  if (stderr) console.error(`[${apiName}] stderr:`, stderr);
  console.log(`[${apiName}] swagger-typescript-api output:\n${stdout}`);

  const indexFile = path.join(apiDir, 'index.ts');
  const renamedFile = path.join(apiDir, `${apiName}.api.ts`);
  try {
    await fsp.access(indexFile, fs.constants.F_OK);
    await fsp.rename(indexFile, renamedFile);
    console.log(`[${apiName}] Renamed index.ts to ${apiName}.api.ts`);
  } catch {
    console.warn(`[${apiName}] No index.ts file found to rename.`);
  }
}

async function main(): Promise<void> {
  try {
    await fsp.mkdir(OUTPUT_ROOT, { recursive: true });
    for (const { url, name } of apiTargets) {
      await generateApiClient(name, url);
    }
    console.log('✅ All API clients generated successfully.');
  } catch (error) {
    console.error('❌ Error during generation:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

main();
