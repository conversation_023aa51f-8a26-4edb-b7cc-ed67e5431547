import { Client } from '@api/READ_ONLY/client_api/Api';
import { FREQUENCY_OPTIONS, REPETITION_EVENTS } from '@feat-service-requests/const';
import {
  DateTimeRecurrenceValues,
  RecurringEventValues,
  ServiceRequestExtraDetails,
} from '@feat-service-requests/types';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';

const createInitialTimes = () => {
  const base = dayjs().minute(0).second(0).millisecond(0);
  return { fromTime: base, toTime: base.add(1, 'hour') };
};

const createDefaultDateTime = (): DateTimeRecurrenceValues => {
  const { fromTime, toTime } = createInitialTimes();

  return {
    startingDate: dayjs(),
    endingDate: null,
    repeat: REPETITION_EVENTS.NEVER,
    frequency: FREQUENCY_OPTIONS.DAILY,
    interval: 1,
    repetitionDaysOfTheWeek: [],
    fromTime,
    toTime,
  } as const;
};

const makeDefaultExtra = (): ServiceRequestExtraDetails => ({ services: [] }) as const;

// Narrow, explicit setters for the date-time partition:
// type DateTimeAllowedUpdates = Pick<DateTimeRecurrenceValues, 'startingDate' | 'repeat'>;

const useServiceRequestDetails = () => {
  // Separate states (keeps rerenders scoped)
  const [patientDetails, setPatientDetails] = useState<Client | undefined>(undefined);
  const [notes, setNotes] = useState<string>('');

  const [dateTimeRecurrenceValues, setDateTimeRecurrenceValues] =
    useState<DateTimeRecurrenceValues>(createDefaultDateTime);

  const [serviceRequestExtraDetails, setServiceRequestExtraDetails] =
    useState<ServiceRequestExtraDetails>(makeDefaultExtra);

  const updateDateTimeOnlyRecurrenceValues = useCallback(
    (value: RecurringEventValues) => setDateTimeRecurrenceValues((prev) => ({ ...prev, ...value })),
    []
  );

  const updateDateTimeRecurrenceValuesByKey = useCallback(
    <K extends keyof DateTimeRecurrenceValues>(key: K, value: DateTimeRecurrenceValues[K]) => {
      setDateTimeRecurrenceValues((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  const updateNotes = useCallback((value: string) => setNotes(value), []);
  const updatePatientDetails = useCallback((value: Client | undefined) => setPatientDetails(value), []);

  const updateServiceRequestExtraDetailsByKey = useCallback(
    <K extends keyof ServiceRequestExtraDetails>(key: K, value: ServiceRequestExtraDetails[K]) => {
      setServiceRequestExtraDetails((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  const isValidToCreateANewAAServiceRequest =
    serviceRequestExtraDetails.services.length > 0 &&
    Boolean(patientDetails?.clientId) &&
    Boolean(dateTimeRecurrenceValues.startingDate) &&
    Boolean(dateTimeRecurrenceValues.fromTime) &&
    Boolean(dateTimeRecurrenceValues.toTime);

  return {
    isValidToCreateANewAAServiceRequest,
    dateTime: {
      values: dateTimeRecurrenceValues,
      updateDateTimeOnlyRecurrenceValues,
      updateDateTimeRecurrenceValuesByKey,
      resetToDefault: () => setDateTimeRecurrenceValues(createDefaultDateTime),
    },
    notes: {
      value: notes,
      updateValue: updateNotes,
      resetToDefault: () => setNotes(''),
    },
    patient: {
      value: patientDetails,
      updateValue: updatePatientDetails,
      resetToDefault: () => setPatientDetails(undefined),
    },
    serviceRequestExtraDetails: {
      value: serviceRequestExtraDetails,
      updateValueByKey: updateServiceRequestExtraDetailsByKey,
    },
  };
};

export default useServiceRequestDetails;
