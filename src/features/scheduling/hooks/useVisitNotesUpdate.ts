import { ApiClient } from '@api/api-configuration';
import { useMessage } from '@context/message';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useState } from 'react';

export const useVisitNotesUpdate = () => {
  const [isLoading, setIsLoading] = useState(false);
  const updateVisitInStore = useSchedulingStore((state) => state.updateVisit);
  const { showSuccess, showError } = useMessage();

  const updateVisitNotes = async (visitId: number, notes: string) => {
    console.log({ visitId, notes });
    setIsLoading(true);
    try {
      await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(visitId, { notes });

      const currentVisits = useSchedulingStore.getState().visits;
      const currentVisit = currentVisits.find((v) => v.id === visitId);

      if (currentVisit) {
        updateVisitInStore({
          id: visitId,
          serviceRequest: {
            ...currentVisit.serviceRequest,
            notes,
          },
        });
      }

      // Success feedback
      showSuccess('Notes updated successfully');
    } catch (error) {
      showError('Failed to update notes');
      console.error('Notes update error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    updateVisitNotes,
    isLoading,
  };
};
