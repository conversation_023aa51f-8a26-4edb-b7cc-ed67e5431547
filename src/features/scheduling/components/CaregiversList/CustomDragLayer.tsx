import AppAvatar from '@app/components/ui/Avatar';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { useRef } from 'react';
import { useDragLayer } from 'react-dnd';

const CustomDragLayer = () => {
  const { isDragging, item, currentOffset } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
  }));
  const avatarRef = useRef<HTMLDivElement>(null);

  if (!isDragging || !item?.caregiver) {
    return null;
  }
  const { caregiver } = item;
  const fullname = `${caregiver.firstName} ${caregiver.lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: '' });

  const width = avatarRef.current?.offsetWidth || 0;
  const height = avatarRef.current?.offsetHeight || 0;

  return (
    <div
      style={{
        position: 'fixed',
        pointerEvents: 'none',
        zIndex: 9999,
        left: (currentOffset?.x || 0) + width / 2,
        top: (currentOffset?.y || 0) + height / 2,
        opacity: 1,
      }}
    >
      <div
        ref={avatarRef}
        className="visit-item rounded-2xl px-2 cursor-move transition flex gap-2 items-center py-4 bg-app-gray-light text-app-text shadow-lg"
      >
        <AppAvatar isSelected={true} fullName={fullname} value={fullnameLetters} />
      </div>
    </div>
  );
};

export default CustomDragLayer;
