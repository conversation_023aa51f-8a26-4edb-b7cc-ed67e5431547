import { CheckOutlined, CloseOutlined, EditOutlined } from '@ant-design/icons';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { useMessage } from '@context/message';
import { Button, DatePicker, TimePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useState } from 'react';

type VisitTimeEditorProps = {
  visit: VisitPopulated;
  onTimeUpdate?: (visitId: number, startTime: string, endTime: string) => Promise<void>;
};

/**
 * Editable time range component
 * Shows formatted time by default, becomes editable on hover/click
 */
export function VisitTimeEditor({ visit, onTimeUpdate }: VisitTimeEditorProps) {
  const { showError } = useMessage();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs(visit.startTime));
  const [startTime, setStartTime] = useState<Dayjs>(dayjs(visit.startTime));
  const [endTime, setEndTime] = useState<Dayjs>(dayjs(visit.endTime));

  // Calculate initial duration between start and end time
  const [originalDuration, setOriginalDuration] = useState(() => {
    return dayjs(visit.endTime).diff(dayjs(visit.startTime), 'minute');
  });

  const handleEdit = () => {
    setIsEditing(true);
    // Update original duration when entering edit mode
    setOriginalDuration(endTime.diff(startTime, 'minute'));
  };

  // Handle start time change and auto-adjust end time
  const handleStartTimeChange = (time: Dayjs | null) => {
    if (!time) return;

    setStartTime(time);
    // Automatically adjust end time to maintain the same duration
    const newEndTime = time.add(originalDuration, 'minute');
    setEndTime(newEndTime);
  };

  // Disable minutes that are not 00, 15, 30, 45
  const disabledMinutes = () => {
    const allowedMinutes = [0, 15, 30, 45];
    const disabledList: number[] = [];

    for (let i = 0; i < 60; i++) {
      if (!allowedMinutes.includes(i)) {
        disabledList.push(i);
      }
    }
    return disabledList;
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset to original values
    setSelectedDate(dayjs(visit.startTime));
    setStartTime(dayjs(visit.startTime));
    setEndTime(dayjs(visit.endTime));
  };

  const handleSave = async () => {
    if (!selectedDate || !startTime || !endTime) {
      showError('Please select date and both times');
      return;
    }

    if (endTime.isBefore(startTime)) {
      showError('End time must be after start time');
      return;
    }

    // Combine date with times
    const startDateTime = selectedDate.hour(startTime.hour()).minute(startTime.minute()).second(0);

    const endDateTime = selectedDate.hour(endTime.hour()).minute(endTime.minute()).second(0);

    setIsLoading(true);
    try {
      if (onTimeUpdate) {
        await onTimeUpdate(visit.id, startDateTime.toISOString(), endDateTime.toISOString());
      }
      setIsEditing(false);
    } catch (error) {
      showError('Failed to update visit time');
      console.error('Time update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimeRange = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    // If same day, show date once
    if (startDate.toDateString() === endDate.toDateString()) {
      return `${startDate.toLocaleDateString()} ${startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Different days
    return `${startDate.toLocaleString([], { dateStyle: 'short', timeStyle: 'short' })} - ${endDate.toLocaleString([], { dateStyle: 'short', timeStyle: 'short' })}`;
  };

  if (isEditing) {
    return (
      <div className="rounded-sm p-3 bg-white/20 mt-2">
        <div className="flex flex-col gap-1">
          <div className="flex flex-col gap-2">
            <div className="w-full">
              <div className="text-xs text-gray-100 mb-1">Date</div>
              <DatePicker
                format="DD/MM/YYYY"
                value={selectedDate}
                onChange={(date) => setSelectedDate(date!)}
                size="small"
                className="w-full !rounded-sm !text-xs !bg-white/30 !text-gray-100"
                placeholder="Select date"
              />
            </div>

            <div className="flex gap-2">
              <div className="flex-1">
                <div className="text-xs text-gray-100 mb-1">Start</div>
                <TimePicker
                  format="HH:mm"
                  value={startTime}
                  needConfirm={false}
                  onChange={handleStartTimeChange}
                  size="small"
                  className="w-full !rounded-sm !text-xs !bg-white/30 !text-gray-100"
                  placeholder="Start time"
                  minuteStep={15}
                  disabledTime={() => ({
                    disabledMinutes: disabledMinutes,
                  })}
                />
              </div>

              <div className="flex-1">
                <div className="text-xs text-gray-100 mb-1">End</div>
                <TimePicker
                  format="HH:mm"
                  value={endTime}
                  needConfirm={false}
                  onChange={(time) => setEndTime(time!)}
                  size="small"
                  className="w-full !rounded-sm !text-xs !bg-white/30 !text-gray-100"
                  placeholder="End time"
                  minuteStep={15}
                  disabledTime={() => ({
                    disabledMinutes: disabledMinutes,
                    disabledHours: () => {
                      if (!startTime) return [];
                      const hours: number[] = [];
                      for (let i = 0; i < startTime.hour(); i++) {
                        hours.push(i);
                      }
                      return hours;
                    },
                  })}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-1 mt-2">
            <Button
              size="small"
              className="!bg-white/30 !border-none !text-white hover:!bg-white/20"
              icon={<CloseOutlined />}
              onClick={handleCancel}
              disabled={isLoading}
            />
            <Button
              size="small"
              className="!bg-app-primary !border-none !text-white hover:!bg-app-primary/60"
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleSave}
              loading={isLoading}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="group relative border border-transparent hover:border-white/50 hover:border-dashed rounded-md px-2 py-1 mt-2 cursor-pointer transition-all duration-200 bg-white/20"
      onClick={handleEdit}
    >
      <div className="flex items-center justify-between w-full text-center text-cyan-50">
        <p className="text-sm">{formatTimeRange(visit.startTime, visit.endTime)}</p>

        <EditOutlined className="text-gray-400 transition-opacity duration-200 ml-2 text-xs" />
      </div>
    </div>
  );
}
