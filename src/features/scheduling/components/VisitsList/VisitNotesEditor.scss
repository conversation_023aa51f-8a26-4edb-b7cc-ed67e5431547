#visitcard-textarea {
  resize: none;
  /* Works in WebKit browsers (Chrome, Edge, Safari, Opera) */
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.3); /* track color */
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.4); /* scrollbar thumb color */
    border-radius: 6px;
    /* border: 1px solid rgba(255, 255, 255, 0.4); */
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.7); /* hover color */
  }

  /* Grab corner (bottom-right between scrollbars) */
  &::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.6);
  }
}
