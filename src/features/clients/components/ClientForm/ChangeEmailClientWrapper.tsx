import { FieldComponentProps } from '@app/types/form.types';
import { ChangeEmailFormWrapper } from '@pages/userProfile/ChangeEmail/ChangeEmailWrapper';
import React from 'react';

interface ChangeEmailClientWrapperProps extends FieldComponentProps<unknown> {
  // Add any additional props if needed
}

export const ChangeEmailClientWrapper: React.FC<ChangeEmailClientWrapperProps> = () => {
  // Since ChangeEmailFormWrapper doesn't accept props, we just render it
  return <ChangeEmailFormWrapper />;
};
