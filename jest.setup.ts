import '@testing-library/jest-dom';

globalThis.window = globalThis.window || ({} as never);
globalThis.window['ENV'] = {
  APP_AUTH_CLIENT_ID: '9ddbf4f0-8c95-4158-b71b-d1aae0d108ea',
  APP_AUTH_CALLBACK: 'http://localhost:3030/auth/callback',
  APP_DEV_URL: 'https://loom-dev.api.konnecta.io/api',
  LOOM_TENANT: '',
  LOOM_TENANT_URL: '',
  SENTRY_DNS: '',
  SENTRY_ENVIROMENT: 'local-dev',
  FEATURE_SCHEDULING: 'true',
  FEATURE_SHIFTS: 'true',
  FEATURE_AVAILABILITY: 'true',
  // HUMAN_RESOURCES: 'true',
  // PROJECTS: 'true',
  // PROJECT_REPORTING: 'true',
  APP_VERSION: 'local.dev_1.0.0',
};
Object.defineProperty(globalThis, 'import.meta', {
  value: {
    env: {
      BASE_URL: '/',
      // Add any other env vars your app expects
    },
  },
});
beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
});

jest.mock('axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: [] })),
}));
