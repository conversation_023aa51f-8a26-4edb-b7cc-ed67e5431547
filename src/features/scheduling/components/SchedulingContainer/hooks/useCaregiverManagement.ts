import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useCallback, useEffect, useState } from 'react';
import { fetchAvailableCaregivers } from '../utils/fetchCaregivers';
import { filterCaregivers } from '../utils/filterCaregivers';

/**
 * Custom hook for managing caregiver data and search functionality
 * Handles fetching available caregivers and client-side filtering
 * @returns Object containing caregivers state and search function
 */
export const useCaregiverManagement = () => {
  const activeVisit = useSchedulingStore((state) => state.activeVisit);
  const setAvailableCaregivers = useSchedulingStore((state) => state.setAvailableCaregivers);
  const availableCaregivers = useSchedulingStore((state) => state.availableCaregivers);

  const [filteredCaregivers, setFilteredCaregivers] = useState(availableCaregivers);

  // Fetch available caregivers when active visit changes
  useEffect(() => {
    if (!activeVisit) {
      setAvailableCaregivers([]);
      return;
    }

    const fetchCaregivers = async () => {
      console.log('Fetching caregivers for visit', { activeVisit });
      const caregivers = await fetchAvailableCaregivers(activeVisit);
      setAvailableCaregivers(caregivers);
    };

    fetchCaregivers();
  }, [activeVisit, setAvailableCaregivers]);

  // Update filtered caregivers when available caregivers change
  useEffect(() => {
    setFilteredCaregivers(availableCaregivers);
  }, [availableCaregivers]);

  // Handle caregiver search with client-side filtering
  const onCaregiverChange = useCallback(
    (searchTerm: string) => {
      const filtered = filterCaregivers(availableCaregivers, searchTerm);
      setFilteredCaregivers(filtered);
    },
    [availableCaregivers]
  );

  return {
    availableCaregivers,
    filteredCaregivers,
    onCaregiverChange,
    activeVisit,
  };
};
