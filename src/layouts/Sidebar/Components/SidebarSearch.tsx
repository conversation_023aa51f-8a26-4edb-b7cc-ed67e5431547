import { Input, InputProps } from 'antd';
import { useEffect, useRef, useState } from 'react';

type DebouncedInputProps = {
  onChange: (value: string) => void;
  debounce?: number;
  initValue?: string;
  placeholder?: string;
  PrefixIcon?: React.ComponentType<{ className?: string }>;
} & Omit<InputProps, 'onChange'>;

export function SidebarSearch({
  onChange,
  debounce = 500,
  initValue = '',
  placeholder = 'Search...',
  PrefixIcon,
  className = '',
  ...rest
}: DebouncedInputProps) {
  const [value, setValue] = useState(initValue);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      onChange(newValue);
    }, debounce);
  };

  useEffect(() => {
    setValue(initValue);
  }, [initValue]);

  return (
    <Input
      variant="outlined"
      size={'large'}
      prefix={PrefixIcon ? <PrefixIcon className="text-gray-500" /> : undefined}
      type="text"
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      className={` ${className}  ${PrefixIcon ? '' : 'pl-4'} pr-4 bg-app-gray `}
      {...rest}
    />
  );
}
