import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { fetchVisitsData } from '../utils/fetchVisitsData';

/**
 * Custom hook for managing visit data based on current date
 * Handles fetching visits when date changes
 * @returns Object containing visits state and date management functions
 */
export const useVisitData = () => {
  const setVisits = useSchedulingStore((state) => state.setVisits);
  const visits = useSchedulingStore((state) => state.visits);

  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());

  // Fetch visits when current date changes
  useEffect(() => {
    const loadVisits = async () => {
      const visitsData = await fetchVisitsData(currentDate);
      const sortedVisits = visitsData?.data.data.sort((a, b) => {
        const startDiff = new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
        if (startDiff !== 0) return startDiff;
        return new Date(a.endTime).getTime() - new Date(b.endTime).getTime();
      });
      setVisits(sortedVisits || []);
    };

    loadVisits();
  }, [currentDate, setVisits]);

  return {
    visits,
    currentDate,
    setCurrentDate,
  };
};
