import React, { memo, useMemo } from 'react';
import { IconComponent } from './types';

interface MenuIconProps {
  /** Icon configuration with filled and outline variants */
  icon?: {
    filled: IconComponent;
    outline: IconComponent;
  };
  /** Whether the menu item is currently active */
  isActive: boolean;
  /** Whether any child menu item is active */
  hasActiveChild?: boolean;
  /** Additional CSS classes for the icon */
  className?: string;
}

/**
 * MenuIcon component that renders the appropriate icon based on state
 * Automatically chooses between filled and outline variants
 * Optimized with React.memo for performance
 */
const MenuIconComponent: React.FC<MenuIconProps> = ({
  icon,
  isActive,
  hasActiveChild = false,
  className = 'text-lg text-app-primary',
}) => {
  // Memoized icon component selection
  const IconComponent = useMemo(() => {
    if (!icon) return null;
    return isActive || hasActiveChild ? icon.filled : icon.outline;
  }, [icon, isActive, hasActiveChild]);

  // Return null if no icon is provided
  if (!IconComponent) return null;

  return <IconComponent className={className} />;
};

// Memoized export with display name
MenuIconComponent.displayName = 'MenuIcon';
export const MenuIcon = memo(MenuIconComponent);
