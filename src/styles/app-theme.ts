import { ThemeConfig } from 'antd';
import { createStyles } from 'antd-style';
import colors from 'tailwindcss/colors';

export const useStyle = createStyles(({ prefixCls, css }) => ({
  linearGradientButton: css`
    &.${prefixCls}-btn-primary:not([disabled]):not(.${prefixCls}-btn-dangerous) {
      > span {
        position: relative;
      }

      &::before {
        content: '';
        background: linear-gradient(135deg, var(--color-app-secondary), var(--color-app-primary));
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        opacity: 0;
      }
    }
  `,
}));
export const theme_colors = {
  primary: '#007AFF',
  icon: '#E0E0E0',
  border: '#E0E0E0',
  textPrimary: '#1f1f1f',
  textSecondary: '#7B7B7B',
  textHeading: '#333333',
  bgContainer: colors.gray[100], // Using Tailwind's gray[50] for light background
  bgWhite: '#ffffff',
  bgHover: '#f0f0f0',
  borderHeader: colors.gray[100],
};

export const appTheme: ThemeConfig = {
  token: {
    // Seed Token
    colorIcon: 'var(--color-app-gray)', // global icon color
    colorText: 'var(--color-app-text-light)',
    colorPrimary: 'var(--color-app-primary)',
    colorPrimaryHover: 'var(--color-app-secondary-light)',
    // Alias Token
    // colorBgContainer: 'var(--color-app-gray)',
    // fontFamily: `'Inter', sans-serif`,
    fontFamily: `'Inter', sans-serif`,
  },
  components: {
    Table: {
      fontSize: 12,
      colorBgContainer: theme_colors.bgWhite,
      colorBorderSecondary: theme_colors.border,
      colorText: theme_colors.textSecondary,
      colorTextHeading: theme_colors.textHeading,
      headerBg: theme_colors.bgContainer,
      rowHoverBg: theme_colors.bgHover,
      headerSplitColor: theme_colors.borderHeader,
      paddingContentHorizontal: 0,
      padding: 0,
      cellPaddingInline: 0,
      borderRadius: 8,
    },

    Button: {
      colorPrimary: 'var(--color-app-primary)',
      colorText: 'var(--color-app-text-light)',
      textHoverBg: 'var(--color-app-gray-light)',
      defaultHoverColor: 'var(--color-app-primary)',
      primaryShadow: 'none',
    },

    DatePicker: {
      colorTextPlaceholder: 'var(--color-app-text-light)',
      colorBgContainer: 'var(--color-app-gray-light)',
      colorBorder: 'transparent',
      activeShadow: 'none',
      activeBorderColor: 'var(--color-app-primary)',
      colorText: 'var(--color-app-text)',
      cellHoverBg: 'var(--color-app-white)',
      fontSize: 14,
      padding: 0,
      cellActiveWithRangeBg: 'var(--color-app-gray-light)',

      colorPrimary: 'var(--color-app-primary)',

      controlItemBgActive: 'var(--color-app-primary)',
      colorTextHeading: 'var(--color-app-text-light)',
    },

    Collapse: {
      // colorText: '#1f1f1f',
      // colorTextHeading: '#333333',
      colorBorder: '#E0E0E0',
      colorBgContainer: 'var(--color-app-gray-light)',
      headerPadding: '12px 16px',
      contentPadding: '12px 16px',
      headerBg: '#F5F5F5',
      contentBg: '#ffffff',
    },

    Calendar: {
      colorTextPlaceholder: 'var(--color-app-text-light)',
      controlItemBgHover: 'var(--color-app-gray)',
      colorText: 'var(--color-app-text)',
      fontSize: 14,
      padding: 0,

      colorPrimary: 'var(--color-app-primary)',
    },

    Input: {
      colorTextPlaceholder: 'var(--color-app-text-light)',
      colorBorder: 'transparent',
      colorBgContainer: 'var(--color-app-gray-light)',
      fontSize: 16,
      colorText: 'var(--color-app-text)',
      hoverBorderColor: 'var(--color-app-secondary-light)',
      activeBorderColor: 'var(--color-app-primary)',
      activeShadow: 'var(--color-app-primary)',
    },
    InputNumber: {
      colorBorder: 'transparent',
      colorBgContainer: 'var(--color-app-gray-light)',
      hoverBorderColor: 'var(--color-app-secondary-light)',
      activeBorderColor: 'var(--color-app-primary)',
      colorTextPlaceholder: 'var(--color-app-text-light)',
      colorText: 'var(--color-app-text)',
      fontSize: 16,
      activeShadow: 'var(--color-app-primary)',
      handleHoverColor: 'var(--color-app-secondary-light)',
      handleBorderColor: 'var(--color-app-gray)',
    },
    Dropdown: {
      // colorBorderBg: 'red',
      // controlOutline: '#00b96b', // Shows a colored outline on hover/focus if supported
      colorBgContainer: 'var(--color-app-gray-light)',
      // colorText: '#7B7B7B',
      fontSize: 16,
    },
    Select: {
      colorBorder: 'transparent',
      colorTextPlaceholder: 'var(--color-app-text-light)',
      colorBgContainer: 'var(--color-app-gray-light)',
      hoverBorderColor: 'var(--color-app-secondary-light)',
      activeBorderColor: 'var(--color-app-primary)',
      colorText: 'var(--color-app-text)',
      fontSize: 16,
      activeOutlineColor: 'none',

      optionSelectedBg: 'var(--color-app-primary)',
      optionSelectedColor: 'var(--color-app-white)',
      optionSelectedFontWeight: 'normal',
    },

    Checkbox: {
      colorBgContainer: 'var(--color-app-gray)',
      colorBorder: 'transparent',
      colorText: 'var(--color-app-text-light)',
      colorPrimary: 'var(--color-app-primary)',
    },
    Badge: {
      colorWarningBg: 'var(--color-app-warning)',
    },
    Switch: {
      colorPrimary: 'var(--color-app-primary)',
    },

    Tabs: {
      itemSelectedColor: 'var(--color-app-text)',
      itemHoverColor: 'var(--color-app-primary)',
    },
    Tooltip: {
      colorBgSpotlight: 'var(--color-app-primary)',
      // colorText: 'red',
      sizePopupArrow: 0,
    },
    Pagination: {
      itemBg: 'var(--color-app-gray-light)',
      itemActiveBg: 'var(--color-app-primary)',
      colorPrimary: 'var(--color-app-white)', // For arrow/next/prev icons
      itemLinkBg: 'var(--color-app-gray-light)', // Background for prev/next buttons
    },
  },
};
