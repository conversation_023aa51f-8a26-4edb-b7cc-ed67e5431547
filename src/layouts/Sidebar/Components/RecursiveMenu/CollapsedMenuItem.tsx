import { Dropdown, Tooltip } from 'antd';
import React, { memo, useCallback, useMemo } from 'react';
import { MenuIcon } from './MenuIcon';
import { MenuItemProps } from './types';
import { createDropdownItems, getMenuItemClasses } from './utils';

/**
 * CollapsedMenuItem component for rendering menu items when sidebar is collapsed
 * Shows icons only with tooltips and dropdowns for items with children
 * Optimized with React.memo and useCallback for performance
 */
const CollapsedMenuItemComponent: React.FC<MenuItemProps> = ({
  item,
  isActive,
  hasChildren,
  hasActiveChild,
  onNavigate,
  index,
  depth,
}) => {
  // Memoized navigation handler
  const handleNavigate = useCallback(() => {
    onNavigate(item.key);
  }, [onNavigate, item.key]);

  // Memoized dropdown click handler
  const handleDropdownClick = useCallback(
    ({ key }: { key: string }) => {
      onNavigate(key);
    },
    [onNavigate]
  );

  // Memoized CSS classes based on current state
  const classes = useMemo(() => getMenuItemClasses(isActive, true), [isActive]);

  // Memoized container classes with conditional margin for first item at depth
  const containerClasses = useMemo(
    () => `
    flex justify-center items-center w-full 
    ${index === 0 && depth !== 0 ? 'mt-2' : ''}
  `,
    [index, depth]
  );

  // Memoized dropdown items
  const dropdownItems = useMemo(
    () => (hasChildren ? createDropdownItems(item.children || []) : []),
    [hasChildren, item.children]
  );

  // Render dropdown for items with children
  if (hasChildren) {
    return (
      <div className={containerClasses}>
        <Dropdown
          trigger={['hover']}
          align={{ offset: [50, -30] }}
          menu={{
            items: dropdownItems,
            onClick: handleDropdownClick,
          }}
          overlayClassName="z-50 w-42"
        >
          <span className={classes.container}>
            <MenuIcon icon={item.icon} isActive={isActive} hasActiveChild={hasActiveChild} className="text-2xl" />
          </span>
        </Dropdown>
      </div>
    );
  }

  // Render tooltip for items without children
  return (
    <div className={containerClasses}>
      <Tooltip title={item.label} placement="right">
        <span className={classes.container} onClick={handleNavigate}>
          <MenuIcon icon={item.icon} isActive={isActive} className="text-xl" />
        </span>
      </Tooltip>
    </div>
  );
};

// Memoized export with display name
CollapsedMenuItemComponent.displayName = 'CollapsedMenuItem';
export const CollapsedMenuItem = memo(CollapsedMenuItemComponent);
