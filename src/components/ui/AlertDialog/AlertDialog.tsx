import { Button, Modal } from 'antd';
import React from 'react';

type AlertDialogType = 'destructive' | 'confirmation';

type AlertDialogProps = {
  isOpen: boolean;
  type?: AlertDialogType;
  title: string;
  description?: string;
  confirmationText?: string;
  cancelText?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
};

const getColorsBasedOnType = (t: AlertDialogType) =>
  t === 'destructive'
    ? {
        title: 'text-red-500',
        filled: '!bg-red-500 hover:!bg-red-700 !text-white !border-transparent',
        outline: '!bg-transparent !border-2 !border-red-500 !text-red-600 hover:!bg-red-50',
      }
    : {
        title: 'text-blue-500',
        filled: '!bg-blue-500 hover:!bg-blue-700 !text-white !border-transparent',
        outline: '!bg-transparent !border-2 !border-blue-500 !text-blue-600 hover:!bg-blue-50',
      };

const btnBase = '!rounded-xl !h-12 !px-7 !text-base !font-semibold transition-colors';
const btnDisabled = '!opacity-60 !cursor-not-allowed pointer-events-none';

const AlertDialog: React.FC<AlertDialogProps> = ({
  isOpen,
  type = 'confirmation',
  title,
  description,
  confirmationText = 'OK',
  cancelText = 'Cancel',
  isLoading = false,
  isDisabled = false,
  onConfirm,
  onCancel,
}) => {
  const color = getColorsBasedOnType(type);
  const disableAll = isDisabled || isLoading;

  return (
    <Modal
      open={isOpen}
      onCancel={onCancel}
      footer={null}
      closable={false}
      centered
      style={{ minWidth: 600, maxWidth: 800 }}
      styles={{ body: { padding: '16px 24px' } }}
      width={'100%'}
      transitionName="ant-move-up"
    >
      <h3 className={`text-lg md:text-2xl font-semibold mb-8 ${color.title}`}>{title}</h3>

      {description && (
        <p className="mb-14 text-base md:text-xl text-gray-800 whitespace-normal break-words">{description}</p>
      )}

      <div className="flex justify-end gap-4">
        <Button
          type="default"
          disabled={disableAll}
          className={[btnBase, color.outline, disableAll ? btnDisabled : ''].join(' ')}
          onClick={onCancel}
        >
          {cancelText}
        </Button>

        <Button
          type="default"
          loading={isLoading}
          disabled={disableAll}
          className={[btnBase, color.filled, disableAll ? btnDisabled : ''].join(' ')}
          onClick={onConfirm}
        >
          {confirmationText}
        </Button>
      </div>
    </Modal>
  );
};

export default AlertDialog;
