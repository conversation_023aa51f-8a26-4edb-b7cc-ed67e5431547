import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { useDrop } from 'react-dnd';
import { useCaregiverAssignment } from './useCaregiverAssignment';

type UseDropZoneProps = {
  visit: VisitPopulated;
  isActive: boolean;
};

/**
 * Hook that manages drag and drop functionality for caregiver assignment
 */
export function useDropZone({ visit, isActive }: UseDropZoneProps) {
  const { updateCaregiverOnVisit } = useCaregiverAssignment();

  const [{ isOver, canDrop }, dropRef] = useDrop(
    () => ({
      accept: 'CAREGIVER',
      canDrop: () => isActive,
      drop: (item: { caregiver: Caregiver }) => {
        updateCaregiverOnVisit(visit.id, item.caregiver);
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    }),
    [visit.id, isActive, updateCaregiverOnVisit]
  );

  const isDroppable = isOver && canDrop;

  return { isDroppable, dropRef };
}
