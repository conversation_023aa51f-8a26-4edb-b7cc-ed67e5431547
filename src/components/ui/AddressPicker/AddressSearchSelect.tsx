import {
  getSuggestions,
  ParsedAddress,
  resetPlacesSession,
  resolveSuggestionToAddress,
} from '@app/components/ui/AddressPicker/google.api';
import FetchSearchSelect from '@app/components/ui/FetchSelect/ApiFetchSelect';
import { CommonListResponse } from '@app/types/table.types';
import { useCallback, useEffect, useState } from 'react';
import { GrLocation } from 'react-icons/gr';
import { AddressValue } from './types';

interface AddressSearchSelectProps {
  disabled?: boolean;
  setValue: (address: AddressValue | undefined) => void;
  value: AddressValue | undefined;
  style?: React.CSSProperties;
  placeholder?: string;
  noDataText?: string;
  onAddressSelect?: (address: ParsedAddress) => void; // New callback for address selection
}

interface GoogleSuggestion {
  placePrediction: google.maps.places.PlacePrediction | null;
}

/**
 * Address search component using Google Maps JavaScript SDK
 * Follows the same pattern as PatientSearch but for addresses
 */
export function AddressSearchSelect({
  disabled = false,
  setValue,
  value,
  style,
  placeholder = 'Search address...',
  noDataText = 'No addresses found',
  onAddressSelect,
}: AddressSearchSelectProps) {
  const [data, setData] = useState<CommonListResponse<GoogleSuggestion>>({
    Results: [],
    TotalResults: 0,
  });

  /**
   * Convert ParsedAddress to AddressValue format
   * Includes lat/lng extraction as requested
   */
  const convertToAddressValue = (parsed: ParsedAddress): AddressValue => {
    return {
      address: parsed.formattedAddress,
      city: parsed.city || '',
      country: parsed.country || '',
      zip: parsed.postalCode || '',
      lat: parsed.location.lat, // ✅ Requirement 0: Extract lat
      lng: parsed.location.lng, // ✅ Requirement 0: Extract lng
    };
  };

  /**
   * Fetch address suggestions using Google Maps JavaScript SDK
   * ✅ Requirement 2: Fixed scrolling data disappearing issue
   */
  const fetchAddresses = useCallback(async (searchValue?: string, pageSize?: number, pageIndex?: number) => {
    try {
      if (!searchValue || searchValue.length < 3) {
        setData({
          Results: [],
          TotalResults: 0,
        });
        return {} as CommonListResponse<GoogleSuggestion>;
      }

      console.log('🔍 Fetching address suggestions for:', searchValue, 'page:', pageIndex);

      // For Google Places, we get all suggestions at once, so we simulate pagination
      const suggestions = await getSuggestions(searchValue.trim());

      console.log('📍 Address suggestions response:', suggestions);

      if (suggestions && suggestions.length > 0) {
        // Apply pagination if specified
        const startIndex = pageIndex && pageSize ? pageIndex * pageSize : 0;
        const endIndex = pageSize ? startIndex + pageSize : suggestions.length;
        const paginatedResults = suggestions.slice(startIndex, endIndex);

        // ✅ FIX: Append data for scrolling, don't replace it
        if (pageIndex && pageIndex > 0) {
          // Scrolling - append to existing data
          setData((prevData) => ({
            Results: [...prevData.Results, ...paginatedResults],
            TotalResults: suggestions.length,
          }));
        } else {
          // New search - replace data
          setData({
            Results: paginatedResults,
            TotalResults: suggestions.length,
          });
        }
      } else {
        setData({
          Results: [],
          TotalResults: 0,
        });
      }
    } catch (err) {
      console.error('Error fetching addresses:', err);
      setData({
        Results: [],
        TotalResults: 0,
      });
    }
    return {} as CommonListResponse<GoogleSuggestion>;
  }, []);

  /**
   * Handle address selection
   * ✅ Requirement 3: Fill out client props (city, zip, lat, lng)
   */
  const handleAddressSelect = async (item?: string | GoogleSuggestion | GoogleSuggestion[] | undefined) => {
    if (item && typeof item === 'object' && !Array.isArray(item)) {
      const suggestion = item as GoogleSuggestion;

      try {
        console.log('🏠 Resolving selected suggestion:', suggestion);

        const parsedAddress = await resolveSuggestionToAddress(suggestion);

        console.log({ parsedAddress });
        const addressValue = convertToAddressValue(parsedAddress);

        console.log('✅ Created address value:', addressValue);
        console.log('📊 Address breakdown:');
        console.log('  - Street:', parsedAddress.street);
        console.log('  - City:', parsedAddress.city);
        console.log('  - State:', parsedAddress.state);
        console.log('  - Country:', parsedAddress.country);
        console.log('  - ZIP:', parsedAddress.postalCode);
        console.log('  - Lat:', parsedAddress.location.lat);
        console.log('  - Lng:', parsedAddress.location.lng);
        console.log('  - Has street address:', parsedAddress.hasStreetAddress);
        console.log('  - Is complete address:', parsedAddress.isCompleteAddress);

        setValue(addressValue);

        // ✅ Requirement 3: Call callback with parsed address for client props
        if (onAddressSelect) {
          onAddressSelect(parsedAddress);
        }

        // Reset session after selection for billing best practices
        resetPlacesSession();
      } catch (error) {
        console.error('Error resolving address:', error);
        setValue(undefined);
      }
    }
  };

  // Test function to verify SDK is working
  useEffect(() => {
    console.log('AddressSearchSelect mounted, testing Google Maps SDK...');
    (async () => {
      try {
        const resp = await getSuggestions('Κορυτ');
        console.log('✅ Google Maps SDK working, predictions:', resp);
      } catch (error) {
        console.error('❌ Google Maps SDK error:', error);
      }
    })();
  }, []);

  return (
    <FetchSearchSelect<GoogleSuggestion>
      data={data}
      style={{ ...style }}
      disabled={disabled}
      value={value ? value.address : undefined}
      onValueChange={handleAddressSelect}
      getId={(item) => item.placePrediction?.placeId || ''}
      getLabel={(item) => {
        const prediction = item.placePrediction;
        if (!prediction) return 'Invalid suggestion';

        // Extract text from the prediction
        const mainText = prediction.text?.text || '';
        const secondaryText = ''; // Google Maps new API doesn't have structured formatting in the same way
        const types = prediction.types || [];

        return (
          <div className="flex flex-col text-sm">
            <span className="font-semibold">{mainText}</span>
            {secondaryText && (
              <span className="text-gray-500 text-xs flex items-center gap-2">
                <GrLocation /> {secondaryText}
              </span>
            )}
            <span className="text-gray-400 text-xs">
              {types
                .filter((type) => !['political', 'geocode'].includes(type))
                .slice(0, 2)
                .join(', ')}
            </span>
          </div>
        );
      }}
      fetchData={fetchAddresses}
      placeholder={placeholder}
      canAddNew={false} // Google Places doesn't support creating new addresses
      canAdd={false}
      notFoundContent={noDataText} // ✅ Requirement 1: Custom no data text
    />
  );
}
