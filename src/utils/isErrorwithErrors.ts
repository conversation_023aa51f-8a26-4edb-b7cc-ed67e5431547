export function isErrorWithDetail(obj: unknown): obj is { detail: string } {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'detail' in obj &&
    typeof (obj as { detail: unknown }).detail === 'string'
  );
}
type ErrorResponse = {
  response?: {
    data?: unknown;
  };
};

export function extractErrorData(error: unknown): unknown {
  if (typeof error === 'object' && error !== null) {
    const e = error as ErrorResponse;
    return e.response?.data ?? error;
  }
  return error;
}
