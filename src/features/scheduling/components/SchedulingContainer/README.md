# SchedulingContainer

A refactored scheduling container component with clean separation of concerns for better maintainability and readability.

## 📁 File Structure

```
SchedulingContainer/
├── SchedulingContainer.tsx     # Main component (UI orchestration)
├── index.ts                   # Exports for easy importing
├── README.md                  # This documentation
├── hooks/                     # Custom hooks for state management
│   ├── useVisitData.ts       # Manages visit data and date selection
│   ├── useVisitSearch.ts     # Handles visit search with debouncing
│   └── useCaregiverManagement.ts # Manages caregiver data and filtering
└── utils/                     # Pure utility functions
    ├── fetchVisitsData.ts    # API calls for visits
    ├── fetchCaregivers.ts    # API calls for caregivers
    └── filterCaregivers.ts   # Client-side caregiver filtering logic
```

## 🎯 Purpose

The SchedulingContainer orchestrates three main sections:

1. **Visits List** - Shows visits for selected date with search functionality
2. **Calendar** - Date picker and visual calendar view
3. **Caregivers List** - Available caregivers with filtering

## 🏗️ Architecture

### Main Component (`SchedulingContainer.tsx`)

- **Role**: UI orchestration and layout
- **Responsibilities**:
  - Renders the three main sections
  - Connects hooks to components
  - Handles component props and styling
- **No business logic**: All logic moved to hooks and utils

### Custom Hooks (`hooks/`)

- **Role**: State management and side effects
- **Benefits**:
  - Reusable across components
  - Testable in isolation
  - Clean separation of concerns

#### `useVisitData.ts`

- Manages visit data based on current date
- Handles initial data loading
- Provides date state management

#### `useVisitSearch.ts`

- Handles debounced search functionality
- Prevents excessive API calls (500ms debounce)
- Manages search logic with minimum character requirements

#### `useCaregiverManagement.ts`

- Fetches available caregivers based on selected visit
- Manages client-side filtering
- Handles caregiver state synchronization

### Utility Functions (`utils/`)

- **Role**: Pure functions for data processing
- **Benefits**:
  - Easy to test
  - Reusable across hooks
  - No side effects

#### `fetchVisitsData.ts`

- API calls for fetching and searching visits
- Handles date range formatting
- Error handling and logging

#### `fetchCaregivers.ts`

- API calls for available caregivers
- Service mapping and time slot handling
- Error handling

#### `filterCaregivers.ts`

- Client-side filtering logic
- Searches firstName, lastName, and full name
- Case-insensitive matching

## 🔄 Data Flow

```
1. User selects date → useVisitData → fetchVisitsData → API → Update visits
2. User searches visits → useVisitSearch → debounced API call → Update visits
3. User selects visit → useCaregiverManagement → fetchCaregivers → API → Update caregivers
4. User searches caregivers → filterCaregivers → Update filtered caregivers (client-side)
```

## 🚀 Benefits of This Structure

### For Junior Developers:

- **Clear responsibility**: Each file has a single, clear purpose
- **Easy to find code**: Logical file organization
- **Small files**: Easier to understand and modify
- **Good documentation**: Comments and clear naming

### For the Team:

- **Better testability**: Hooks and utils can be tested independently
- **Reusability**: Hooks can be used in other components
- **Maintainability**: Changes are isolated to specific files
- **Debugging**: Easier to locate issues

### For Performance:

- **Optimized re-renders**: Proper hook dependencies
- **Debounced search**: Prevents excessive API calls
- **Memoized components**: Using React.memo for optimization

## 🛠️ How to Extend

### Adding a new feature:

1. **New API call**: Add to appropriate `utils/` file
2. **New state management**: Create new hook in `hooks/`
3. **New UI logic**: Update main component
4. **New filtering**: Add to `utils/filterCaregivers.ts`

### Example: Adding visit filtering

1. Create `utils/filterVisits.ts`
2. Add filtering logic to `useVisitSearch.ts`
3. Update `SchedulingContainer.tsx` to use filtered data

## 📋 Testing Strategy

- **Utils**: Unit tests for pure functions
- **Hooks**: React Testing Library with custom render
- **Component**: Integration tests with mocked hooks
- **API calls**: Mock API responses for consistent testing

This structure makes the codebase more maintainable and easier for new developers to understand and contribute to.
