/// <reference lib="webworker" />

interface IDBKeyval {
  set: (key: string, value: NotificationPayload) => Promise<void>;
  get: (key: string) => Promise<NotificationPayload>;
}

interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

interface Firebase {
  initializeApp: (config: FirebaseConfig) => void;
  messaging: () => unknown;
}

declare const self: ServiceWorkerGlobalScope;
declare const idbKeyval: IDBKeyval;
declare const firebase: Firebase;

console.log('SERVICE WORKER FB STARTED');

// Import external scripts
importScripts('https://cdn.jsdelivr.net/npm/idb-keyval@6/dist/umd.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.10.0/firebase-messaging-compat.js');

const NOTIFICATION_KEY = 'notification';
const img = `${self.location.origin}/media/logos/homecarelogo_sm.png`;
console.log('img', img);

interface NotificationPayload {
  title: string;
  body: string;
  type?: string;
  msg?: string;
}

interface NotificationData {
  title?: string;
  body?: string;
}

interface PushEventData {
  notification: NotificationData;
  data?: Record<string, unknown>;
}

self.addEventListener('notificationclick', (event: NotificationEvent) => {
  console.log('Click notification', event);
  const notificationData = event.notification || {};

  const notificationPayload: NotificationPayload = {
    title: notificationData.title || 'N/A',
    body: notificationData.body || 'N/A',
  };
  // Open the URL in the default browser.
  event.waitUntil(routing(notificationPayload, event));
});

async function routing(notificationData: NotificationPayload, event: NotificationEvent): Promise<boolean> {
  const windowClients = await self.clients.matchAll({ includeUncontrolled: true, type: 'window' });
  console.log('clients', { clients: self.clients, windowClients });

  const postMessage: NotificationPayload = {
    type: 'NOTIFICATION_CLICK',
    ...notificationData,
  };

  console.log({ postMessage });
  const lastClient = windowClients.length > 0 && windowClients[windowClients.length - 1];
  console.log({ lastClient });

  if (lastClient) {
    console.log('inside lastClient', { lastClient });
    lastClient.focus();
    lastClient.postMessage({
      ...postMessage,
      msg: 'from background - foreground - tab is there but not focused',
    });

    event.notification.close();
    return Promise.resolve(true);
  }

  // Opening new window
  await saveNotifToDb(postMessage);

  await self.clients.openWindow('/dashboard');
  event.notification.close();
  return Promise.resolve(true);
}

async function saveNotifToDb(notif: NotificationPayload): Promise<void> {
  console.log('saving notification to db', notif);
  return await idbKeyval.set(NOTIFICATION_KEY, notif);
}

const firebaseConfig = {
  apiKey: 'AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8',
  authDomain: 'homecare-2498a.firebaseapp.com',
  projectId: 'homecare-2498a',
  storageBucket: 'homecare-2498a.firebasestorage.app',
  messagingSenderId: '1063363203978',
  appId: '1:1063363203978:web:ee1a9263e338d61fa6ae73',
};

firebase.initializeApp(firebaseConfig);

// Initialize messaging (kept for Firebase setup)
firebase.messaging();

self.addEventListener('push', (event: PushEvent) => {
  console.log('Received push event', event);
  if (event.data) {
    const payload: PushEventData = event.data.json();
    console.log('Push data received', payload);

    if (!('data' in payload)) {
      console.error('No data in payload. Wrong notification schema', payload);
      return;
    }

    const notificationData = payload.notification;
    const title = notificationData?.title || 'N/A';
    const body = notificationData?.body || 'N/A';

    const notificationTitle = title;
    const notificationOptions: NotificationOptions = {
      body: body,
      icon: img,
    };

    console.log('Generating notification', notificationOptions);
    event.waitUntil(self.registration.showNotification(notificationTitle, notificationOptions));
  }
});

export {};
