import { BellOutlined, CloseOutlined } from '@ant-design/icons';
import { ApiClient } from '@api/api-configuration';
import { useAuthContext } from '@context/auth/useAuthContext';
import useNotifications from '@context/notifications/useNotificationContext';
import { getFirebaseToken } from '@web-push/web-push';
import { CLOSE_KEY, NOTIFICATION_DB_KEY, REMIND_LATER_KEY } from '@web-push/web-push.static';
import { Button } from 'antd';
import { del, get } from 'idb-keyval';
import { JSX, memo, useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

type NotificationData = NotificationDataWhenClickedSW | NotificationDirectlyFromSW;
type NotificationDataWhenClickedSW = {
  title: string;
  body: string;
  msg: string;
  type: 'NOTIFICATION_CLICK';
};
type NotificationDirectlyFromSW = {
  data: {
    title: string;
    body: string;
  };
};

type PermissionsAction = {
  id: string;
  label: string | JSX.Element;
  action: () => void;
};

const WebPushPermissions = () => {
  const { user } = useAuthContext();
  const { openNotification } = useNotifications();
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(false);

  const [showPrompt, setShowPrompt] = useState<boolean>(false);

  const PermissionsActions: PermissionsAction[] = [
    {
      id: 'enable',
      label: 'Enable',
      action: async () => {
        handleEnableNotifications();
      },
    },
    {
      id: 'remind-me',
      label: 'Remind me',
      action: () => handleRemindMe(),
    },
    {
      id: 'close',
      label: <CloseOutlined />,
      action: () => handleClose(),
    },
  ];

  const navigate = useNavigate();

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event: MessageEvent<NotificationData>) => {
        console.log('Message from web psh', event);
        let data = null;

        if ('data' in event.data) {
          data = event.data.data;
        } else {
          try {
            data = event.data;
            //this parsing is for backgroudn closed app
          } catch (error) {
            console.error('Error parsing data from service worker', error);
          }
        }
        console.log('Notification Data', data);
        openNotification('topRight', {
          title: data?.title || 'N/A',
          description: data?.body || 'N/A',
          type: 'Success',
        });
      });
    }
  }, [navigate, openNotification]);
  // Check Notification Permission on Mount

  useEffect(() => {
    handleBackgroundNotification();
    const permission = Notification.permission;
    const remindLater = localStorage.getItem(REMIND_LATER_KEY);
    const closed = localStorage.getItem(CLOSE_KEY);

    if (permission === 'granted') {
      setNotificationsEnabled(true);
    } else if (remindLater && new Date().getTime() < Number(remindLater)) {
      setShowPrompt(false); // Hide prompt if "Remind me" was clicked recently
    } else if (closed) {
      setShowPrompt(false); // Hide if "Close" was clicked
    } else {
      setShowPrompt(true); // Show prompt if no previous actions
    }

    if (permission === 'granted') handleGetFirebaseToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleBackgroundNotification = useCallback(async () => {
    const data = await get(NOTIFICATION_DB_KEY);
    if (!data) return;
    del(NOTIFICATION_DB_KEY);
    openNotification('topRight', {
      title: 'From db: ' + data?.title || 'N/A',
      description: data?.body || 'N/A',
      type: 'Success',
    });
    try {
      if (data.type === 'NOTIFICATION_CLICK') {
        console.log('DB DATA NOTIFICATION', data);
        // alert(`${data.module}-${data.action} ${data.id}`);
      }
    } catch (error) {
      console.error('Error handling background notification', error);
    }
  }, [openNotification]);

  const handleGetFirebaseToken = () => {
    getFirebaseToken()
      .then(async (firebaseToken) => {
        try {
          console.log('USER ID', user.userId);
          console.log({ firebaseToken });
          await ApiClient.notificationsApi.notifications.registerNotificationsRegisterPost({
            userId: user.userId,
            deviceToken: firebaseToken,
            platform: 'web',
          });
        } catch (error) {
          console.error('Error registering notifications', error);
          // Handle error, e.g., show a notification or alert
        }
      })
      .catch((err) => console.error('An error occured while retrieving firebase token. ', err));
  };

  const handleEnableNotifications = async () => {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      setNotificationsEnabled(true);
    }
  };

  const handleRemindMe = () => {
    const remindTime = new Date().getTime() + 24 * 60 * 60 * 1000; // Remind after 24 hours
    localStorage.setItem(REMIND_LATER_KEY, remindTime.toString());
    setShowPrompt(false);
  };

  const handleClose = () => {
    localStorage.setItem(CLOSE_KEY, 'true');
    setShowPrompt(false);
  };
  if (notificationsEnabled || !showPrompt) return null;

  return (
    <div
      className="flex w-full justify-between p-4 bg-[#3e63dd] text-white text-[0.8rem]"
      style={{ background: '#3e63dd', color: 'white', fontSize: '0.8rem' }}
    >
      <div className="flex justify-start items-center gap-2">
        <div>
          <BellOutlined />
        </div>
        <div>Homecare needs your permission to send notifications </div>
      </div>
      <div className="flex justify-end items-center gap-4">
        {PermissionsActions.map((action) => (
          <Button className="bg-transparent text-white" key={action.id} onClick={action.action} variant="outlined">
            {action.label}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default memo(WebPushPermissions);
