import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import AppAvatar from '@app/components/ui/Avatar';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { capitalizeFullNameFirst } from '@app/utils/fullNameCapilatizeFirst';
import { VisitLocation } from '@feat-scheduling/components/VisitCalendarEvent/VisitLocation';
import { useVisitNotesUpdate } from '@feat-scheduling/hooks/useVisitNotesUpdate';
import { useVisitTimeUpdate } from '@feat-scheduling/hooks/useVisitTimeUpdate';
import { Button } from 'antd';
import { GrLocation } from 'react-icons/gr';
import { MdOutlineKeyboardArrowDown } from 'react-icons/md';
import { VisitDetails } from './VisitDetails';

type VisitCardProps = {
  visit: VisitPopulated;
  isActive: boolean;
  onToggle: (id: number) => void;
};

/**
 * Component that displays a single visit card
 * Contains client info, location, and expandable details
 */
export function VisitCard({ visit, isActive, onToggle }: VisitCardProps) {
  const { id, client } = visit;
  const { updateVisitTime } = useVisitTimeUpdate();
  const { updateVisitNotes } = useVisitNotesUpdate();
  const fullname = `${client.firstName} ${client.lastName}`;
  const fullnameLetters = getInitials(fullname, { fallback: 'NA' });
  return (
    <div className="pl-2 py-2 w-full">
      <div
        className={`visit-item rounded-md py-2 px-4 transition-all duration-300 ${
          isActive
            ? 'bg-app-primary-500 text-app-gray'
            : 'bg-app-primary-600/10 text-app-text-dark hover:bg-app-primary-600/25'
        }`}
      >
        {/* Client Name and Arrow */}
        <div className="flex items-center justify-between">
          <div className="text-base flex items-center gap-2">
            <AppAvatar isSelected={true} fullName={fullname} value={fullnameLetters} />
            {capitalizeFullNameFirst(client.firstName || '', client.lastName || '')}
          </div>
          <Button
            size="small"
            className={`${isActive ? '!bg-white/25 !text-white' : '!bg-app-primary-600/5'} !border-none hover:!bg-white/30`}
            onClick={() => onToggle(id)}
            icon={
              <MdOutlineKeyboardArrowDown
                className={`transition-transform duration-300 ${isActive ? 'rotate-180' : ''}`}
              />
            }
          />
        </div>

        {/* Location Info */}
        <div className={`flex items-center gap-2 mt-1 text-sm ${isActive ? '!text-app-gray' : 'text-app-text-dark'}`}>
          <GrLocation />
          <VisitLocation
            street={visit.client.street || ''}
            city={visit.client!.city || ''}
            postalCode={visit.client.postalCode || ''}
          />
        </div>

        {/* Expandable Details */}
        <VisitDetails
          visit={visit}
          isActive={isActive}
          onTimeUpdate={updateVisitTime}
          onNotesUpdate={updateVisitNotes}
        />
      </div>
    </div>
  );
}
