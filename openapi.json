{"openapi": "3.1.0", "info": {"title": "Services-Service", "description": "\n    Microservice for managing home care service types & services.\n\n    ## Features\n    * Create, read, update, and delete service types\n    * Create, read, update, and delete services\n    * Secure endpoints with authentication and authorization\n\n    ## Authentication\n    All endpoints require authentication using JWT tokens.\n    Admin endpoints require additional admin permissions.\n    ", "version": "1.0.0"}, "paths": {"/services": {"get": {"tags": ["Services"], "summary": "Get all services", "description": "Retrieve a list of all services with pagination support", "operationId": "get_services_services_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 0, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of service records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Service"}, "title": "Response Get Services Services Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Services"], "summary": "Create new service", "description": "Create a new service record", "operationId": "create_service_services_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceCreate"}}}}, "responses": {"201": {"description": "Service created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}}, "409": {"description": "Conflict: Service with this name already exists or invalid foreign key"}, "400": {"description": "Bad Request: Invalid service type ID"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/services/{service_id}": {"get": {"tags": ["Services"], "summary": "Get service by ID", "description": "Retrieve a specific service by its ID", "operationId": "get_service_services__service_id__get", "parameters": [{"name": "service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Id"}}], "responses": {"200": {"description": "Service record details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}}, "404": {"description": "Service not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Services"], "summary": "Update service", "description": "Update an existing service's information", "operationId": "update_service_services__service_id__put", "parameters": [{"name": "service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceUpdate"}}}}, "responses": {"200": {"description": "Updated service record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}}, "404": {"description": "Service not found"}, "400": {"description": "Bad Request: No fields provided for update or invalid data"}, "409": {"description": "Conflict: Service with this name already exists or invalid foreign key"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Services"], "summary": "Delete service", "description": "Delete a service record by ID", "operationId": "delete_service_services__service_id__delete", "parameters": [{"name": "service_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Id"}}], "responses": {"204": {"description": "Service deleted successfully"}, "404": {"description": "Service not found"}, "409": {"description": "Conflict: Service is referenced by other records"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/services/by-type/{service_type_id}": {"get": {"tags": ["Services"], "summary": "Get services by type ID", "description": "Retrieve a list of services associated with a specific service type ID", "operationId": "get_services_by_type_services_by_type__service_type_id__get", "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Type Id"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 0, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of service records for the given type", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Service"}, "title": "Response Get Services By Type Services By Type  Service Type Id  Get"}}}}, "404": {"description": "Service type not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/service-types": {"get": {"tags": ["Service Types"], "summary": "Get all service types", "description": "Retrieve a list of all service types with pagination support", "operationId": "get_service_types_service_types_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 0, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "List of service type records", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceType"}, "title": "Response Get Service Types Service Types Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Service Types"], "summary": "Create new service type", "description": "Create a new service type record", "operationId": "create_service_type_service_types_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceTypeCreate"}}}}, "responses": {"201": {"description": "Service type created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceType"}}}}, "409": {"description": "Conflict: Service type with this name already exists"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/service-types/{service_type_id}": {"get": {"tags": ["Service Types"], "summary": "Get service type by ID", "description": "Retrieve a specific service type by its ID", "operationId": "get_service_type_service_types__service_type_id__get", "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Type Id"}}], "responses": {"200": {"description": "Service type record details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceType"}}}}, "404": {"description": "Service type not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Service Types"], "summary": "Update service type", "description": "Update an existing service type's information", "operationId": "update_service_type_service_types__service_type_id__put", "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceTypeUpdate"}}}}, "responses": {"200": {"description": "Updated service type record", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceType"}}}}, "404": {"description": "Service type not found"}, "400": {"description": "Bad Request: No fields provided for update"}, "409": {"description": "Conflict: Service type with this name already exists"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Service Types"], "summary": "Delete service type", "description": "Delete a service type record by ID", "operationId": "delete_service_type_service_types__service_type_id__delete", "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Service Type Id"}}], "responses": {"204": {"description": "Service type deleted successfully"}, "404": {"description": "Service type not found"}, "409": {"description": "Conflict: Service type is referenced by existing services"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"type": "integer", "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes"}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat", "description": "Timestamp when the service record was created"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat", "description": "Timestamp of the last service record update"}}, "additionalProperties": false, "type": "object", "required": ["serviceId", "name", "serviceTypeId", "estimatedTimeMinutes", "createdAt"], "title": "Service"}, "ServiceCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the specific service"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "serviceTypeId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"type": "integer", "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes"}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s"}}, "additionalProperties": false, "type": "object", "required": ["name", "serviceTypeId", "estimatedTimeMinutes"], "title": "ServiceCreate", "description": "Schema for creating a new service."}, "ServiceType": {"properties": {"serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "Unique identifier for the service type (PK of service_types table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the service type (e.g., 'Skin/Wound Management', 'Elimination Management')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service type"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat", "description": "Timestamp when the service type was created"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat", "description": "Timestamp of the last service type update"}}, "additionalProperties": false, "type": "object", "required": ["serviceTypeId", "name", "createdAt"], "title": "ServiceType", "description": "Model representing a type of service, stored in a separate table."}, "ServiceTypeCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the service type (e.g., 'Skin/Wound Management', 'Elimination Management')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "ServiceTypeCreate", "description": "Schema for creating a new service type."}, "ServiceTypeUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "additionalProperties": false, "type": "object", "title": "ServiceTypeUpdate", "description": "Schema for updating an existing service type."}, "ServiceUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "serviceTypeId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes"}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s"}}, "additionalProperties": false, "type": "object", "required": ["serviceTypeId", "estimatedTimeMinutes"], "title": "ServiceUpdate", "description": "Schema for updating an existing service."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}