#!/usr/bin/env node

import { spawn } from 'child_process';
import { watch } from 'fs';
import { resolve } from 'path';

const sourceFile = 'src/service-workers/firebase-messaging-sw.ts';
const outputFile = 'public/firebase-messaging-sw.js';

function buildServiceWorker() {
  console.log('Building service worker...');

  const tsc = spawn(
    'npx',
    [
      'tsc',
      sourceFile,
      '--target',
      'ES2020',
      '--lib',
      'ES2020,WebWorker',
      '--outDir',
      'public',
      '--outFile',
      outputFile,
      '--skipLibCheck',
      '--allowJs',
    ],
    { stdio: 'inherit' }
  );

  tsc.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Service worker built successfully');
    } else {
      console.error('❌ Service worker build failed');
    }
  });
}

// Build once
buildServiceWorker();

// Watch for changes if in watch mode
// eslint-disable-next-line no-undef
if (process.argv.includes('--watch')) {
  console.log('👀 Watching for changes...');
  watch(resolve(sourceFile), (eventType) => {
    if (eventType === 'change') {
      buildServiceWorker();
    }
  });
}
