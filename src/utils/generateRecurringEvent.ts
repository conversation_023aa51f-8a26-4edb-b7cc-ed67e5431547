import { Caregiver, Service } from '@api/READ_ONLY/caregiver_api/Api';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(isSameOrBefore);
interface VisitCard {
  id: number;
  date: Dayjs;
  caregiver: Caregiver;
  therapy: Service;
}
const WEEKDAY_MAP: Record<string, number> = {
  sunday: 0,
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
};

const ALL_WEEKDAYS = Object.keys(WEEKDAY_MAP); // ['sunday', ... , 'saturday']

export function generateVisits(
  start: Dayjs,
  end: Dayjs,
  visitsPerCycle: number,
  unit: 'week' | 'month',
  repeatDays: string[],
  time: Dayjs,
  caregivers: Caregiver[],
  therapies: Service[]
): VisitCard[] {
  // Prepare days to use, filling up randomly if fewer than visitsPerCycle
  const initialDays = repeatDays.length ? repeatDays.map((d) => d.toLowerCase()) : [start.format('dddd').toLowerCase()];

  const daysToUse = [...initialDays];
  const remaining = ALL_WEEKDAYS.filter((d) => !daysToUse.includes(d));

  while (daysToUse.length < visitsPerCycle && remaining.length) {
    const rnd = Math.floor(Math.random() * remaining.length);
    daysToUse.push(remaining.splice(rnd, 1)[0]);
  }

  const visits: VisitCard[] = [];
  let cycleCursor = start.clone().startOf(unit);
  let id = 0;

  while (cycleCursor.isSameOrBefore(end, 'day')) {
    let addedThisCycle = 0;

    for (const dayName of daysToUse) {
      if (addedThisCycle >= visitsPerCycle) break;

      const targetDayNum = WEEKDAY_MAP[dayName];
      let visitDate = cycleCursor.clone().day(targetDayNum);

      // Fix dayjs .day() rolling to previous cycle (e.g. if target day before cycle start)
      if (visitDate.isBefore(cycleCursor, 'day')) {
        visitDate = visitDate.add(1, 'week');
      }

      // Only include visits within [start, end]
      if (visitDate.isBefore(start, 'day') || visitDate.isAfter(end, 'day')) continue;

      visitDate = visitDate.hour(time.hour()).minute(time.minute()).second(0).millisecond(0);

      visits.push({
        id,
        date: visitDate,
        caregiver: caregivers[id % (caregivers.length || 1)] || 'Unassigned',
        therapy: therapies[id % (therapies.length || 1)] || 'Therapy',
      });

      id++;
      addedThisCycle++;
    }

    cycleCursor = cycleCursor.add(1, unit).startOf(unit);
  }

  return visits;
}
