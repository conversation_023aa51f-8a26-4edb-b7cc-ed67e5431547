import React, { lazy, memo, Suspense, useCallback, useMemo } from 'react';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';
import { MenuIcon } from './MenuIcon';
import { ExpandedMenuItemProps } from './types';
import { getLeftClass, getPaddingLeftClass } from './utils';

// Lazy load RecursiveMenu to avoid circular dependency
const RecursiveMenu = lazy(() => import('./RecursiveMenu'));

/**
 * ExpandedMenuItem component for rendering menu items when sidebar is expanded
 * Shows full labels, icons, and handles nested menu structures
 * Optimized with React.memo and useCallback to prevent unnecessary re-renders
 */
const ExpandedMenuItemComponent: React.FC<ExpandedMenuItemProps> = ({
  item,
  isActive,
  hasChildren,
  hasActiveChild,
  isOpen,
  depth,
  index,
  isSidebarCollapsed,
  onToggle,
  onNavigate,
}) => {
  /**
   * Memoized click handler to prevent unnecessary re-renders
   * Toggles submenu for items with children, navigates for leaf items
   */
  const handleClick = useCallback(() => {
    if (hasChildren && !isSidebarCollapsed) {
      onToggle(item.key);
    } else if (!hasChildren) {
      onNavigate(item.key);
    }
  }, [hasChildren, isSidebarCollapsed, onToggle, onNavigate, item.key]);

  // Memoized CSS classes to avoid recalculation on every render
  const containerClasses = useMemo(
    () => `
    relative flex items-center w-full gap-x-2 cursor-pointer 
    rounded-md transition-all duration-200 text-sm
    ${isActive ? '' : 'text-text-app-text-light hover:text-app-primary hover:bg-app-gray-light'}
    ${hasChildren ? 'pr-6' : ''}
  `,
    [isActive, hasChildren]
  );

  const labelClasses = useMemo(
    () => `
    relative inline-block z-10 w-full whitespace-nowrap 
    px-5 py-3 rounded-4xl
    ${isActive ? 'text-app-primary font-medium' : 'text-app-text-light hover:text-app-primary'}
  `,
    [isActive]
  );

  const wrapperClasses = useMemo(
    () => `
    w-full justify-center 
    ${index === 0 && depth !== 0 ? 'mt-0' : ''}
  `,
    [index, depth]
  );

  const paddingClasses = useMemo(
    () => `
    w-full mx-auto relative flex items-center justify-center 
    ${isSidebarCollapsed ? 'px-0' : getPaddingLeftClass(depth)}
  `,
    [isSidebarCollapsed, depth]
  );

  return (
    <div className={wrapperClasses}>
      {/* Main menu item container */}
      <div className={paddingClasses}>
        <div className={containerClasses} onClick={handleClick}>
          {/* Menu item icon */}
          {item.icon && (
            <span className="flex items-center z-10">
              <MenuIcon icon={item.icon} isActive={isActive} hasActiveChild={hasActiveChild} />
            </span>
          )}

          {/* Menu item label with active state styling */}
          <span className={labelClasses}>
            <div className="relative z-10">{item.label}</div>
            {/* Active state background pill */}
            {isActive && (
              <div className="menu-pill absolute top-0 left-0 w-full h-full z-0 rounded-tl-2xl rounded-bl-2xl bg-white" />
            )}
          </span>

          {/* Chevron icon for expandable items */}
          {hasChildren && (isOpen ? <FiChevronDown className="ml-auto" /> : <FiChevronRight className="ml-auto" />)}
        </div>
      </div>

      {/* Submenu container with animation */}
      {hasChildren && !isSidebarCollapsed && (
        <div className={`transition-all duration-300 ${isOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'}`}>
          <div className="relative flex justify-center">
            {/* Vertical line indicator for nested items */}
            <div className={`absolute top-0 bottom-0 ${getLeftClass(depth)} w-px bg-app-primary z-0`} />
            {/* Lazy-loaded recursive menu for children */}
            <Suspense fallback={<div>Loading...</div>}>
              <RecursiveMenu items={item.children || []} depth={depth + 1} />
            </Suspense>
          </div>
        </div>
      )}
    </div>
  );
};

// Memoized export with display name
ExpandedMenuItemComponent.displayName = 'ExpandedMenuItem';
export const ExpandedMenuItem = memo(ExpandedMenuItemComponent);
