import { CaregiverShift, CaregiverWithServices, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { ShiftMap, UpdateAction } from '@feat-shifts/ShiftsContainer';
import { Modal } from 'antd';
import { AxiosResponse } from 'axios';
import dayjs, { Dayjs } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { memo, ReactNode, useState } from 'react';
import ShiftForm from './ShiftForm';
import ShiftsTableBody from './shiftsTable/ShiftsTableBody';
import ShiftsTableHeader from './shiftsTable/ShiftsTableHeader';

dayjs.extend(isoWeek);

export type ModalMode = 'create' | 'edit';

type Props = {
  shifts: ShiftMap;
  caregivers: CaregiverWithServices[];
  daysOfWeek: Dayjs[];
  dateNavigator: ReactNode;
  onCreate: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: ShiftCreate[]
  ) => Promise<AxiosResponse<number>[]>;
  onEdit: (
    selectedCaregiver: number,
    selectedDate: string,
    shifts: CaregiverShift[]
  ) => Promise<AxiosResponse<CaregiverShift>[]>;
  onDelete: (selectedCaregiver: number, shiftId: number) => Promise<AxiosResponse<void>>;
  onLocalUpdate: (action: UpdateAction) => void;
};

const ShiftsBoard = ({
  shifts,
  caregivers,
  daysOfWeek,
  dateNavigator,
  onCreate,
  onEdit,
  onDelete,
  onLocalUpdate,
}: Props) => {
  const { openNotification } = useNotifications();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mode, setMode] = useState<ModalMode>('create');

  const [selectedCaregiver, setSelectedCaregiver] = useState<null | number>(null);
  const [selectedDate, setSelectedDate] = useState<null | string>(null);
  const [shiftList, setShiftList] = useState<ShiftCreate[] | CaregiverShift[]>([
    { periodFrom: '', periodTo: '', notes: '' },
  ]);

  const closeModal = () => {
    setIsModalOpen(false);
    setShiftList([{ periodFrom: '', periodTo: '', notes: '' }]);
    setSelectedCaregiver(null);
    setSelectedDate(null);
  };

  const handleDeleteShift = async (id: number, index: number) => {
    if (!selectedCaregiver || !selectedDate) return;
    onDelete(selectedCaregiver, id)
      .then((res) => {
        console.log('Deleting Shift res', res);
        openNotification('topRight', {
          title: `Shifts`,
          description: 'Shift deleted successfully.',
          type: 'Success',
        });
        onLocalUpdate({
          type: 'delete',
          caregiverId: selectedCaregiver,
          date: selectedDate,
          shiftId: id,
        });
        const updateShiftList = shiftList.filter((_, i) => i !== index) as ShiftCreate[] | CaregiverShift[];
        setShiftList(updateShiftList);
        updateShiftList.length === 0 && setIsModalOpen(false);
      })
      .catch((error) => {
        console.log('error', isErrorWithDetail(error));
        const errorData = extractErrorData(error);
        if (isErrorWithDetail(errorData)) {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shift deletion failed. ' + errorData.detail,
            type: 'Warning',
          });
        }
      });
  };

  const handleSave = async () => {
    if (!selectedCaregiver || !selectedDate) return;
    const validShifts = shiftList.filter((s) => s.periodFrom && s.periodTo);

    if (mode === 'create') {
      console.log('CREATE SHIFT');
      onCreate(selectedCaregiver, selectedDate, validShifts as ShiftCreate[])
        .then((res) => {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts created successfully.',
            type: 'Success',
          });
          onLocalUpdate({
            type: 'create',
            caregiverId: selectedCaregiver,
            date: selectedDate,
            shifts: validShifts as CaregiverShift[],
          });
          console.log('CREATE SHIFT res', res);

          closeModal();
        })
        .catch((error) => {
          const errorData = extractErrorData(error);
          if (isErrorWithDetail(errorData)) {
            openNotification('topRight', {
              title: `Shifts`,
              description: 'Shifts creation failed. ' + errorData.detail,
              type: 'Warning',
            });
          }
        });
    }

    if (mode === 'edit') {
      const shiftsToUpdate = validShifts.filter((s): s is CaregiverShift => 'caregiverShiftId' in s);
      const shiftsToCreate = validShifts.filter((s): s is ShiftCreate => !('caregiverShiftId' in s));
      console.log('SHIFTS update', validShifts);
      console.log('SHIFTS update', shiftsToUpdate);
      console.log('SHIFTS create', shiftsToCreate);

      const promises: (Promise<AxiosResponse<CaregiverShift>[]> | Promise<AxiosResponse<number>[]>)[] = [];

      if (shiftsToUpdate.length > 0) {
        promises.push(onEdit(selectedCaregiver, selectedDate, shiftsToUpdate));
      }
      if (shiftsToCreate.length > 0) {
        promises.push(onCreate(selectedCaregiver, selectedDate, shiftsToCreate));
      }

      Promise.all(promises)
        .then(() => {
          openNotification('topRight', {
            title: `Shifts`,
            description: 'Shifts saved successfully.',
            type: 'Success',
          });

          if (shiftsToUpdate.length > 0) {
            onLocalUpdate({
              type: 'update',
              caregiverId: selectedCaregiver,
              date: selectedDate,
              shifts: shiftsToUpdate,
            });
          }

          if (shiftsToCreate.length > 0) {
            onLocalUpdate({
              type: 'create',
              caregiverId: selectedCaregiver,
              date: selectedDate,
              shifts: shiftsToCreate as CaregiverShift[], // backend returns ids usually
            });
          }

          closeModal();
        })
        .catch((error) => {
          const errorData = extractErrorData(error);
          if (isErrorWithDetail(errorData)) {
            openNotification('topRight', {
              title: `Shifts`,
              description: 'Shifts save failed. ' + errorData.detail,
              type: 'Warning',
            });
          }
        });
    }
  };

  const handleClickAdd = (id: number, dateKey: string) => {
    setSelectedCaregiver(id);
    setSelectedDate(dateKey);
    setShiftList([{ periodFrom: '', periodTo: '', notes: '' }]);
    setMode('create');
    setIsModalOpen(true);
  };

  const handleClickEdit = (id: number, dateKey: string, shifts: CaregiverShift[]) => {
    setSelectedCaregiver(id);
    setSelectedDate(dateKey);
    setShiftList(shifts);
    setMode('edit');
    setIsModalOpen(true);
  };

  return (
    <div>
      {isModalOpen && (
        <Modal
          title={mode === 'create' ? 'Add Shifts' : 'Edit Shifts'}
          open={isModalOpen}
          onCancel={closeModal}
          onOk={handleSave}
          okText="Save"
          cancelText="Cancel"
          width={600}
        >
          <ShiftForm
            shiftList={shiftList}
            setShiftList={setShiftList}
            handleDeleteShift={mode === 'edit' ? handleDeleteShift : undefined}
            mode={mode}
          />
        </Modal>
      )}
      {dateNavigator}
      <div className="rounded-md overflow-hidden border border-app-gray max-h-[80vh] flex flex-col">
        <div className="overflow-auto w-full h-full">
          <table className="table-fixed border-collapse w-full">
            <ShiftsTableHeader daysOfWeek={daysOfWeek} />
            <ShiftsTableBody
              caregiversList={caregivers}
              daysOfWeek={daysOfWeek}
              shiftMap={shifts}
              handleAdd={handleClickAdd}
              handleEdit={handleClickEdit}
            />
          </table>
        </div>
      </div>
    </div>
  );
};

export default memo(ShiftsBoard);
