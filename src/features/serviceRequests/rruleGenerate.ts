import { Dayjs } from 'dayjs';
import { RRule, datetime } from 'rrule';

const WEEKDAY_MAP = [RRule.SU, RRule.MO, RRule.TU, RRule.WE, RRule.TH, RRule.FR, RRule.SA];

const FREQUENCY_MAP = {
  daily: RRule.DAILY,
  weekly: RRule.WEEKLY,
  monthly: RRule.MONTHLY,
  yearly: RRule.YEARLY,
};

const buildRRule = (event: {
  startingDate: Dayjs;
  endingDate: Dayjs | null;
  fromTime: Dayjs;
  toTime: Dayjs;
  repeat: 'daily' | 'weekly' | 'never';
  interval: number;
  frequency: 'daily' | 'weekly';
  repetitionDaysOfTheWeek: number[];
  zoneId: string;
}) => {
  if (event.repeat === 'never' || !event.endingDate) return null;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const options: any = {
    tzid: event.zoneId,
    freq: FREQUENCY_MAP[event.frequency],
    interval: event.interval || 1,
    dtstart: datetime(
      event.startingDate.year(),
      event.startingDate.month() + 1,
      event.startingDate.date(),
      event.fromTime.hour(),
      event.fromTime.minute(),
      event.fromTime.second()
    ),
    until: datetime(
      event.endingDate.year(),
      event.endingDate.month() + 1,
      event.endingDate.date(),
      event.toTime.hour(),
      event.toTime.minute(),
      event.toTime.second()
    ),
  };

  if (event.frequency === 'weekly' && event.repetitionDaysOfTheWeek?.length) {
    options.byweekday = event.repetitionDaysOfTheWeek.map((d) => WEEKDAY_MAP[d]);
  }

  const rule = new RRule(options);
  return rule;
};

export default buildRRule;
