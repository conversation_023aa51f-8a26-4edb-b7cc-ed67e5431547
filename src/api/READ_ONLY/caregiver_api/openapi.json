{"openapi": "3.1.0", "info": {"title": "caregivers", "description": "\nMicroservice for managing home care caregivers.\n\n## Features\n* Create, read, update, and delete caregiver records\n* Manage caregiver shifts and services\n* Secure endpoints with authentication and authorization\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\nAdmin endpoints require additional admin permissions.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/caregivers/available": {"get": {"tags": ["Caregivers"], "summary": "Get available caregivers", "description": "Returns a list of caregivers who are available in the specified time range. A caregiver is considered available if they do not have any assigned visits overlapping the given `from`–`to` interval. Optionally filter caregivers by `service_id` to only include those who can provide the specified service.", "operationId": "caregivers_available_caregivers_available_get", "parameters": [{"name": "from", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "Start of the time range (inclusive, ISO8601)", "title": "From"}, "description": "Start of the time range (inclusive, ISO8601)", "example": "2025-09-10T17:00:00+00:00"}, {"name": "to", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "End of the time range (exclusive, ISO8601)", "title": "To"}, "description": "End of the time range (exclusive, ISO8601)", "example": "2025-09-10T19:00:00+00:00"}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Optional service filter", "title": "Service Ids"}, "description": "Optional service filter", "example": [1]}], "responses": {"200": {"description": "List of available caregivers", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaregiverWithServices"}, "title": "Response Caregivers Available Caregivers Available Get"}}}}, "422": {"description": "Invalid time range (e.g., 'to' earlier than 'from')"}, "500": {"description": "Internal server error"}}}}, "/caregivers/availability/by-dates": {"post": {"tags": ["Caregivers Availability"], "summary": "Get available caregivers on specific dates", "description": "Returns availability for caregivers across non-continuous dates. Filters by duration, caregiver IDs, and service IDs.", "operationId": "get_available_caregivers_by_dates_caregivers_availability_by_dates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverAvailabilityByDatesRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverAvailability"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/availability/by-range": {"get": {"tags": ["Caregivers Availability"], "summary": "Get available caregivers in a datetime range", "description": "Returns caregiver availability within a continuous datetime range. Optionally filters by slot duration, caregiver IDs, and service IDs.", "operationId": "get_available_caregivers_by_range_caregivers_availability_by_range_get", "parameters": [{"name": "from_date", "in": "query", "required": true, "schema": {"type": "string", "format": "date", "description": "Start of date range (UTC)", "title": "From Date"}, "description": "Start of date range (UTC)", "example": "2020-10-08"}, {"name": "to_date", "in": "query", "required": true, "schema": {"type": "string", "format": "date", "description": "End of date range (UTC)", "title": "To Date"}, "description": "End of date range (UTC)", "example": "2020-10-15"}, {"name": "duration_minutes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Slot duration in minutes", "default": 30, "title": "Duration Minutes"}, "description": "Slot duration in minutes", "example": 60}, {"name": "caregiver_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver IDs", "title": "Caregiver Ids"}, "description": "Filter by caregiver IDs", "example": [1, 2, 3]}, {"name": "service_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by caregiver service IDs", "title": "Service Ids"}, "description": "Filter by caregiver service IDs", "example": [10, 20]}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverAvailability"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers": {"get": {"tags": ["Caregivers"], "summary": "Get all caregivers", "description": "Retrieve a list of all caregivers", "operationId": "get_caregivers_caregivers_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "services", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by service IDs", "title": "Services"}, "description": "Filter by service IDs"}, {"name": "active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Active"}, "description": "Filter by active status"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by city (exact match)", "title": "City"}, "description": "Filter by city (exact match)"}, {"name": "skills", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by skills (match any)", "title": "Skills"}, "description": "Filter by skills (match any)"}, {"name": "languages_spoken", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by languages spoken (match any)", "title": "Languages Spoken"}, "description": "Filter by languages spoken (match any)"}, {"name": "rating_min", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Minimum rating", "title": "Rating <PERSON>"}, "description": "Minimum rating"}, {"name": "rating_max", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Maximum rating", "title": "Rating Max"}, "description": "Maximum rating"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiversGetAllPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Caregivers"], "summary": "Create new caregiver", "description": "Create a new caregiver", "operationId": "create_caregiver_caregivers_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverCreate"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/minified": {"get": {"tags": ["Caregivers"], "summary": "Get all caregivers with minified response schema", "description": "Retrieve a list of all caregivers", "operationId": "get_caregivers_min_caregivers_minified_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "services", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Filter by service IDs", "title": "Services"}, "description": "Filter by service IDs"}, {"name": "active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Active"}, "description": "Filter by active status"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by city (exact match)", "title": "City"}, "description": "Filter by city (exact match)"}, {"name": "skills", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by skills (match any)", "title": "Skills"}, "description": "Filter by skills (match any)"}, {"name": "languages_spoken", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "null"}], "description": "Filter by languages spoken (match any)", "title": "Languages Spoken"}, "description": "Filter by languages spoken (match any)"}, {"name": "rating_min", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Minimum rating", "title": "Rating <PERSON>"}, "description": "Minimum rating"}, {"name": "rating_max", "in": "query", "required": false, "schema": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "Maximum rating", "title": "Rating Max"}, "description": "Maximum rating"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiversGetAllMinifiedPaginationResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}": {"get": {"tags": ["Caregivers"], "summary": "Get caregiver by ID", "description": "Retrieve a caregiver by their ID", "operationId": "get_caregiver_caregivers__caregiver_id__get", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "404": {"description": "Caregiver not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Caregivers"], "summary": "Update caregiver", "description": "Update an existing caregiver", "operationId": "update_caregiver_caregivers__caregiver_id__put", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverUpdate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Caregiver"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Caregivers"], "summary": "Delete Caregiver", "description": "Delete a caregiver by id\n\n:param request: The request object\n:type request: Request\n:param caregiver_id: The ID of the caregiver to delete\n:type caregiver_id: int\n:raises HTTPException: If the caregiver is not found\n:raises HTTPException: If an error occurs while deleting the caregiver\n:return: No content\n:rtype: None", "operationId": "delete_caregiver_caregivers__caregiver_id__delete", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "responses": {"204": {"description": "No content"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/search": {"get": {"tags": ["Caregivers"], "summary": "Search for caregivers", "description": "Search for caregivers by first_name, last_name or email", "operationId": "search_caregivers_caregivers_search_get", "parameters": [{"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 3}, {"type": "null"}], "description": "Free text query", "title": "Query"}, "description": "Free text query"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverSearchPaginationResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/by-user/{user_id}": {"get": {"tags": ["Caregivers"], "summary": "Get caregiver by user ID", "description": "Retrieve a caregiver by their user ID including related services", "operationId": "get_caregiver_by_user_id_by_user__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverWithServices"}}}}, "404": {"description": "Caregiver not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}/shifts": {"post": {"tags": ["Shifts"], "summary": "Add a shift on the caregiver", "description": "Add a new shift for a caregiver", "operationId": "add_shift_caregivers__caregiver_id__shifts_post", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShiftCreate"}}}}, "responses": {"201": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "integer", "title": "Response Add Shift Caregivers  Caregiver Id  Shifts Post"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Shifts"], "summary": "Get shifts for a caregiver", "description": "Fetches shifts assigned to a caregiver with optional from/to date filters.", "operationId": "get_caregiver_shifts_caregivers__caregiver_id__shifts_get", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "The ID of the caregiver", "title": "Caregiver Id"}, "description": "The ID of the caregiver"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "description": "Pagination offset", "default": 0, "title": "Offset"}, "description": "Pagination offset"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Pagination limit", "default": 100, "title": "Limit"}, "description": "Pagination limit"}, {"name": "from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter from this date", "title": "From"}, "description": "Filter from this date"}, {"name": "to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "Filter to this date", "title": "To"}, "description": "Filter to this date"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverShiftsPaginatedResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/shifts/by-dates": {"get": {"tags": ["Shifts"], "summary": "Fetch all caregiver shifts for a list of dates", "description": "Returns all caregiver shifts where the shift start date matches any of the provided dates.", "operationId": "get_shifts_by_dates_caregivers_shifts_by_dates_get", "parameters": [{"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "description": "Pagination offset", "default": 0, "title": "Offset"}, "description": "Pagination offset"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Pagination limit", "default": 100, "title": "Limit"}, "description": "Pagination limit"}, {"name": "dates", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "date"}, "description": "List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)", "title": "Dates"}, "description": "List of dates (e.g. ?dates=2025-07-18&dates=2025-07-19)"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverShiftsPaginatedResponse"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/caregivers/{caregiver_id}/shifts/{shift_id}": {"put": {"tags": ["Shifts"], "summary": "Update a caregiver shift", "description": "Updates an existing caregiver shift's notes, start time, and end time.", "operationId": "update_shift_caregivers__caregiver_id__shifts__shift_id__put", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}, {"name": "shift_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Shift Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShiftUpdate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaregiverShift"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Shifts"], "summary": "Delete a caregiver shift", "description": "Deletes a caregiver shift by its ID.", "operationId": "delete_shift_caregivers__caregiver_id__shifts__shift_id__delete", "parameters": [{"name": "caregiver_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Caregiver Id"}}, {"name": "shift_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Shift Id"}}], "responses": {"204": {"description": "No content"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CalendarEvent": {"properties": {"start": {"type": "string", "format": "date-time", "title": "Start"}, "end": {"type": "string", "format": "date-time", "title": "End"}, "caregivers": {"items": {"$ref": "#/components/schemas/CaregiverAvailabilityResponse"}, "type": "array", "title": "Caregivers"}, "isAvailable": {"type": "boolean", "title": "Isavailable"}}, "type": "object", "required": ["start", "end", "isAvailable"], "title": "CalendarEvent"}, "Caregiver": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "caregiverId", "createdAt"], "title": "Caregiver"}, "CaregiverAvailability": {"properties": {"eventsByDate": {"additionalProperties": {"items": {"$ref": "#/components/schemas/CalendarEvent"}, "type": "array"}, "type": "object", "title": "Eventsbydate"}}, "type": "object", "title": "CaregiverAvailability"}, "CaregiverAvailabilityByDatesRequest": {"properties": {"dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Dates", "description": "List of individual dates to check (UTC)"}, "durationMinutes": {"type": "integer", "title": "Durationminutes", "description": "Slot duration in minutes"}, "caregiverIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Caregiverids", "description": "Filter by caregiver IDs"}, "serviceIds": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Serviceids", "description": "Filter by caregiver service IDs"}}, "type": "object", "required": ["dates", "durationMinutes"], "title": "CaregiverAvailabilityByDatesRequest"}, "CaregiverAvailabilityResponse": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverAvailabilityResponse"}, "CaregiverCreate": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}], "minLength": 1, "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}}, "type": "object", "required": ["firstName", "lastName", "services"], "title": "CaregiverCreate"}, "CaregiverMinified": {"properties": {"caregiverId": {"type": "integer", "title": "Caregiverid"}, "firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}}, "type": "object", "required": ["caregiverId", "firstName", "lastName"], "title": "CaregiverMinified"}, "CaregiverSearchPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/CaregiverWithServices"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "CaregiverSearchPaginationResponse"}, "CaregiverShift": {"properties": {"caregiverShiftId": {"type": "integer", "title": "Caregivershiftid"}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "periodFrom": {"type": "string", "format": "date-time", "title": "Periodfrom"}, "periodTo": {"type": "string", "format": "date-time", "title": "Periodto"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["caregiverShiftId", "caregiverId", "periodFrom", "periodTo"], "title": "CaregiverShift"}, "CaregiverShiftsPaginatedResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/CaregiverShift"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "CaregiverShiftsPaginatedResponse"}, "CaregiverUpdate": {"properties": {"firstName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firstname"}, "lastName": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "services": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Rating"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active"}}, "type": "object", "title": "CaregiverUpdate"}, "CaregiverWithServices": {"properties": {"firstName": {"type": "string", "title": "Firstname"}, "lastName": {"type": "string", "title": "Lastname"}, "userId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Userid"}, "dateOfBirth": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateofbirth"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/GenderEnum"}, {"type": "null"}]}, "nationalId": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 5}, {"type": "null"}], "title": "<PERSON>id"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "street": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Street"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "postalCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postalcode"}, "coverageAreas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Coverageareas"}, "travelRadiusKm": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Travelradiuskm"}, "services": {"anyOf": [{"items": {"$ref": "#/components/schemas/Service"}, "type": "array"}, {"type": "null"}], "title": "Services"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications"}, "skills": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Skills"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties"}, "languagesSpoken": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Languagesspoken"}, "rating": {"anyOf": [{"type": "number", "maximum": 5.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "active": {"type": "boolean", "title": "Active", "default": true}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["firstName", "lastName", "caregiverId", "createdAt"], "title": "CaregiverWithServices"}, "CaregiversGetAllMinifiedPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/CaregiverMinified"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "CaregiversGetAllMinifiedPaginationResponse"}, "CaregiversGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/CaregiverWithServices"}, "type": "array", "title": "Data", "description": "Paged data"}}, "type": "object", "required": ["total", "offset", "limit", "data"], "title": "CaregiversGetAllPaginationResponse"}, "GenderEnum": {"type": "string", "enum": ["MALE", "FEMALE", "OTHER"], "title": "GenderEnum"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Service": {"properties": {"serviceId": {"type": "integer", "title": "Serviceid", "description": "Unique identifier for the service (PK of services table)"}, "name": {"type": "string", "title": "Name", "description": "Name of the specific service (e.g., 'Basic Cleaning', 'Plumbing Repair')"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the service offering"}, "serviceTypeId": {"type": "integer", "title": "Servicetypeid", "description": "ID of the service type (foreign key to ServiceType table)"}, "estimatedTimeMinutes": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Estimatedtimeminutes", "description": "Estimated time required to perform the service in minutes", "default": 0}, "costInEuros": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Costine<PERSON>s", "description": "Estimated cost of the service in Euros"}}, "type": "object", "required": ["serviceId", "name", "serviceTypeId"], "title": "Service", "description": "Model representing a Homecare Service offering."}, "ShiftCreate": {"properties": {"notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "periodFrom": {"type": "string", "format": "date-time", "title": "Periodfrom"}, "periodTo": {"type": "string", "format": "date-time", "title": "Periodto"}}, "type": "object", "required": ["notes", "periodFrom", "periodTo"], "title": "ShiftCreate"}, "ShiftUpdate": {"properties": {"notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "periodFrom": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Periodfrom"}, "periodTo": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Periodto"}}, "type": "object", "required": ["notes", "periodFrom", "periodTo"], "title": "ShiftUpdate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/caregivers-api/"}], "security": [{"oauth2": []}]}