import { APP_PREFIX } from '@app/routes/urls';
import { Route, RoutesStructureWithStringKeys } from '@app/types/routing.types';

export const addFullPathToRoutes = (routeConstruct: RoutesStructureWithStringKeys): void => {
  const normalizePath = (path: string): string => {
    return `/${path}`.replace(/\/+/g, '/');
  };
  // Iterate over top-level sections
  Object.entries(routeConstruct).forEach(([sectionKey, section]) => {
    const keyPrefix = sectionKey.toLocaleLowerCase().includes('auth') ? '' : `${APP_PREFIX}/`;
    const basePath = normalizePath(`${keyPrefix}${sectionKey}`);

    const updateRoutes = (routes: Record<string, Route>, parentPath: string): void => {
      Object.entries(routes).forEach(([key, route]) => {
        const currentPath = normalizePath(`${parentPath}/${key}`);

        // Assign normalized full_path
        route.full_path = currentPath; // Remove leading slash for consistency

        // Recursively update sub-routes if they exist
        if (route.routes) {
          updateRoutes(route.routes, currentPath);
        }
      });
    };

    // Assign full_path to top-level section
    section.full_path = basePath;

    // Process nested routes
    if (section.routes) {
      updateRoutes(section.routes, basePath);
    }
  });
};
