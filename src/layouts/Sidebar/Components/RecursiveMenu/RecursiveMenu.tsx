import useUiContext from '@context/ui/useUiContext';
import React, { memo, useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// Import modular components and utilities
import '../menu.scss';
import { CollapsedMenuItem } from './CollapsedMenuItem';
import { ExpandedMenuItem } from './ExpandedMenuItem';
import { useMenuState } from './hooks';
import { MenuItem } from './types';
import { isChildActive } from './utils';

/**
 * RecursiveMenu Component
 *
 * A modular and maintainable navigation menu component that supports:
 * - Nested menu items with unlimited depth
 * - Collapsed/expanded sidebar states
 * - Active state management with auto-opening parent items
 * - Smooth animations and transitions
 * - TypeScript support with proper type definitions
 * - Performance optimized with React.memo and useCallback
 *
 * Architecture:
 * - types.ts: Type definitions and interfaces
 * - utils.ts: Utility functions for styling and calculations
 * - hooks.ts: Custom hooks for state management
 * - MenuIcon.tsx: Icon rendering component
 * - CollapsedMenuItem.tsx: Menu item for collapsed sidebar
 * - ExpandedMenuItem.tsx: Menu item for expanded sidebar
 */

interface RecursiveMenuProps {
  /** Array of menu items to render */
  items: MenuItem[];
  /** Current nesting depth (used for styling and indentation) */
  depth?: number;
}

const RecursiveMenu: React.FC<RecursiveMenuProps> = ({ items, depth = 0 }) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { isSidebarCollapsed } = useUiContext();

  // Custom hook manages open/closed state and auto-opening logic
  const { openItems, toggleItem } = useMenuState(items, pathname);

  // Memoized navigation handler to prevent unnecessary re-renders
  const handleNavigate = useCallback(
    (key: string | number) => {
      const target = String(key);
      if (pathname !== target) {
        navigate(target);
      }
    },
    [pathname, navigate]
  );

  // Memoized container classes to avoid recalculation
  const containerClasses = useMemo(
    () =>
      `flex flex-col w-full items-center relative ${
        depth !== 0 && 'pl-' + depth * 100
      } ${depth === 0 ? 'gap-1.5' : 'gap-1.5'}`,
    [depth]
  );

  return (
    <div className={containerClasses}>
      {items.map((item, index) => {
        const isOpen = !!openItems[item.key];
        const hasChildren = !!(item.children && item.children.length > 0);
        const isActive = pathname === item.key;
        const hasActiveChild = hasChildren ? isChildActive(item.children!, pathname) : false;

        // Render collapsed menu item when sidebar is collapsed
        if (isSidebarCollapsed) {
          return (
            <CollapsedMenuItem
              key={item.key}
              item={item}
              isActive={isActive}
              hasChildren={hasChildren}
              hasActiveChild={hasActiveChild}
              index={index}
              depth={depth}
              onNavigate={handleNavigate}
            />
          );
        }

        // Render expanded menu item when sidebar is expanded
        return (
          <ExpandedMenuItem
            key={item.key}
            item={item}
            isActive={isActive}
            hasChildren={hasChildren}
            hasActiveChild={hasActiveChild}
            isOpen={isOpen}
            index={index}
            depth={depth}
            isSidebarCollapsed={isSidebarCollapsed}
            onNavigate={handleNavigate}
            onToggle={toggleItem}
          />
        );
      })}
    </div>
  );
};

export default memo(RecursiveMenu);
