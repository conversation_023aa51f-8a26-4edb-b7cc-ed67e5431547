# DateTimeDisplay Component

A reusable React component for displaying date and time information with consistent formatting using dayjs.

## Features

- ✅ **Dayjs integration** - Consistent date/time formatting
- ✅ **Flexible layout** - Single line or separate date/time lines
- ✅ **Time ranges** - Display start and end times
- ✅ **Customizable formats** - Configure date and time formats
- ✅ **Custom styling** - Full CSS class control
- ✅ **TypeScript support** - Full type safety
- ✅ **Performance optimized** - Memoized to prevent unnecessary re-renders
- ✅ **Null handling** - Graceful fallback for missing dates

## Basic Usage

```tsx
import { DateTimeDisplay } from '@components/common/DateTimeDisplay';

// Separate date and time lines (default)
<DateTimeDisplay
  startTime={visit.startTime}
  endTime={visit.endTime}
  showDateSeparately
/>

// Single line format
<DateTimeDisplay
  startTime={appointment.date}
  showDateSeparately={false}
/>

// Date only
<DateTimeDisplay
  startTime={createdAt}
  showDateSeparately={false}
  dateFormat="MMM DD, YYYY"
/>
```

## Advanced Usage

```tsx
// Custom formats
<DateTimeDisplay
  startTime={event.start}
  endTime={event.end}
  dateFormat="dddd, MMMM Do YYYY"
  timeFormat="h:mm A"
  showDateSeparately
/>

// Custom styling
<DateTimeDisplay
  startTime={visit.startTime}
  endTime={visit.endTime}
  className="p-2 border rounded"
  dateClassName="font-semibold text-gray-800"
  timeClassName="text-blue-600"
/>

// Time range only
<DateTimeDisplay
  startTime="2023-12-01T09:00:00Z"
  endTime="2023-12-01T17:00:00Z"
  showDateSeparately={false}
  dateFormat=""
  timeFormat="HH:mm"
/>
```

## Props

| Prop                 | Type             | Default          | Description                             |
| -------------------- | ---------------- | ---------------- | --------------------------------------- |
| `startTime`          | `string \| Date` | -                | Start date/time (required)              |
| `endTime`            | `string \| Date` | -                | End date/time (optional)                |
| `showDateSeparately` | `boolean`        | `true`           | Show date on first line, time on second |
| `dateFormat`         | `string`         | `'MMM DD, YYYY'` | Dayjs date format string                |
| `timeFormat`         | `string`         | `'HH:mm'`        | Dayjs time format string                |
| `className`          | `string`         | `''`             | CSS class for container                 |
| `dateClassName`      | `string`         | `''`             | CSS class for date line                 |
| `timeClassName`      | `string`         | `''`             | CSS class for time line                 |

## Output Examples

### Separate Lines (showDateSeparately=true)

```
Dec 15, 2023
09:00 - 17:00
```

### Single Line (showDateSeparately=false)

```
Dec 15, 2023 09:00 - 17:00
```

### Date Only

```
Dec 15, 2023
```

### Time Only

```
09:00 - 17:00
```

## Common Use Cases

### Table Columns

Perfect for displaying visit times in tables:

```tsx
{
  key: 'visitTime',
  title: 'Time',
  render: (_, visit) => (
    <DateTimeDisplay
      startTime={visit.startTime}
      endTime={visit.endTime}
      showDateSeparately
      timeClassName="text-gray-600"
    />
  ),
}
```

### Event Cards

Great for event or appointment cards:

```tsx
<DateTimeDisplay
  startTime={event.startTime}
  endTime={event.endTime}
  dateFormat="dddd, MMM Do"
  timeFormat="h:mm A"
  dateClassName="font-bold text-lg"
  timeClassName="text-blue-600"
/>
```

### Creation Dates

Simple date display for created/updated timestamps:

```tsx
<DateTimeDisplay startTime={item.createdAt} showDateSeparately={false} dateFormat="MMM DD, YYYY" />
```

## Dayjs Format Options

Common format strings:

- `'YYYY-MM-DD'` → 2023-12-15
- `'MMM DD, YYYY'` → Dec 15, 2023
- `'dddd, MMMM Do YYYY'` → Friday, December 15th 2023
- `'HH:mm'` → 14:30
- `'h:mm A'` → 2:30 PM
- `'HH:mm:ss'` → 14:30:45

See [Dayjs format documentation](https://day.js.org/docs/en/display/format) for more options.
