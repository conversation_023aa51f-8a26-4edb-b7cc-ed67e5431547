import { memo } from 'react';

interface DefaultFooterProps {
  currentCount?: number;
  totalCount?: number;
  hasMore?: boolean;
  loading?: boolean;
}

const DefaultFooter = memo(({ currentCount, totalCount, hasMore, loading }: DefaultFooterProps) => (
  <div className="text-center w-full text-xs italic px-2">
    {currentCount && totalCount
      ? `${currentCount}/${totalCount} items`
      : totalCount
        ? `${totalCount} items`
        : 'No items'}
    {hasMore && !loading && <span className="ml-1 text-blue-500">• Scroll for more</span>}
  </div>
));

DefaultFooter.displayName = 'DefaultFooter';

export default DefaultFooter;
