import { FieldComponentProps } from '@app/types/form.types';
import React from 'react';
import { ServicesSelectCaregiver } from './ServicesSelectCaregiver';

interface ServicesFieldWrapperProps extends FieldComponentProps<unknown> {
  // Add any additional props if needed
}

export const ServicesFieldWrapper: React.FC<ServicesFieldWrapperProps> = ({ value, onChange, error }) => {
  return (
    <ServicesSelectCaregiver value={value} onChange={onChange} error={typeof error === 'string' ? error : undefined} />
  );
};
