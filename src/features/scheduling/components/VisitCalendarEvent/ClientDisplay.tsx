import AppAvatar from '@app/components/ui/Avatar';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { capitalizeFullNameFirst } from '@app/utils/fullNameCapilatizeFirst';
import { memo } from 'react';

type DisplayProps = {
  firstName: string;
  lastName: string;
};

/**
 * Component that displays client avatar and name
 */
export const ClientDisplay = memo(({ firstName, lastName }: DisplayProps) => {
  const fullName = `${firstName} ${lastName}`;
  const initials = getInitials(fullName, { fallback: '' });
  const displayName = capitalizeFullNameFirst(firstName || '', lastName || '');

  return (
    <div className="flex items-center gap-2">
      <AppAvatar isSelected={true} fullName={fullName} value={initials} />
      <div>{displayName}</div>
    </div>
  );
});

ClientDisplay.displayName = 'ClientDisplay';
