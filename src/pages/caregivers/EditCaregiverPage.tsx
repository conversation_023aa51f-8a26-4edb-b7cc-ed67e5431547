import { ApiClient } from '@api/api-configuration';
import { Caregiver, CaregiverCreate, CaregiverUpdate, Service } from '@api/READ_ONLY/caregiver_api/Api';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import { omit } from '@app/utils/omitKeys';
import { transformList } from '@app/utils/transformToList';
import useNotifications from '@context/notifications/useNotificationContext';
import { CaregiverForm } from '@feat-caregivers/components/CaregiverForm/CaregiverForm';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

export const EditCaregiverPage = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit
  const { openNotification } = useNotifications();
  const [data, setData] = useState<Caregiver>();

  useEffect(() => {
    const fetchCaregiver = async () => {
      try {
        const response = await ApiClient.caregiverApi.caregivers.getCaregiverCaregiversCaregiverIdGet(Number(id));
        setData(response.data);
      } catch (err) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Failed to fetch caregiver',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);

    fetchCaregiver();
  }, [id, openNotification, state]);
  const clientData = data;

  if (!clientData) return <div>Loading...</div>;
  if (!clientData) return <div>Error loading client</div>;
  const onSubmit = async (formData: Caregiver | CaregiverCreate) => {
    try {
      const allowedData = omit(formData as Caregiver, ['coverageAreas', 'caregiverId', 'createdAt', 'updatedAt']);

      const transformedData: CaregiverUpdate = {
        ...allowedData,
        services: (allowedData.services as Service[]).map((s) => s.serviceId),
        certifications: transformList(formData.certifications),
        skills: transformList(formData.skills),
        specialties: transformList(formData.specialties),
        languagesSpoken: transformList(formData.languagesSpoken),
      };

      console.log({ transformedData });
      await ApiClient.caregiverApi.caregivers.updateCaregiverCaregiversCaregiverIdPut(Number(id), transformedData);

      openNotification('topRight', {
        title: `Caregiver`,
        description: 'Caregiver updated successfully.',
        type: 'Success',
      });
    } catch (error: unknown) {
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver update failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };
  return <CaregiverForm data={clientData} onSubmit={onSubmit} />;
};
