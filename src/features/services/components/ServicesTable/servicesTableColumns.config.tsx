import { Service, ServiceType } from '@api/READ_ONLY/services_api/Api';
import { Typography } from 'antd';
import { ReactNode } from 'react';

export type ServiceTableColumnType<T> = {
  dataIndex?: keyof T;
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, record?: T) => ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getServiceTableColumns = (serviceTypes: ServiceType[]): ServiceTableColumnType<Service>[] => [
  {
    dataIndex: 'name',
    key: 'name',
    title: 'Name',
    width: 120,
    render: (value) => <Typography>{value as string}</Typography>,
  },
  {
    dataIndex: 'description',
    key: 'description',
    title: 'Description',
    width: 140,
    render: (value) => <Typography>{value as string}</Typography>,
  },
  {
    dataIndex: 'estimatedTimeMinutes',
    key: 'estimatedTimeMinutes',
    title: 'Estimated Time (Minutes)',
    width: 120,
    render: (value) => <Typography>{value as string}</Typography>,
  },
  {
    dataIndex: 'serviceTypeId',
    key: 'type',
    title: 'Service Type',
    width: 120,
    render: (value) => {
      const typeId = value as number;
      const serviceType = serviceTypes.find((s) => s.serviceTypeId === typeId);
      return <Typography>{serviceType?.name || 'Unknown'}</Typography>;
    },
  },
];
