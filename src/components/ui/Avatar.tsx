import { isLightColour } from '@app/utils/colourFunctions';
import { hashCode, intToRGB } from '@app/utils/generateColors';
import { Avatar, Tooltip } from 'antd';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';
import { useEffect, useState } from 'react';

type Props = {
  fullName: string;
  value: string;
  size?: AvatarSize;
  isSelected?: boolean;
  imageUrl?: string;
  personId?: number;
  showTooltip?: boolean;
};

const AppAvatar = ({ fullName, value, size = 'default', isSelected = false, showTooltip = false }: Props) => {
  const [imageUrl, setImageUrl] = useState('');
  useEffect(() => {
    //   const loadImages = async () => {
    //     const blob = await getEmpPhoto(personId as number);
    //     if (blob) setImageUrl(URL.createObjectURL(blob));
    //   };
    //   loadImages();

    // console.log(personId);
    setImageUrl('');
  }, []);
  return (
    <Tooltip title={showTooltip ? fullName : undefined}>
      {isSelected ? (
        imageUrl ? (
          <img src={imageUrl} className="h-full w-full rounded-full" />
        ) : (
          <Avatar
            className="border-solid border-primary border-2 "
            size={size}
            style={{
              backgroundColor: fullName ? '#' + intToRGB(hashCode(fullName)) : '',
              color: fullName && isLightColour('#' + intToRGB(hashCode(fullName))) ? 'black' : 'white',
            }}
            src={imageUrl || undefined}
            shape="circle"
          >
            {!imageUrl && <>{value}</>}
          </Avatar>
        )
      ) : (
        'me'
      )}
    </Tooltip>
  );
};

export default AppAvatar;
