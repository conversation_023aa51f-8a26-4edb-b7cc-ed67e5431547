import { ServiceRequestResponse } from '@api/READ_ONLY/service_request_api/Api';
import { StateCreator } from 'zustand';

export interface ServiceRequestsSlice {
  serviceRequests: ServiceRequestResponse[];
  loadingServiceRequests: boolean;
  setServiceRequests: (serviceRequests: ServiceRequestResponse[]) => void;
  setLoadingServiceRequests: (loading: boolean) => void;
}

export const createServiceRequestsSlice: StateCreator<ServiceRequestsSlice> = (set) => ({
  serviceRequests: [],
  loadingServiceRequests: false,
  setServiceRequests: (serviceRequests) => set({ serviceRequests }),
  setLoadingServiceRequests: (loading) => set({ loadingServiceRequests: loading }),
});
