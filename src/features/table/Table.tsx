import { ExtendedTableColumnType } from '@app/types/table.types';
import { parseSorting } from '@app/utils/parseSorting';
import { useTable } from '@context/table/TableProvider';
import { useUrlParams } from '@context/urlParams/UrlParamProvider';
import { Table as AntdTable, Empty } from 'antd';
import { ColumnGroupType, ColumnType } from 'antd/es/table';
import { useEffect, useRef, useState } from 'react';
import TableFilters from './TableFilters';

type props<T> = {
  data: T[];
  totalData: number;
  loadingDataFetch: boolean;
  actions?: (record: T) => void;
  onRowClick?: (record: T) => void;
  hideActionsColumn?: boolean;
  rowKey: keyof T;
  addNavigate: () => void;
  exportComponent: React.ReactElement;
  localStorageKey: string;
};
function Table<T>({
  data,
  totalData,
  loadingDataFetch = true,
  onRowClick,
  hideActionsColumn = false,
  rowKey,
  addNavigate,
  exportComponent,
  localStorageKey,
}: props<T>) {
  const { handleTableChange } = useTable();
  const [showEmpty, setShowEmpty] = useState(false);
  const { columns, setColumns } = useTable();
  const { params } = useUrlParams();
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [tableScrollHeight, setTableScrollHeight] = useState<string | undefined>(undefined);

  // Calculate optimal table height to ensure pagination is visible
  useEffect(() => {
    const calculateTableHeight = () => {
      if (tableContainerRef.current) {
        const viewportHeight = window.innerHeight;
        const tableContainer = tableContainerRef.current;
        const containerRect = tableContainer.getBoundingClientRect();
        const containerTop = containerRect.top;

        // Calculate available height for the table
        const availableHeight = viewportHeight - containerTop - 120; // Reserve 120px for pagination

        // Get the actual table element inside the container
        const tableElement = tableContainer.querySelector('.ant-table-tbody');
        if (tableElement) {
          const tableHeight = tableElement.scrollHeight;

          // Only apply scroll height if table content actually overflows the available space
          if (tableHeight > availableHeight) {
            setTableScrollHeight(`${availableHeight}px`);
          } else {
            setTableScrollHeight(undefined);
          }
        } else {
          setTableScrollHeight(undefined);
        }
      }
    };

    // Use a small delay to ensure table is rendered
    const timeoutId = setTimeout(calculateTableHeight, 100);
    window.addEventListener('resize', calculateTableHeight);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, [data.length, loadingDataFetch]);

  useEffect(() => {
    setColumns((columns) =>
      columns.map((column) => {
        const sortItem = params.sorting && parseSorting(params.sorting).find((s) => s.key === column.key);

        return {
          ...column,
          sortOrder: sortItem ? (sortItem.value === 'asc' ? 'ascend' : 'descend') : undefined,
        } as ExtendedTableColumnType<unknown>;
      })
    );
  }, [params.sorting, setColumns]);
  useEffect(() => {
    if (!loadingDataFetch && data.length === 0) {
      const timeout = setTimeout(() => setShowEmpty(true), 20);
      return () => clearTimeout(timeout);
    } else {
      setShowEmpty(false);
    }
  }, [loadingDataFetch, data]);
  console.log('data', data);
  // Filter out actions column if hideActionsColumn is true
  const filteredColumns = hideActionsColumn ? columns.filter((col) => col.key !== 'actions') : columns;
  return (
    <>
      <TableFilters<T>
        localStorageKey={localStorageKey}
        addNavigate={addNavigate}
        hasAdd={true}
        columns={columns as ExtendedTableColumnType<T>[]}
        Export={exportComponent}
      />
      <div ref={tableContainerRef} className="w-full h-full">
        <AntdTable<T>
          id="table"
          columns={filteredColumns as (ColumnType<T> | ColumnGroupType<T>)[]}
          dataSource={data}
          size={'middle'}
          bordered={true}
          loading={loadingDataFetch}
          scroll={{
            x: 'max-content',
            ...(tableScrollHeight && { y: tableScrollHeight }),
          }}
          locale={{
            emptyText: (
              <>
                {loadingDataFetch ? (
                  <>Loading Data...</>
                ) : (
                  showEmpty && (
                    <div className="flex justify-center items-center h-full">
                      <Empty className="text-base" description="No Data" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    </div>
                  )
                )}
              </>
            ),
          }}
          pagination={{
            size: 'default',
            position: ['bottomCenter'],
            current: params.pageIndex ? Number(params.pageIndex) + 1 : 0,
            pageSize: params.pageSize ? Number(params.pageSize) : 25,
            total: totalData,
            pageSizeOptions: ['10', '25', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
          }}
          onRow={(record) => ({
            onClick: () => {
              console.log('render onclick');
              onRowClick?.(record);
            },
            style: onRowClick ? { cursor: 'pointer' } : undefined,
          })}
          sortDirections={['ascend', 'descend']}
          onChange={handleTableChange}
          rowKey={rowKey}
          sticky={true}
        />
      </div>
    </>
  );
}

export default Table;
