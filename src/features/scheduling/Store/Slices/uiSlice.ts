import { StateCreator } from 'zustand';

export type UiSlice = {
  activeCaregiver: number | null;
  setActiveCaregiver: (id: number | null) => void;

  loadingClients: boolean;
  loadingVisits: boolean;
  loadingServices: boolean;
  loadingServiceTypes: boolean;
  loadingCaregivers: boolean;

  setLoadingClients: (val: boolean) => void;
  setLoadingVisits: (val: boolean) => void;
  setLoadingServices: (val: boolean) => void;
  setLoadingServiceTypes: (val: boolean) => void;
  setLoadingCaregivers: (val: boolean) => void;
};

export const createUiSlice: StateCreator<UiSlice> = (set) => ({
  activeCaregiver: null,
  setActiveCaregiver: (activeCaregiver) => set({ activeCaregiver }),

  loadingClients: false,
  loadingVisits: false,
  loadingServices: false,
  loadingServiceTypes: false,
  loadingCaregivers: false,

  setLoadingClients: (val) => set({ loadingClients: val }),
  setLoadingVisits: (val) => set({ loadingVisits: val }),
  setLoadingServices: (val) => set({ loadingServices: val }),
  setLoadingServiceTypes: (val) => set({ loadingServiceTypes: val }),
  setLoadingCaregivers: (val) => set({ loadingCaregivers: val }),
});
