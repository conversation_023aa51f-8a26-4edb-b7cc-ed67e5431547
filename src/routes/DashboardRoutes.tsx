import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import Dashboard from '@pages/Dashboard';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const DashboardPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Dashboard">
      <Dashboard />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  DashboardPage,
};
