import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Dayjs } from 'dayjs';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { fetchVisitsData, searchVisitsData } from '../utils/fetchVisitsData';

/**
 * Custom hook for managing visit search functionality
 * Handles debounced search with minimum character requirements
 * @param currentDate - The current selected date
 * @returns Object containing search function and state
 */
export const useVisitSearch = (currentDate: Dayjs) => {
  const setVisits = useSchedulingStore((state) => state.setVisits);

  const searchVisits = useCallback(
    async (term: string) => {
      // Don't search for 1-2 characters to avoid too many API calls
      if (term.length === 1 || term.length === 2) return;

      const shouldOmitQuery = term == null || (typeof term === 'string' && term.trim().length === 0);

      if (shouldOmitQuery) {
        // If search is cleared, reload all visits for the current date
        const visits = await fetchVisitsData(currentDate);
        setVisits(visits?.data.data || []);
        return;
      }

      // Search with the term
      const visits = await searchVisitsData(term, currentDate);
      setVisits(visits.data.data);
    },
    [currentDate, setVisits]
  );

  // Create debounced version of search function
  const debouncedSearch = useMemo(() => debounce(searchVisits, 500), [searchVisits]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  return {
    onSearchTermChange: debouncedSearch,
  };
};
