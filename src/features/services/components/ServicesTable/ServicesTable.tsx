import Table from '@app/features/table/Table';
import { APP_PREFIX, EDIT_SERVICE, NEW_SERVICE, SERVICE_PAGE } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const ServiceTable = () => {
  const navigate = useNavigate();
  const services = useSchedulingStore((state) => state.services);
  const loading = useSchedulingStore((state) => state.loadingServices);

  return (
    <div className="w-full h-full">
      <Table
        rowKey={'serviceId'}
        data={services}
        totalData={services.length}
        loadingDataFetch={loading}
        addNavigate={() => navigate(`/${APP_PREFIX}/${SERVICE_PAGE}/${NEW_SERVICE}`)}
        exportComponent={<></>}
        localStorageKey="ServicesTable"
        onRowClick={(record) => {
          const path = `/${APP_PREFIX}/${SERVICE_PAGE}/${EDIT_SERVICE.replace(':id', String(record.serviceId))}`;
          navigate(path, { state: record });
        }}
      />
    </div>
  );
};

export default ServiceTable;
