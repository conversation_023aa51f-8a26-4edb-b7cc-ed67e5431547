import { useAuthContext } from '@context/auth/useAuthContext';
import { getAuthToken } from '@feat-auth/auth.config';
import axios, { AxiosInstance } from 'axios';
import { useEffect } from 'react';

const setupAxios = (axios: AxiosInstance, removeAuthentication: () => void) => {
  axios.defaults.headers.Accept = 'application/json';
  axios.defaults.headers['Content-Type'] = 'application/json';

  axios.interceptors.request.use(
    (config) => {
      const auth = getAuthToken();
      if (auth) {
        config.headers.Authorization = `Bearer ${auth}`;
      }
      return config;
    },
    (err) => Promise.reject(err)
  );

  axios.interceptors.response.use(
    (response) => {
      return Promise.resolve(response);
    },
    (error) => {
      if (error?.response?.status === 401) {
        localStorage.setItem('loggedOut', 'false');
        removeAuthentication();
        return Promise.resolve(true);
      }
      return Promise.reject(error);
    }
  );
};

const UseAxiosInitSetup = () => {
  const { removeAuthentication } = useAuthContext(); // Get auth state from context

  useEffect(() => {
    setupAxios(axios, removeAuthentication);
  }, [removeAuthentication]);

  return null;
};

export default UseAxiosInitSetup;
