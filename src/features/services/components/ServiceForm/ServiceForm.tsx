import { ApiClient } from '@api/api-configuration';
import { Service, ServiceCreate } from '@api/READ_ONLY/services_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, SERVICE_LIST, SERVICE_PAGE } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getServicesFormConfig } from './form.config';

type Props = {
  data?: Service;
};
export function ServiceForm({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();
  const setServiceTypes = useSchedulingStore((s) => s.setServiceTypes);
  const serviceTypes = useSchedulingStore((s) => s.serviceTypes);

  const fields = getServicesFormConfig(serviceTypes);
  const onSubmit = async (formData: ServiceCreate) => {
    try {
      console.log(formData);
      const response = await ApiClient.serviceApi.services.createServiceServicesPost(formData);
      console.log('res', response);
      openNotification('topRight', {
        title: `Service`,
        description: 'Service created successfully. ',
        type: 'Success',
      });
      console.log(`${APP_PREFIX}/${SERVICE_PAGE}/${SERVICE_LIST}`);
      navigate(`/${APP_PREFIX}/${SERVICE_PAGE}/${SERVICE_LIST}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Caregiver creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };
  useEffect(() => {
    const fetchTypes = async () => {
      const res = await ApiClient.serviceApi.serviceTypes.getServiceTypesServiceTypesGet();
      setServiceTypes(res.data.data);
    };
    fetchTypes();
  }, [setServiceTypes]);
  return <FormWrapper defaultValues={data} fields={fields} onSubmit={onSubmit} />;
}
