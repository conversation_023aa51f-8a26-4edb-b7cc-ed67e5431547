import React from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';

const TestCalendar = () => {
  const visits = [
    {
      id: 'visit-1',
      title: 'Visit A',
      start: '2025-07-02T09:00:00',
      end: '2025-07-02T10:00:00',
    },
    {
      id: 'visit-2',
      title: 'Visit B',
      start: '2025-07-02T11:30:00',
      end: '2025-07-02T12:30:00',
    },
  ];

  const availabilityEvents = [
    {
      id: 'availability-1',
      start: '2025-07-02T08:00:00',
      end: '2025-07-02T13:00:00',
      display: 'background',
      backgroundColor: 'rgba(0, 255, 0, 0.2)', // light green
    },
  ];

  const allEvents = [...visits, ...availabilityEvents];

  return (
    <FullCalendar
      plugins={[timeGridPlugin]}
      initialView="timeGridDay"
      events={allEvents}
      height={600}
      slotMinTime="06:00:00"
      slotMaxTime="18:00:00"
      nowIndicator={true}
      eventOverlap={true}
      allDaySlot={false}
    />
  );
};

export default TestCalendar;
