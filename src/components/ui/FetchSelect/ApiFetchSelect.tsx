import { CommonListResponse } from '@app/types/table.types';
import React, { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import SearchSelect, { TSearchSelectNewValuesProps } from './SearchSelect';

export type FetchDataFn<T> = (
  searchValue?: string,
  pageSize?: number,
  pageIndex?: number
) => Promise<CommonListResponse<T> | undefined>;

type Props<T> = {
  value: { label: string; value: number | string }[] | string | undefined;
  data: CommonListResponse<T>;
  fetchData: FetchDataFn<T>;
  getId: (item: T) => string | number;
  getLabel: (item: T) => string | React.ReactNode;
  onValueChange?: (item?: T | T[] | string) => void;
  debounce?: number;
  disabled?: boolean;
  placeholder?: string;
  mode?: 'tags' | 'multiple';
  readOnly?: boolean;
  canAdd?: boolean;
  style?: React.CSSProperties;
  canAddNew?: boolean;
  isClearable?: boolean;
  notFoundContent?: ReactNode; // Add support for custom no data text
  OptionComponent?: (option: T) => React.ReactElement;
  onValueSelect?: (item: T) => void;
  onValueDeselect?: (item: string | number) => void;
  onClear?: () => void;
  optionRender?: (option: T) => ReactNode;
  optionDisable?: (item: T) => boolean;
  onSearchChange?: (data: string) => void;
  onCreateNew?: (input: string) => void;
} & TSearchSelectNewValuesProps;

function FetchSearchSelect<T>({
  debounce = 500,
  value,
  disabled,
  fetchData,
  getId,
  getLabel,
  onValueChange,
  data,
  placeholder = 'Select',
  OptionComponent,
  mode,
  readOnly = false,
  canAddNew,
  onCreateNew,
  onValueSelect,
  onValueDeselect,
  onClear,
  optionRender,
  optionDisable,
  allowNewValue,
  newValueText,
  newValueCb = () => {},
  onSearchChange,
  style = {},
  isClearable = true,
  notFoundContent,
}: Props<T>) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [response, setResponse] = useState<CommonListResponse<T>>({
    Results: [],
    TotalResults: 0,
  });

  const ajaxSize = 10;

  const handleScrollSetData = useCallback(
    async (searchValue: string, ajaxSize: number, ajaxIndex: number) => {
      const res = await fetchData(searchValue, ajaxSize, ajaxIndex);
      if (res) {
        setResponse((d) => ({
          TotalResults: res?.TotalResults ?? 0,
          Results: [...d.Results, ...(res?.Results ?? [])],
        }));
      }
    },
    [fetchData]
  );

  const _onSearchChangeApi = useCallback(
    (searchValue: string) => {
      //if search value is more than 3 characters we do the api call. or if its zero or not exists. if there one or two ccharacters we do nothing
      const dontSearch = searchValue.length === 1 || searchValue.length === 2;

      if (dontSearch) return;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      console.log('running');
      timeoutRef.current = setTimeout(async () => {
        const res = await fetchData(searchValue, ajaxSize, 0);
        if (res) {
          setResponse(res);
        }
      }, debounce);
    },
    [debounce, fetchData]
  );

  useEffect(() => {
    setResponse(data);
  }, [data]);

  return (
    <SearchSelect<T>
      disabled={disabled}
      ajaxSize={ajaxSize}
      data={response}
      value={value}
      onValueChange={onValueChange}
      onSearchChange={onSearchChange ?? _onSearchChangeApi}
      handleScrollSetData={handleScrollSetData}
      getId={getId}
      getLabel={getLabel}
      placeholder={placeholder}
      OptionComponent={OptionComponent}
      mode={mode}
      readOnly={readOnly}
      onValueSelect={onValueSelect}
      onValueDeselect={onValueDeselect}
      onClear={onClear}
      allowNewValue={allowNewValue}
      newValueCb={newValueCb}
      newValueText={newValueText}
      optionRender={optionRender}
      optionDisable={optionDisable}
      style={style}
      canAddNew={canAddNew}
      onCreateNew={onCreateNew}
      isClearable={isClearable}
      notFoundContent={notFoundContent}
    />
  );
}

export default FetchSearchSelect;
