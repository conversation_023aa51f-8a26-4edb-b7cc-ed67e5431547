import { CalendarOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import FullCalendar from '@fullcalendar/react';
import { Button, DatePicker, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';

const { Text } = Typography;

type Props = {
  mode?: 'day' | 'week';
  calendarRef?: React.RefObject<FullCalendar | null>;
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
};

const DateNavigator: React.FC<Props> = ({ mode = 'day', calendarRef, currentDate, setCurrentDate }) => {
  const [showPicker, setShowPicker] = useState(false);

  const calendarApi = calendarRef?.current?.getApi();

  const updateDate = (newDate: dayjs.Dayjs) => {
    setCurrentDate(newDate);
    if (mode === 'day') {
      calendarApi?.gotoDate(newDate.format('YYYY-MM-DD'));
    }
  };

  const handlePrev = () => {
    if (mode === 'day' && calendarApi) {
      calendarApi.prev();
      setCurrentDate(dayjs(calendarApi.getDate()));
    } else if (mode === 'week') {
      setCurrentDate(currentDate.subtract(1, 'week'));
    }
  };

  const handleNext = () => {
    if (mode === 'day' && calendarApi) {
      calendarApi.next();
      setCurrentDate(dayjs(calendarApi.getDate()));
    } else if (mode === 'week') {
      setCurrentDate(currentDate.add(1, 'week'));
    }
  };

  return (
    <div className="flex justify-center items-center mb-4 space-x-4 px-2">
      <Button type="text" className="hover:bg-gray-300 rounded" icon={<LeftOutlined />} onClick={handlePrev} />

      <div className="min-w-64 w-full text-center justify-center ">
        {showPicker ? (
          <DatePicker
            open
            picker={mode === 'week' ? 'week' : 'date'}
            onOpenChange={(open) => setShowPicker(open)}
            defaultValue={currentDate}
            onChange={(date) => date && updateDate(date)}
            format="D MMMM YYYY"
            placement="bottomLeft"
          />
        ) : (
          <Button type="text" icon={<CalendarOutlined />} onClick={() => setShowPicker(true)}>
            <Text style={{ fontSize: '20px', fontWeight: 600 }}>
              {mode === 'week'
                ? `${currentDate.startOf('week').format('D MMM')} - ${currentDate.endOf('week').format('D MMM')}`
                : currentDate.format('D MMMM YYYY')}
            </Text>
          </Button>
        )}
      </div>

      <Button type="text" className="hover:bg-gray-300 rounded" icon={<RightOutlined />} onClick={handleNext} />
    </div>
  );
};

export default DateNavigator;
