openapi: 3.0.0
info:
  title: Dummy API
  version: 1.0.0

paths:
  /users:
    get:
      summary: Get all users
      operationId: getUsers
      tags:
        - Users
      responses:
        '200':
          description: A list of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'

    post:
      summary: Create a new user
      operationId: createUser
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '201':
          description: User created

  /users/{id}:
    get:
      summary: Get a user by ID
      operationId: getUserById
      tags:
        - Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: A single user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          description: User not found

    put:
      summary: Update a user by ID
      operationId: updateUser
      tags:
        - Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: User updated
        '404':
          description: User not found

    delete:
      summary: Delete a user by ID
      operationId: deleteUser
      tags:
        - Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: User deleted
        '404':
          description: User not found

  /posts:
    get:
      summary: Get all posts
      operationId: getPosts
      tags:
        - Posts
      responses:
        '200':
          description: A list of posts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Post'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string

    Post:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        content:
          type: string
        authorId:
          type: integer
