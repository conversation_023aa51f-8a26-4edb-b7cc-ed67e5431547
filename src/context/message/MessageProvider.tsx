import { message } from 'antd';
import { memo, ReactNode } from 'react';
import { MessageContext, MessageContextType } from './MessageContext';

interface MessageProviderProps {
  children: ReactNode;
}

/**
 * Message provider that wraps Ant Design's message API
 * Provides a clean interface for showing messages throughout the app
 */
const MessageProvider = memo(({ children }: MessageProviderProps) => {
  const [messageApi, contextHolder] = message.useMessage();

  const showSuccess = (content: string) => {
    messageApi.success(content);
  };

  const showError = (content: string) => {
    messageApi.error(content);
  };

  const showWarning = (content: string) => {
    messageApi.warning(content);
  };

  const showInfo = (content: string) => {
    messageApi.info(content);
  };

  const showLoading = (content: string) => {
    return messageApi.loading(content);
  };

  const value: MessageContextType = {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
  };

  return (
    <MessageContext.Provider value={value}>
      {contextHolder}
      {children}
    </MessageContext.Provider>
  );
});

MessageProvider.displayName = 'MessageProvider';

export default MessageProvider;
