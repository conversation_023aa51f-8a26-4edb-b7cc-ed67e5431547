import { FieldComponentProps } from '@app/types/form.types';
import { ChangePasswordFormWrapper } from '@pages/userProfile/ChangePasswordWrapper';
import React from 'react';

interface ChangePasswordClientWrapperProps extends FieldComponentProps<unknown> {
  // Add any additional props if needed
}

export const ChangePasswordClientWrapper: React.FC<ChangePasswordClientWrapperProps> = () => {
  // Since ChangePasswordFormWrapper doesn't accept props, we just render it
  return <ChangePasswordFormWrapper />;
};
