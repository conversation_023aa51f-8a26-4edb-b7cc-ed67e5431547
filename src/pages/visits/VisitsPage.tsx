import { ApiClient } from '@api/api-configuration';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import VisitTable from '@feat-visits/components/VisitTable/VisitTable';
import { getVisitTableColumns } from '@feat-visits/components/VisitTable/visitTableColumns.config';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};

const filters = [
  { Id: 0, label: 'Scheduled', value: false },
  { Id: 1, label: 'In Progress', value: false },
  { Id: 2, label: 'Completed', value: false },
  { Id: 3, label: 'Cancelled', value: false },
];

export const VisitsPage = () => {
  const columns: () => ExtendedTableColumnType<VisitPopulated>[] = useCallback(() => getVisitTableColumns(), []);
  const setVisits = useSchedulingStore((state) => state.setVisits);
  const setLoading = useSchedulingStore((state) => state.setLoadingVisits);

  return (
    <TableProvider
      storageKey="visitTable"
      storageKeyColumns="visitTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={filters}
      fetchData={async ({ searchTerm }) => {
        setLoading(true);
        try {
          const response = await ApiClient.visitsApi.visits.searchVisitsVisitsSearchGet({
            query: searchTerm,
          });
          setVisits(response.data.data);
        } catch (error) {
          console.error('Error fetching visits:', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <VisitTable />
    </TableProvider>
  );
};
