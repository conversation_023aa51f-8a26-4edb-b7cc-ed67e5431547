import { NotificationPlacement } from 'antd/es/notification/interface';
import { createContext } from 'react';

export type ToastOptions = {
  title: string;
  description: string;
  type: 'Light' | 'Danger' | 'Success' | 'Warning' | 'Info';
  duration?: number;
};

type NotificationsContextProps = {
  openNotification: (placement: NotificationPlacement, data: ToastOptions) => void;
};

const initNotificationsContextPropsState = {
  showNotification: () => {},
  hideNotification: () => {},
  setOptions: () => {},
  openNotification: () => {},
};

const NotificationsContext = createContext<NotificationsContextProps>(initNotificationsContextPropsState);
NotificationsContext.displayName = 'Notifications';

export default NotificationsContext;
