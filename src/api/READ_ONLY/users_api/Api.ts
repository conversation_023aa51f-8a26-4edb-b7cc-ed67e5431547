/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** RoleResponse */
export interface RoleResponse {
  /** Name */
  name: string;
  /** Code */
  code: string;
  /** Description */
  description?: string | null;
  /** Roleid */
  roleId: number;
}

/** UserCreateReq */
export interface UserCreateReq {
  /**
   * Email
   * @format email
   */
  email: string;
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
  /** Roles */
  roles: ("adm" | "cgv" | "clt")[];
}

/** UserResponse */
export interface UserResponse {
  /**
   * Email
   * @format email
   */
  email: string;
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
  /** Userid */
  userId: number;
  /**
   * Createdat
   * @format date-time
   */
  createdAt: string;
  /** Updatedat */
  updatedAt: string | null;
  /** Roles */
  roles: RoleResponse[];
}

/** UserSearchPaginationResponse */
export interface UserSearchPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: UserResponse[];
}

/** UserUpdateReq */
export interface UserUpdateReq {
  /** Email */
  email?: string | null;
  /** Firstname */
  firstName?: string | null;
  /** Lastname */
  lastName?: string | null;
  /** Avatarurl */
  avatarUrl?: string | null;
  /** Roles */
  roles?: ("adm" | "cgv" | "clt")[] | null;
}

/** UsersGetAllPaginationResponse */
export interface UsersGetAllPaginationResponse {
  /**
   * Total
   * Total number of items matching the filters
   */
  total: number;
  /**
   * Offset
   * Current offset in the result set
   */
  offset: number;
  /**
   * Limit
   * Maximum number of items returned
   */
  limit: number;
  /**
   * Data
   * Paged data
   */
  data: UserResponse[];
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  "body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  JsonApi = "application/vnd.api+json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || "/api/v1/users-api/",
    });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === "object"
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== "string"
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title users
 * @version 1.0.0
 * @baseUrl /api/v1/users-api/
 *
 *
 * Microservice for managing home care users.
 *
 * ## Features
 * * Create, read, update, and delete user records
 * * Secure endpoints with authentication and authorization
 *
 * ## Authentication
 * All endpoints require authentication using JWT tokens.
 * Admin endpoints require additional admin permissions.
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  health = {
    /**
     * @description Returns the health status and version of the service
     *
     * @tags Health
     * @name HealthCheckHealthGet
     * @summary Health check endpoint
     * @request GET:/health
     * @secure
     */
    healthCheckHealthGet: (params: RequestParams = {}) =>
      this.request<any, any>({
        path: `/health`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  users = {
    /**
     * @description Retrieve a list of all user roles
     *
     * @tags Roles
     * @name GetRolesUsersRolesGet
     * @summary Get all roles
     * @request GET:/users/roles
     * @secure
     */
    getRolesUsersRolesGet: (params: RequestParams = {}) =>
      this.request<RoleResponse[], void>({
        path: `/users/roles`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a list of all users with pagination support
     *
     * @tags Users
     * @name GetUsersUsersGet
     * @summary Get all users
     * @request GET:/users
     * @secure
     */
    getUsersUsersGet: (
      query?: {
        /**
         * Offset
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<UsersGetAllPaginationResponse, HTTPValidationError | void>({
        path: `/users`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a new user
     *
     * @tags Users
     * @name CreateUserUsersPost
     * @summary Create new user
     * @request POST:/users
     * @secure
     */
    createUserUsersPost: (data: UserCreateReq, params: RequestParams = {}) =>
      this.request<UserResponse, void | HTTPValidationError>({
        path: `/users`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a specific user by their ID
     *
     * @tags Users
     * @name GetUserUsersUserIdGet
     * @summary Get user by ID
     * @request GET:/users/{user_id}
     * @secure
     */
    getUserUsersUserIdGet: (userId: number, params: RequestParams = {}) =>
      this.request<UserResponse, void | HTTPValidationError>({
        path: `/users/${userId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Update an existing user's information
     *
     * @tags Users
     * @name UpdateUserUsersUserIdPut
     * @summary Update user
     * @request PUT:/users/{user_id}
     * @secure
     */
    updateUserUsersUserIdPut: (
      userId: number,
      data: UserUpdateReq,
      params: RequestParams = {},
    ) =>
      this.request<UserResponse, void | HTTPValidationError>({
        path: `/users/${userId}`,
        method: "PUT",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Delete a user by ID
     *
     * @tags Users
     * @name DeleteUserUsersUserIdDelete
     * @summary Delete user
     * @request DELETE:/users/{user_id}
     * @secure
     */
    deleteUserUsersUserIdDelete: (userId: number, params: RequestParams = {}) =>
      this.request<void, void | HTTPValidationError>({
        path: `/users/${userId}`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description Search for users by various criteria
     *
     * @tags Users
     * @name SearchUsersUsersSearchGet
     * @summary Search users
     * @request GET:/users/search
     * @secure
     */
    searchUsersUsersSearchGet: (
      query: {
        /**
         * Data
         * Search query (min 3 characters)
         * @minLength 3
         */
        data: string;
        /**
         * Offset
         * @min 0
         * @default 0
         */
        offset?: number;
        /**
         * Limit
         * @min 1
         * @max 500
         * @default 100
         */
        limit?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<UserSearchPaginationResponse, void | HTTPValidationError>({
        path: `/users/search`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),
  };
}
