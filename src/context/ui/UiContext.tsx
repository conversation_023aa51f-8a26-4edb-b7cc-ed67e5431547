import { createContext } from 'react';

interface UiContextProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  isSidebarCollapsed: boolean;
  toggleSidebarCollapse: () => void;
}

const defaultState: UiContextProps = {
  isSidebarOpen: true,
  toggleSidebar: () => {},
  isSidebarCollapsed: false,
  toggleSidebarCollapse: () => {},
};

const UiContext = createContext<UiContextProps>(defaultState);
UiContext.displayName = 'Ui Context';

export default UiContext;
