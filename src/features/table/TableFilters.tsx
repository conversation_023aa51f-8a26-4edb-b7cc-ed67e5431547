import { DebouncedInput } from '@app/components/ui/DebounceInput/DebounceInput';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { useUrlParams } from '@context/urlParams/UrlParamProvider';
import { ReactElement, useState } from 'react';
import { ResponsiveFiltersBar } from './ResponsiveFiltersBar/ResponsiveFilterBar';

type Props<T> = {
  extendedCalendar?: boolean;
  Export?: ReactElement;
  hasAdd?: boolean;
  setIsCreateModal?: (b: boolean) => void;
  meModeFilter?: boolean;
  addNavigate: () => void;
  columns: ExtendedTableColumnType<T>[];
  localStorageKey: string;
  dropdownFilter?: ReactElement;
  assigneesFilter?: boolean;
  leftArea?: ReactElement;
};

const TableFilters = <T,>({ Export, dropdownFilter, hasAdd = true, addNavigate, leftArea }: Props<T>) => {
  const [openCustomisation, setOpenCustomisation] = useState<boolean>(false);
  const { params, setParams, removeParams } = useUrlParams();

  // useEffect(() => {
  //   if (hasOrg) {
  //     fetchOrganisation()
  //       .then((res) => setOrganisations([DefaultOrg, ...res]))
  //       .catch((err) => console.log(err));
  //   }
  // }, []);

  return (
    <div className="flex w-full justify-between mb-2">
      <div className="flex justify-center items-center">
        <div className="example-header flex flex-row gap-2 ">
          <DebouncedInput
            onChange={(e) => {
              setParams({ searchTerm: e }, { isTableParam: true });
            }}
            initValue={params.searchTerm}
            className="form-control form-control-solid w-250px ps-14 mb-2"
            placeholder="Search..."
            type="search"
          />
          {leftArea && leftArea}
        </div>
      </div>
      <ResponsiveFiltersBar
        meModeFilter={false}
        assigneesFilter={false}
        dropdownFilter={dropdownFilter}
        hasAdd={hasAdd}
        Export={Export}
        addNavigate={addNavigate}
        handleReset={() => removeParams([''])}
        openCustomisation={openCustomisation}
        setOpenCustomisation={setOpenCustomisation}
      />
    </div>
  );
};

export default TableFilters;
