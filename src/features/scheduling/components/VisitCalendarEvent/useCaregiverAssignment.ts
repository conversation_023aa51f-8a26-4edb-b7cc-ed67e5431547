import { ApiClient } from '@api/api-configuration';
import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import dayjs from 'dayjs';

type VisitNotifiationAssigmentData = {
  visitId: number;
  visitStartTime: string;
  visitEndTime: string;
  clientId: number;
  clientFirstName: string;
  clientLastName: string;
  caregiverId: number;
  caregiverFirstName: string;
  caregiverLastName: string;
  address: string;
};

/**
 * Hook that manages caregiver assignment to visits
 */
export function useCaregiverAssignment() {
  const setVisits = useSchedulingStore((state) => state.setVisits);
  const visits = useSchedulingStore((state) => state.visits);
  const { openNotification } = useNotifications();

  const updateCaregiverOnVisit = async (visitId: number, caregiver: Caregiver) => {
    try {
      if (caregiver.caregiverId) {
        const visitResp = await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(visitId, {
          caregiverIds: [caregiver.caregiverId],
        });

        const visit = visitResp.data;

        const notificationVisitData: VisitNotifiationAssigmentData = {
          visitId: visit.id,
          visitStartTime: visit.startTime,
          visitEndTime: visit.endTime,
          clientId: visit.client!.clientId,
          clientFirstName: visit.client!.firstName || 'N/A',
          clientLastName: visit.client!.lastName || 'N/A',
          caregiverId: caregiver.caregiverId,
          caregiverFirstName: caregiver.firstName || 'N/A',
          caregiverLastName: caregiver.lastName || 'N/A',
          address: visit.client.street || 'N/A',
        };
        // Send notification to caregiver
        if (caregiver.userId) {
          await ApiClient.notificationsApi.notifications.sendNotificationNotificationsSendUserIdPost(caregiver.userId, {
            title: 'New Visit Assigned',
            body: `You have been assigned to a new visit`,
            data: notificationVisitData,
          });
        }

        // Show success notification
        openNotification('topRight', {
          title: `Visit Assigned`,
          description: `You've been assigned to Visit #${visit.id} on ${dayjs(visit.startTime).format('MMMM D')} ${dayjs(visit.startTime).format('HH:mm')} - ${dayjs(visit.endTime).format('HH:mm')} at ${visit.street}. Patient: ${visit.client.firstName} ${visit.client.lastName}`,
          type: 'Success',
        });

        // Update local state
        setVisits(visits.map((v) => (v.id === visitId ? visit : v)));
      }
    } catch (error) {
      openNotification('topRight', {
        title: `Visit`,
        description: 'Visit failed to update.',
        type: 'Warning',
      });
    }
  };

  return { updateCaregiverOnVisit };
}
