import { FieldGroup } from '@app/types/form.types';

export const doctorFormConfig: FieldGroup[] = [
  {
    title: 'Personal Information',
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        width: 'half',
        rules: { required: 'First name is required' },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        width: 'half',
        rules: { required: 'Last name is required' },
      },
      {
        name: 'email',
        label: 'Email',
        type: 'email',
        width: 'half',
        rules: {
          required: 'Email is required',
          pattern: {
            value: /^\S+@\S+$/i,
            message: 'Please enter a valid email',
          },
        },
      },
      {
        name: 'phone',
        label: 'Phone',
        type: 'text',
        width: 'half',
      },
    ],
  },
  {
    title: 'Professional Information',
    fields: [
      {
        name: 'specialization',
        label: 'Specialization',
        type: 'text',
        width: 'half',
      },
      {
        name: 'licenseNumber',
        label: 'License Number',
        type: 'text',
        width: 'half',
      },
      {
        name: 'isActive',
        label: 'Active Status',
        type: 'checkbox',
        width: 'half',
      },
    ],
  },
];
