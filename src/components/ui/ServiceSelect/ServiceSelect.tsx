import { ApiClient } from '@api/api-configuration';
import { Service } from '@api/READ_ONLY/caregiver_api/Api';
import { SelectProps } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import SearchSelectV2 from '../SearchSelectV2/SearchSelectV2';

type ServiceSelectProps = {
  onServiceChange?: (service: Service | Service[] | undefined) => void;
  placeholder?: string;
  pageSize?: number;
  minSearchLength?: number; // New: minimum search term length
  searchDebounceMs?: number; // New: debounce delay in milliseconds
} & Omit<SelectProps, 'options' | 'onChange' | 'loading' | 'onSearch'>;

export const ServiceSelect = ({
  onServiceChange,
  placeholder = 'Select Service',
  pageSize = 10,
  minSearchLength = 3, // Default: require at least 2 characters
  searchDebounceMs = 300, // Default: 300ms debounce
  ...selectProps
}: ServiceSelectProps) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentSearch, setCurrentSearch] = useState('');
  const [currentOffset, setCurrentOffset] = useState(0);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const loadServices = useCallback(
    async (search: string = '', offset: number = 0, append: boolean = false) => {
      if (loading) return;

      console.log({ search, offset, append });
      setLoading(true);
      const query: {
        limit: number;
        offset: number;
        query?: string;
      } = {
        limit: pageSize,
        offset,
      };

      if (search) {
        query.query = search.trim();
      }

      try {
        const response = await ApiClient.serviceApi.services.searchServicesServicesSearchGet(query);
        const resp = response.data;
        const newServices = resp.data;
        setServices((prev) => (append ? [...prev, ...newServices] : newServices));
        setTotalCount(resp.total);
        setHasMore(resp.offset + resp.data.length < resp.total);
        setCurrentOffset(resp.offset);
      } catch (error) {
        console.error('Failed to fetch services:', error);
        // Handle error appropriately
      } finally {
        setLoading(false);
      }
    },
    [pageSize, loading]
  );

  // Initial load
  useEffect(() => {
    loadServices();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearch = useCallback(
    (searchTerm: string) => {
      // Clear existing debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // If search term is too short, only update local state but don't search
      if (searchTerm.length > 0 && searchTerm.length < minSearchLength) {
        setCurrentSearch(searchTerm);
        // Don't make API call, just return
        return;
      }

      // Set up debounced search
      debounceTimerRef.current = setTimeout(() => {
        setCurrentSearch(searchTerm);
        setCurrentOffset(0);
        loadServices(searchTerm, 0, false);
      }, searchDebounceMs);
    },
    [loadServices, minSearchLength, searchDebounceMs]
  );

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const handleLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextOffset = currentOffset + pageSize;
      loadServices(currentSearch, nextOffset, true);
    }
  }, [loading, hasMore, currentOffset, pageSize, currentSearch, loadServices]);

  const handleChange = useCallback(
    (value: number | number[] | undefined) => {
      if (Array.isArray(value)) {
        // Multiple selection
        const selectedServices = services.filter((service) => value.includes(service.serviceId));
        onServiceChange?.(selectedServices);
      } else if (value) {
        // Single selection
        const selectedService = services.find((service) => service.serviceId === value);
        onServiceChange?.(selectedService);
      } else {
        // Clear selection
        onServiceChange?.(undefined);
      }
    },
    [services, onServiceChange]
  );

  // Transform services to Select options format
  const options = services.map((service) => ({
    value: service.serviceId,
    label: service.name,
    data: service,
  }));

  return (
    <SearchSelectV2
      {...selectProps}
      options={options}
      loading={loading}
      hasMore={hasMore}
      totalCount={totalCount}
      currentCount={services.length}
      placeholder={placeholder}
      onSearch={handleSearch}
      onLoadMore={handleLoadMore}
      onChange={handleChange}
      optionRender={(option) => {
        const service = option.data.data as Service;
        return <span>{service.name}</span>;
      }}
      inputSearchProps={{
        placeholder: `Search services... (min ${minSearchLength} characters)`,
      }}
    />
  );
};

export default ServiceSelect;
