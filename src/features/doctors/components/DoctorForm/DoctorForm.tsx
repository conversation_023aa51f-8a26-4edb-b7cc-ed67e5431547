import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { APP_PREFIX, DOCTOR_PAGE } from '@app/routes/urls';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { useNavigate } from 'react-router-dom';
import { doctorFormConfig } from './form.config';

type Props = {
  data?: Doctor;
};

export function DoctorFormInner({ data }: Props) {
  const { openNotification } = useNotifications();
  const navigate = useNavigate();

  const onSubmit = async (formData: unknown) => {
    try {
      console.log(formData);
      openNotification('topRight', {
        title: `Doctor`,
        description: 'Doctor created successfully. ',
        type: 'Success',
      });
      navigate(`/${APP_PREFIX}/${DOCTOR_PAGE}`);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Doctor`,
          description: 'Doctor creation failed. ' + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return <FormWrapper defaultValues={data} onSubmit={onSubmit} fields={doctorFormConfig} />;
}
