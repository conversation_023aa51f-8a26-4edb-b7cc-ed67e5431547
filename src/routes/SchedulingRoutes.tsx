import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import SchedulingPage from '@pages/scheduling/SchedulingPage';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const SchedulingRouteContainer = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Scheduling">
      <SchedulingPage />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default { SchedulingRouteContainer };
