import { ApiClient } from '@api/api-configuration';
import { useMessage } from '@context/message';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useState } from 'react';

export const useVisitCaregiverUpdate = () => {
  const [isLoading, setIsLoading] = useState(false);
  const updateVisitInStore = useSchedulingStore((state) => state.updateVisit);
  const { showSuccess, showError } = useMessage();

  const removeCaregiverFromVisit = async (visitId: number, caregiverIdToRemove: number) => {
    setIsLoading(true);
    try {
      // Get current visit to retrieve current caregiver IDs
      const visits = useSchedulingStore.getState().visits;
      const currentVisit = visits.find((v) => v.id === visitId);

      if (!currentVisit) {
        throw new Error('Visit not found');
      }

      // Get current caregiver IDs and filter out the one to remove
      const currentCaregiverIds = currentVisit.caregivers?.map((c) => c.caregiverId) || [];
      const updatedCaregiverIds = currentCaregiverIds.filter((id) => id !== caregiverIdToRemove);

      // Update visit via API
      await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(visitId, {
        caregiverIds: updatedCaregiverIds,
      });

      // Update store - remove the caregiver from the visit
      const updatedCaregivers = currentVisit.caregivers?.filter((c) => c.caregiverId !== caregiverIdToRemove) || [];

      updateVisitInStore({
        id: visitId,
        caregivers: updatedCaregivers,
      });

      // Success feedback
      showSuccess('Caregiver removed from visit successfully');
    } catch (error) {
      showError('Failed to remove caregiver from visit');
      console.error('Caregiver removal error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const assignCaregiverToVisit = async (visitId: number, caregiverIdToAdd: number) => {
    setIsLoading(true);
    try {
      // Get current visit to retrieve current caregiver IDs
      const visits = useSchedulingStore.getState().visits;
      const currentVisit = visits.find((v) => v.id === visitId);

      if (!currentVisit) {
        throw new Error('Visit not found');
      }

      // Get current caregiver IDs and add the new one
      const currentCaregiverIds = currentVisit.caregivers?.map((c) => c.caregiverId) || [];
      const updatedCaregiverIds = [...currentCaregiverIds, caregiverIdToAdd];

      // Update visit via API
      await ApiClient.visitsApi.visits.updateVisitVisitsVisitIdPut(visitId, {
        caregiverIds: updatedCaregiverIds,
      });

      // Note: In a real scenario, you'd need to fetch the updated visit to get the full caregiver data
      // For now, we'll just trigger a refresh or the parent component should handle the update

      // Success feedback
      showSuccess('Caregiver assigned to visit successfully');
    } catch (error) {
      showError('Failed to assign caregiver to visit');
      console.error('Caregiver assignment error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    removeCaregiverFromVisit,
    assignCaregiverToVisit,
    isLoading,
  };
};
