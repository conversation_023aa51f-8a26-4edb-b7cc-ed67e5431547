import { MenuItem, MenuStructure, RoutesStructure, RoutesStructureWithStringKeys } from '@app/types/routing.types';
import { useAuthContext } from '@context/auth/useAuthContext';
import getMenu from '@context/routing/utils/getMenu';
import { ReactNode, useCallback, useMemo } from 'react';
import { RoutingContext } from './RoutingContext';
import { generateRouteStructure, getAllPaths, getIndexRouteKey, getParentKey } from './utils/routerHelpers';
import RoutingTransformer from './utils/RoutingTransformer';

export const RoutingProvider = ({ children }: { children: ReactNode }) => {
  // const { allowIf, permissions, auth } = useAuthContext();
  // const { showFeature } = useFeatureContext();

  const { permissions, auth } = useAuthContext();

  const hasPermissions = useMemo(() => permissions.length > 0, [permissions]);

  const RouteConstruct: RoutesStructure = useMemo(() => {
    return generateRouteStructure(); // or generateRouteStructure(allowIf, showFeature);
  }, []);

  const menuStructure: MenuStructure = useMemo(() => {
    return getMenu(RouteConstruct); // or getMenu(RouteConstruct, showFeature);
  }, [RouteConstruct]);

  const filteredMenuStructure: MenuStructure = useMemo(() => {
    return filterMenu(menuStructure);
  }, [menuStructure]);

  const GetParentKey = useCallback((key: string) => getParentKey(filteredMenuStructure, key), [filteredMenuStructure]);

  const contextValue = useMemo(
    () => ({
      menuStructure,
      filteredMenuStructure,
      getIndexRouteKey: getIndexRouteKey(filteredMenuStructure, hasPermissions),
      permissions,
      getParentKey: GetParentKey,
      RouteConstruct,
      RouteConstructStrKeys: RouteConstruct as RoutesStructureWithStringKeys,
      toReactRouter: RoutingTransformer(RouteConstruct as RoutesStructureWithStringKeys),
      getAllPaths: () => getAllPaths(RouteConstruct),
    }),
    [menuStructure, filteredMenuStructure, permissions, GetParentKey, RouteConstruct, hasPermissions]
  );

  if (!auth) return null;

  return <RoutingContext.Provider value={contextValue}>{children}</RoutingContext.Provider>;
};

// Safe version of filterMenu with proper typing
function filterMenu(menu: MenuItem[]): MenuItem[] {
  return menu
    .map((item): MenuItem | null => {
      const children = item.children ? filterMenu(item.children) : undefined;

      const isVisible = item.show === true;
      const hasChildren = Array.isArray(children);
      const hasVisibleChildren = hasChildren && children.length > 0;

      if (isVisible && (!hasChildren || hasVisibleChildren)) {
        return { ...item, children };
      }

      return null;
    })
    .filter((item): item is MenuItem => item !== null);
}
