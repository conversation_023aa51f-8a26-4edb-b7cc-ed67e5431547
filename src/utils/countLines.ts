export function countLines(text: string, lineLength = 50) {
  let totalLines = 0;

  // Split by real newlines
  const rawLines = text.split(/\r?\n/);

  for (const rawLine of rawLines) {
    if (rawLine.length === 0) {
      totalLines += 1; // empty line still counts
    } else {
      // number of wrapped lines this raw line takes
      const chunks = Math.ceil(rawLine.length / lineLength);
      totalLines += chunks;
    }
  }

  return totalLines;
}
