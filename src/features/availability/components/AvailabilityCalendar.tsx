import { ApiClient } from '@api/api-configuration';
import { CaregiverAvailabilityResponse, Service } from '@api/READ_ONLY/caregiver_api/Api';
import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import { EventContentArg } from '@fullcalendar/core/index.js';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { GoPersonFill } from 'react-icons/go';
import { MdAddCircle } from 'react-icons/md';

dayjs.extend(utc);

type Props = {
  duration: number;
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
  childrenSidebar?: React.ReactNode;
};
type AvailabilityDataItem = {
  start: string;
  end: string;
  caregivers?: CaregiverAvailabilityResponse[];
};

type AvailabilityDataResponse = {
  [date: string]: AvailabilityDataItem[];
};
type AvailabilityEvent = {
  id: string;
  title: string;
  start: string;
  end: string;
  durationFormatted: string;
  caregivers: { caregiverId: number; firstName: string; lastName: string }[];
};

// ---------------------------
// EventContent Component
// ---------------------------
const EventContent = memo(({ eventInfo }: { eventInfo: EventContentArg }) => {
  const eventStart = dayjs(eventInfo.event.start);
  const eventEnd = dayjs(eventInfo.event.end);
  const now = dayjs();

  const caregivers = eventInfo.event.extendedProps.caregivers as AvailabilityEvent['caregivers'];
  const durationFormatted = eventInfo.event.extendedProps.durationFormatted as AvailabilityEvent['durationFormatted'];

  let opacity = 1;
  if (eventStart.isBefore(now, 'day') || (eventStart.isSame(now, 'day') && eventStart.hour() < now.hour())) {
    opacity = 0.25;
  }

  const { setValue } = useFormContext();

  return (
    <button
      type="button"
      onClick={() => {
        setValue('fromDate', eventStart.format());
        setValue('toDate', eventEnd.format());
      }}
      style={{ opacity }}
      className="w-full cursor-pointer rounded-xl border-0 h-full text-app-primary overflow-hidden bg-app-primary/25 duration-300 border-solid border-app-primary border-y-0 border-x-4 p-0.5"
    >
      <div className="flex justify-between items-center h-full">
        <div>{durationFormatted}</div>
        <Tooltip
          title={
            <div>
              {caregivers.map((c) => (
                <div key={c.caregiverId}>{c.firstName + ' ' + c.lastName}</div>
              ))}
            </div>
          }
        >
          <div className="flex justify-center items-center">
            <GoPersonFill />
            {caregivers.length}
          </div>
        </Tooltip>
      </div>
      <div className="flex cursor-pointer justify-center items-center absolute -bottom-[5px] left-1/2 -translate-x-1/2 rounded-full text-white">
        <MdAddCircle size={20} className="text-app-primary" />
      </div>
    </button>
  );
});
EventContent.displayName = 'EventContent';

const AvailabilityCalendar = ({ duration, currentDate, setCurrentDate }: Props) => {
  const calendarRef = useRef<FullCalendar | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [availabilityEvents, setAvailabilityEvents] = useState<AvailabilityEvent[]>([]);

  const {
    watch,
    formState: { errors },
  } = useFormContext();

  const nowHHMMSS = useMemo(() => dayjs().format('HH:mm:ss'), []);
  const caregiver = watch('caregiver');
  const service = watch('service');

  // Stable serviceIds string for dependency tracking
  const serviceIds = useMemo(() => (service ? service.map((s: Service) => s.serviceId) : []), [service]);
  const serviceIdsKey = useMemo(() => serviceIds.join(','), [serviceIds]);

  const getFullWeek = useCallback(() => {
    const startOfWeek = currentDate.startOf('week');
    const endOfWeek = currentDate.endOf('week');
    const dates: string[] = [];
    let d = startOfWeek;
    while (d.isBefore(endOfWeek) || d.isSame(endOfWeek, 'day')) {
      dates.push(d.format('YYYY-MM-DD'));
      d = d.add(1, 'day');
    }
    return dates;
  }, [currentDate]);

  useEffect(() => {
    const fetchAvailability = async () => {
      try {
        const response =
          await ApiClient.caregiverApi.caregivers.getAvailableCaregiversByDatesCaregiversAvailabilityByDatesPost({
            dates: getFullWeek(),
            durationMinutes: duration,
            caregiverIds: caregiver ? [caregiver.caregiverId] : [],
            serviceIds,
          });

        const availabilityData: AvailabilityDataResponse = response.data.eventsByDate || {};

        const calendarEvents: AvailabilityEvent[] = Object.values(availabilityData).flatMap(
          (events: AvailabilityDataItem[]) =>
            events.map((d: AvailabilityDataItem) => ({
              id: d.start + d.end,
              title: 'test',
              start: d.start,
              end: d.end,
              durationFormatted:
                duration % 60 === 0
                  ? `${Math.floor(duration / 60)}h`
                  : `${Math.floor(duration / 60)}h ${duration % 60}m`,
              caregivers: (d.caregivers || []).map((c: CaregiverAvailabilityResponse) => ({
                caregiverId: c.caregiverId,
                firstName: c.firstName,
                lastName: c.lastName,
              })),
            }))
        );

        setAvailabilityEvents(calendarEvents);
      } catch (err) {
        console.error('Failed to fetch availability', err);
      }
    };

    fetchAvailability();
  }, [currentDate, duration, caregiver?.caregiverId, serviceIdsKey, getFullWeek, caregiver, serviceIds]);

  useEffect(() => {
    if (!sidebarOpen && Object.keys(errors).length > 0) {
      setSidebarOpen(true);
    }
  }, [errors, sidebarOpen]);

  // Memoize events for FullCalendar
  const memoizedEvents = useMemo(() => availabilityEvents, [availabilityEvents]);

  return (
    <div className="flex flex-col h-full w-full relative">
      <DateNavigator currentDate={currentDate} setCurrentDate={setCurrentDate} calendarRef={calendarRef} />

      <div className="relative h-full w-full flex-1 overflow-hidden rounded-xl border border-solid border-app-gray-light bg-app-gray-light">
        <FullCalendar
          ref={calendarRef}
          firstDay={0}
          dayCellClassNames="bg-white"
          slotLaneClassNames="border-0"
          slotLabelClassNames="bg-app-gray-light"
          plugins={[timeGridPlugin, interactionPlugin]}
          initialView="timeGridWeek"
          headerToolbar={false}
          scrollTime={nowHHMMSS}
          allDaySlot={false}
          eventContent={(eventInfo) => <EventContent eventInfo={eventInfo} />}
          dayHeaderContent={(e) => {
            const d = e.date;
            const dayName = d.toLocaleString('en-US', { weekday: 'short' });
            const dayNum = d.getDate();
            return (
              <div className="flex items-center bg-app-gray-light font-semibold text-center mt-8">
                <div key={dayNum} className="flex-1 text-app-text-light flex items-start justify-center">
                  <div className="text-4xl font-light mr-4">{dayNum}</div>
                  <div className="flex flex-col items-start text-sm">
                    <div>{dayName}</div>
                    <div className="font-light">{dayjs(new Date()).format('MMM')}</div>
                  </div>
                </div>
              </div>
            );
          }}
          dayHeaderClassNames="bg-app-gray-light text-center align-middle h-16"
          expandRows
          height="100%"
          slotMinTime="00:00:00"
          slotMaxTime="24:00:00"
          eventClassNames="!bg-transparent !shadow-none"
          eventBackgroundColor="!bg-transparent !shadow-none"
          eventBorderColor="transparent"
          nowIndicator
          selectable
          editable={false}
          locale="el"
          slotDuration="00:15:00"
          slotLabelFormat={{ hour: 'numeric', minute: '2-digit', hour12: false }}
          dayHeaderFormat={{ weekday: 'short', month: 'short', day: 'numeric' }}
          events={memoizedEvents}
        />
      </div>
    </div>
  );
};

export default memo(AvailabilityCalendar);
