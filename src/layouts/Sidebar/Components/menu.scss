@layer components {
  $boxDimensions: 15px;
  $radiusDim: 8px;

  .menu-pill::before,
  .menu-pill::after {
    width: $boxDimensions;
    height: $boxDimensions;
  }
  .menu-pill::before {
    content: '';
    position: absolute;
    top: -15px;
    right: 0;
    background: radial-gradient(circle at top left, #f5f5f5 $boxDimensions, #ffffff $radiusDim);
  }

  .menu-pill::after {
    content: '';
    position: absolute;
    bottom: -15px;
    right: 0;
    background: radial-gradient(circle at bottom left, #f5f5f5 $boxDimensions, #ffffff $radiusDim);
  }
}
