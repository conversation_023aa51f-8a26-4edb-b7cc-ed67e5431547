import { ApiClient } from '@api/api-configuration';
import { ClientUpdate } from '@api/READ_ONLY/client_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { Button } from 'antd';
import React from 'react';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import ChangePasswordForm, { ChangePasswordFormValues } from './ChangePasswordForm';

export const ChangePasswordFormWrapper: React.FC = () => {
  const { id } = useParams();
  const { openNotification } = useNotifications();

  const methods = useForm<ChangePasswordFormValues>({
    defaultValues: {
      old_password: '',
      new_password: '',
      confirm_password: '',
    },
  });

  const onSubmit: SubmitHandler<ChangePasswordFormValues> = async (data) => {
    try {
      console.log('submitting', data);
      if (data.new_password === data.confirm_password) {
        if (id) {
          await ApiClient.clientApi.clients.updateClientClientsClientIdPut(Number(id), data as unknown as ClientUpdate);
          openNotification('topRight', {
            title: `Password change`,
            description: 'Password updated successfully.',
            type: 'Success',
          });
        }
      }
    } catch (err) {
      console.error('Updating password err', err);
      openNotification('topRight', {
        title: `Password change`,
        description: 'Password change failed.',
        type: 'Warning',
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <ChangePasswordForm />
      <div className="z-50 flex w-full justify-end">
        <Button onClick={() => onSubmit(methods.getValues())} type="primary" htmlType="submit" size="large">
          Change Password
        </Button>
      </div>
    </FormProvider>
  );
};
