// src/components/FormField.tsx
import { AddressSearchSelect } from '@app/components/ui/AddressPicker';
import { appTheme } from '@app/styles/app-theme';
import { FieldConfig } from '@app/types/form.types';
import { Checkbox, DatePicker, Input, InputNumber, Radio, Select, Tooltip } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import { JSX } from 'react';
import { Controller, ControllerRenderProps, FieldError, useFormContext } from 'react-hook-form';

export function FormField({ field }: { field: FieldConfig<unknown> }): JSX.Element {
  const {
    control,
    formState: { errors },
    setValue, // Add setValue to update other form fields
  } = useFormContext();

  const renderInput = (controllerField: ControllerRenderProps): JSX.Element => {
    const hasError = Boolean(errors[field.name]);

    // Handle custom components first
    if (field.component) {
      const CustomComponent = field.component;
      return (
        <CustomComponent
          value={controllerField.value}
          onChange={controllerField.onChange}
          error={hasError}
          {...(field.componentProps || {})}
        />
      );
    }

    switch (field.type) {
      case 'address': {
        const addressValue = controllerField.value
          ? typeof controllerField.value === 'string'
            ? {
                // If it's just a string, create a basic AddressValue object
                address: controllerField.value,
                city: '',
                country: '',
                postalCode: '',
                lat: 0,
                lng: 0,
              }
            : controllerField.value // It's already an AddressValue object
          : undefined;

        return (
          <AddressSearchSelect
            value={addressValue}
            setValue={(addressValue) => controllerField.onChange(addressValue)}
            placeholder={field.placeholder || 'Search address...'}
            disabled={field.disabled}
            noDataText="No addresses found for your search"
            onAddressSelect={(parsedAddress) => {
              // ✅ Auto-fill related form fields when address is selected
              console.log('🎯 Auto-filling form fields with address:', parsedAddress);

              if (parsedAddress.formattedAddress) {
                setValue('street', parsedAddress.street);
              }
              // Update city field if it exists
              if (parsedAddress.city) {
                setValue('city', parsedAddress.city);
              }

              // Update postalCode field if it exists
              if (parsedAddress.postalCode) {
                setValue('postalCode', parsedAddress.postalCode);
              }

              // Update latitude field if it exists
              setValue('geoLat', parsedAddress.location.lat);
              setValue('geoLng', parsedAddress.location.lng);
            }}
            {...(field.componentProps || {})}
          />
        );
      }

      case 'text':
      case 'email':
      case 'password':
        return (
          <Input
            size="large"
            {...controllerField}
            type={field.type === 'password' ? 'password' : 'text'}
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'number':
        return (
          <InputNumber
            className="!w-full"
            size="large"
            {...controllerField}
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'list':
        return (
          <Input
            size="large"
            value={controllerField.value ?? ''}
            onChange={(e) => controllerField.onChange(e.target.value)}
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'date':
        return (
          <DatePicker
            size="large"
            className="w-full"
            value={controllerField.value ? dayjs(controllerField.value) : null}
            onChange={(date) => controllerField.onChange(date ? date.toDate() : null)}
            onBlur={controllerField.onBlur}
            name={controllerField.name}
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'select':
        return (
          <Select
            size="large"
            className="w-full"
            value={controllerField.value}
            onChange={(val) => controllerField.onChange(val)}
            onBlur={controllerField.onBlur}
            options={field.options ?? []}
            allowClear
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'multiple-select':
        return (
          <Select
            size="large"
            mode="multiple"
            className="w-full"
            value={controllerField.value}
            onChange={(val) => controllerField.onChange(val)}
            onBlur={controllerField.onBlur}
            options={field.options ?? []}
            allowClear
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'textarea':
        return (
          <TextArea
            rows={4}
            {...controllerField}
            status={hasError ? 'error' : undefined}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      case 'checkbox':
        return (
          <Checkbox
            checked={!!controllerField.value}
            onChange={(e) => controllerField.onChange(e.target.checked)}
            onBlur={controllerField.onBlur}
            name={controllerField.name}
            className={hasError ? '!border-app-danger' : ''}
            disabled={field.disabled}
          >
            {field.label}
          </Checkbox>
        );

      case 'radiogroup':
        return (
          <Radio.Group
            {...controllerField}
            options={field.options ?? []}
            onChange={(val) => controllerField.onChange(val)}
            className={hasError ? '!border-app-danger' : ''}
            disabled={field.disabled}
          >
            {field.label}
          </Radio.Group>
        );

      case 'custom': {
        if (!field.component) return <></>;
        const CustomComponent = field.component as React.ComponentType<Record<string, unknown>>;
        return <CustomComponent {...controllerField} {...(field.componentProps || {})} />;
      }

      default:
        return (
          <Input
            {...controllerField}
            status={hasError ? 'error' : undefined}
            size="large"
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );
    }
  };

  const hasError = Boolean(errors[field.name]);

  return (
    <>
      {field.type !== 'checkbox' && field.type !== 'custom' && (
        <Tooltip title={String(field.label)} placement="topLeft" overlayStyle={{ maxWidth: 300 }}>
          <label
            className="text-ellipsis block overflow-hidden whitespace-nowrap"
            style={{ fontSize: appTheme.components?.InputNumber?.fontSize }}
          >
            {String(field.label)}
            {Boolean(field.rules?.required) && <span className="text-red-500 ml-1">*</span>}
          </label>
        </Tooltip>
      )}

      <Controller
        name={field.name}
        control={control}
        rules={field.rules}
        render={({ field: controllerField }) => renderInput(controllerField)}
      />

      {field.type !== 'checkbox' && field.type !== 'custom' && hasError && (
        <p className="text-app-danger text-xs mt-1">{(errors[field.name] as FieldError).message}</p>
      )}
    </>
  );
}
