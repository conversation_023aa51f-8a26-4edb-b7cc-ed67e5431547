import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { getInitials } from '@app/utils/extractFullnameInitials';
import { CaregiverAvatar } from '@feat-scheduling/components/CaregiverAvatar';
import { GoPlus } from 'react-icons/go';

type VisitAvatarProps = {
  visit: VisitPopulated;
  showRemoveButton?: boolean;
};

/**
 * Component that displays either a caregiver avatar or an assignment prompt
 */
export function VisitAvatar({ visit, showRemoveButton = false }: VisitAvatarProps) {
  const caregiver = visit.caregivers?.[0];

  if (caregiver) {
    const fullname = `${caregiver.firstName} ${caregiver.lastName}`;
    const fullnameLetters = getInitials(fullname, { fallback: '' });

    return (
      <CaregiverAvatar
        fullName={fullname}
        value={fullnameLetters}
        isSelected={true}
        showRemoveButton={showRemoveButton}
        visitId={visit.id}
        caregiverId={caregiver.caregiverId}
      />
    );
  }

  return (
    <div className="w-full h-full max-w-[20px] max-h-[20px] border border-app-text-dark text-app-text-dark border-dashed flex items-center justify-center rounded-full transition-transform duration-200 group-hover:scale-150">
      <GoPlus />
    </div>
  );
}
