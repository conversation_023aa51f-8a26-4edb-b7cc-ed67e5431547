import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import { generateRandomEventsForWeek } from '@app/utils/dummy_events_generator';
import interactionPlugin from '@fullcalendar/interaction'; // optional for interactivity
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Button, Popover, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useRef, useState } from 'react';
import { GoPersonFill } from 'react-icons/go';
import { IoFilter } from 'react-icons/io5';
import FiltersPanel from './FiltersPanel';

type Props = {
  duration: number;
  currentDate: Dayjs;
  setCurrentDate: (d: Dayjs) => void;
};

const AvailabilityCalendar = ({ duration, currentDate, setCurrentDate }: Props) => {
  const calendarRef = useRef<FullCalendar>(null);
  const [openCustomisation, setOpenCustomisation] = useState<boolean>(false);

  return (
    <div className="w-full">
      <div className="flex items-center ">
        <div className="flex-shrink-0 mb-4 px-2">
          <Popover
            open={openCustomisation}
            onOpenChange={setOpenCustomisation}
            trigger="click"
            placement="bottom"
            content={<FiltersPanel duration={0} setDuration={() => {}} />}
          >
            <Tooltip title={'Filters'}>
              <Button size="large" type="text" icon={<IoFilter />} />
            </Tooltip>
            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </Popover>
        </div>
        <div className="flex-grow">
          <DateNavigator currentDate={currentDate} setCurrentDate={setCurrentDate} calendarRef={calendarRef} />
        </div>
      </div>

      <div className="flex h-full w-full relative">
        <div className="relative flex-1  overflow-hidden rounded-xl border border-solid border-app-gray-light bg-app-gray-light">
          <FullCalendar
            ref={calendarRef}
            dayCellClassNames={'bg-white'}
            slotLaneClassNames={'border-0 '}
            slotLabelClassNames={'bg-app-gray-light'}
            plugins={[timeGridPlugin, interactionPlugin]}
            initialView="timeGridWeek"
            headerToolbar={false}
            allDaySlot={false}
            eventContent={(eventInfo) => {
              const eventStart = dayjs(eventInfo.event.start);
              const eventEnd = dayjs(eventInfo.event.end);
              const now = dayjs();

              let opacity = 1;
              if (eventStart.isBefore(now, 'day')) {
                opacity = 0.25;
              } else if (eventStart.isSame(now, 'day') && eventStart.hour() < now.hour()) {
                opacity = 0.25;
              }

              const durationInMinutes = eventEnd.diff(eventStart, 'minute');
              const hours = Math.floor(durationInMinutes / 60);
              const minutes = durationInMinutes % 60;

              const durationFormatted = [hours > 0 ? `${hours}h` : '', minutes > 0 ? `${minutes}m` : '']
                .filter(Boolean)
                .join(' ');

              return (
                <div
                  style={{ opacity }}
                  className="rounded-xl border-0 h-full text-app-primary overflow-hidden bg-app-primary/25 duration-300 border-solid border-app-primary border-y-0 border-x-4 p-0.5"
                >
                  <div className="flex justify-between items-center h-full">
                    <div>{durationFormatted}</div>
                    <Tooltip title="asdasd">
                      <div className="flex justify-center items-center">
                        <GoPersonFill />
                        {5}
                      </div>
                    </Tooltip>
                  </div>
                  <div className="flex justify-center items-center absolute bottom-0 translate-y-1/2 left-1/2 -translate-x-1/2 bg-app-primary text-lg text-app-gray-1 aspect-square w-4 h-4 rounded-full text-white">
                    +
                  </div>
                </div>
              );
            }}
            dayHeaderContent={(e) => {
              const d = e.date;
              const dayName = d.toLocaleString('en-US', { weekday: 'short' });
              const dayNum = d.getDate();

              return (
                <div className="flex items-center  bg-app-gray-light font-semibold text-center mt-8 ">
                  <div key={dayNum} className="flex-1 text-app-text-light flex items-start justify-center">
                    <div className="text-4xl font-light mr-4">{dayNum}</div>
                    <div className="flex flex-col items-start text-sm ">
                      <div className="">{dayName}</div>
                      <div className="font-light">{dayjs(new Date()).format('MMM')}</div>
                    </div>
                  </div>
                </div>
              );
            }}
            // viewClassNames={'!bg-app-gray-light'}
            dayHeaderClassNames={'bg-app-gray-light text-center align-middle h-16 '}
            expandRows={true} // Important for equal row spacing
            height="100%" // Inherits height from parent
            slotMinTime="08:00:00"
            slotMaxTime="20:00:00"
            eventClassNames={'!bg-transparent !shadow-none '}
            eventBackgroundColor="!bg-transparent !shadow-none "
            eventBorderColor="transparent"
            nowIndicator={true}
            selectable={true}
            editable={false}
            locale="el"
            slotDuration="00:30:00"
            slotLabelFormat={{
              hour: 'numeric',
              minute: '2-digit',
              hour12: false,
            }}
            dayHeaderFormat={{
              weekday: 'short',
              month: 'short',
              day: 'numeric',
            }}
            events={generateRandomEventsForWeek(50, duration)}
          />
        </div>
      </div>
    </div>
  );
};

export default AvailabilityCalendar;
