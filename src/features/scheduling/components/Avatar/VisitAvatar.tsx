import { memo } from 'react';

const VisitAvatar = ({ fullname }: { fullname: string }) => {
  const getInitials = (fullname: string) => {
    if (typeof fullname !== 'string') return 'N/A';

    const [first = '', second = ''] = fullname.trim().split(' ');

    const firstInitial = first.charAt(0).toUpperCase() || 'N';
    const secondInitial = second.charAt(0).toUpperCase() || 'A';

    return `${firstInitial}${secondInitial}`;
  };
  const initials = getInitials(fullname); // e.g., 'JD'
  return (
    <div className="flex items-center justify-center w-8 h-8 bg-app-secondary text-white rounded-full">{initials}</div>
  );
};

export default memo(VisitAvatar);
