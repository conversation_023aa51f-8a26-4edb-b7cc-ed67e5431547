import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';
import { ScrollProps } from '@app/types/ui.types';
import SearchFilter from '@feat-scheduling/components/VisitsList/shared/SearchFilter';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Empty } from 'antd';
import { memo } from 'react';
import SimpleBar from 'simplebar-react';
import DraggableCaregiver from './DraggableCaregiver';

type CaregiverProps = {
  caregivers: CaregiverWithServices[];
  filteredCaregivers?: CaregiverWithServices[];
  scrollProps: ScrollProps;
  onSearchTermChange?: (searchTerm: string) => void;
  noDataText: string;
};

const CaregiversList = ({
  caregivers = [],
  filteredCaregivers,
  scrollProps,
  onSearchTermChange,
  noDataText,
}: CaregiverProps) => {
  const activeVisit = useSchedulingStore((state) => state.activeVisit);
  const activeCaregiver = useSchedulingStore((state) => state.activeCaregiver);
  const setActiveCaregiver = useSchedulingStore((state) => state.setActiveCaregiver);

  const displayCaregivers = filteredCaregivers || caregivers;

  return (
    <div className="flex flex-col h-full min-h-0 overflow-hidden gap-2">
      <SearchFilter
        onValueChange={onSearchTermChange}
        title={<div className="flex justify-center">Caregivers</div>}
        placeholder={'Search caregivers'}
      />
      <div className="flex-1 min-h-0 overflow-hidden">
        {displayCaregivers.length > 0 ? (
          <SimpleBar
            forceVisible={scrollProps.forceVisible}
            autoHide={scrollProps.autoHide}
            className={`max-h-full  ${scrollProps.className || ''}`}
          >
            <div className="flex flex-col gap-1 h-full pr-1">
              {displayCaregivers.map((caregiver) => (
                <DraggableCaregiver
                  key={caregiver.caregiverId}
                  caregiver={caregiver}
                  isActive={caregiver.caregiverId === activeCaregiver}
                  onClick={() => setActiveCaregiver(caregiver.caregiverId)}
                />
              ))}
              {displayCaregivers.length === 0 && (
                <div className="flex h-[300px] justify-center items-center">
                  {!activeVisit ? <div>You need to select an event</div> : <>No caregiver available</>}
                </div>
              )}
            </div>
          </SimpleBar>
        ) : (
          <div className="h-full flex items-center justify-center">
            <Empty description={noDataText} className="w-40" />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(CaregiversList);
