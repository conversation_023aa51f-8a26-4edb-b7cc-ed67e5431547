import { AuthLayout } from '@pages/auth/AuthLayout';
import { Navigate, Route, Routes } from 'react-router-dom';
import { LOGIN } from './urls';

const AuthRoutes = () => (
  <Routes>
    <Route element={<AuthLayout />}>
      <Route index element={<Navigate to={LOGIN} replace />} />
      <Route path="*" element={<Navigate to={LOGIN} replace />} />
    </Route>
  </Routes>
);
export default AuthRoutes;
