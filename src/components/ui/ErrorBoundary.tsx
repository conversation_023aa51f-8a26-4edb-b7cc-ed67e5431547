import { <PERSON><PERSON>, Button } from 'antd';
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error Boundary component to catch and handle React errors gracefully
 * Provides a better UX than the default error screen
 */
class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // You can also log the error to an error reporting service here
    // Example: Sentry.captureException(error);
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined });
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <Alert
              type="error"
              showIcon
              message="Something went wrong"
              description={
                <div className="space-y-4">
                  <p>An unexpected error occurred. This has been logged and we&apos;ll look into it.</p>

                  {process.env.NODE_ENV === 'development' && this.state.error && (
                    <details className="text-xs bg-gray-100 p-2 rounded">
                      <summary className="cursor-pointer font-medium">Error Details (Development Only)</summary>
                      <pre className="mt-2 whitespace-pre-wrap">
                        {this.state.error.message}
                        {'\n'}
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}

                  <div className="flex gap-2">
                    <Button type="primary" onClick={this.handleReset}>
                      Try Again
                    </Button>
                    <Button onClick={this.handleReload}>Reload Page</Button>
                  </div>
                </div>
              }
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
