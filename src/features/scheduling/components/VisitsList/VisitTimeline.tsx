import { getVerticalLineStyling } from './utils';

type VisitTimelineProps = {
  index: number;
  totalVisits: number;
};

/**
 * Component that renders the timeline visual (dots and connecting lines)
 * Creates the vertical timeline appearance between visits
 */
export function VisitTimeline({ index, totalVisits }: VisitTimelineProps) {
  return (
    <div className="relative w-7">
      {/* Connecting line between visits */}
      {totalVisits > 1 && (
        <div
          className={`w-0.5 border-l-1 border-dashed border-app-primary-200 absolute left-1/2 -translate-x-1/2 z-0 ${getVerticalLineStyling(
            index,
            totalVisits
          )}`}
        />
      )}

      {/* Outer circle */}
      <div className="absolute top-1/2 left-1/2 w-6 h-6 rounded-full bg-app-primary-100/50 -translate-x-1/2 -translate-y-1/2" />

      {/* Inner circle */}
      <div className="absolute top-1/2 left-1/2 w-3 h-3 rounded-full bg-app-secondary-light/75 -translate-x-1/2 -translate-y-1/2" />
    </div>
  );
}
