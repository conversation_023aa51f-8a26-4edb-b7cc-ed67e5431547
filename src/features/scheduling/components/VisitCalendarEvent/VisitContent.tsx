import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import React, { memo } from 'react';
import { CaregiverBadge } from './CaregiverBadge';
import { ClientDisplay } from './ClientDisplay';
import { VisitLocation } from './VisitLocation';

type Props = {
  visit: VisitPopulated;
};

const VisitPopupContent: React.FC<Props> = ({ visit }) => {
  const caregiver = visit.caregivers ? visit.caregivers[0] : null;

  return (
    <div className="flex flex-col items-center justify-center gap-1 bg-app-secondary-extra-light py-2 px-4 rounded-xl">
      <ClientDisplay firstName={visit.client.firstName || ''} lastName={visit.client.lastName || ''} />
      <VisitLocation
        street={visit.client.street || ''}
        city={visit.client!.city || ''}
        postalCode={visit.client.postalCode || ''}
      />
      <CaregiverBadge caregiver={caregiver || undefined} />
    </div>
  );
};

export default memo(VisitPopupContent);
