import { useEffect, useState } from 'react';

type UseContentHeightOptions = {
  selector?: string;
  element?: HTMLElement | null;
};

export function useContentHeight({ selector, element }: UseContentHeightOptions) {
  const [contentHeight, setContentHeight] = useState<number | null>(null);

  useEffect(() => {
    const target = element ?? (selector ? document.querySelector<HTMLElement>(selector) : null);
    if (!target) return;

    const computed = window.getComputedStyle(target);
    const totalHeight = target.getBoundingClientRect().height;

    const paddingTop = parseFloat(computed.paddingTop || '0');
    const paddingBottom = parseFloat(computed.paddingBottom || '0');

    const heightWithoutPadding = totalHeight - paddingTop - paddingBottom;

    setContentHeight(heightWithoutPadding);
  }, [selector, element]);

  return contentHeight;
}
