import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Tag } from 'antd';

type Props = {
  selected: number[]; // use caregiver IDs
  setSelected: (d: number[]) => void;
};

const SuggestedCaregivers = ({ selected, setSelected }: Props) => {
  const caregivers = useSchedulingStore((state) => state.caregivers);

  const handleToggle = (id: number) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((n) => n !== id));
    } else {
      setSelected([...selected, id]);
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      {caregivers.map((caregiver) => {
        const isActive = selected.includes(caregiver.caregiverId);
        return (
          <Tag
            key={caregiver.caregiverId}
            onClick={() => handleToggle(caregiver.caregiverId)}
            style={{
              padding: '8px 16px',
              borderRadius: '9999px',
              cursor: 'pointer',
              backgroundColor: isActive ? '#3B82F6' : '#f3f4f6',
              color: isActive ? '#ffffff' : '#000000',
              transition: 'all 0.2s ease',
            }}
          >
            {caregiver.firstName} {caregiver.lastName}
          </Tag>
        );
      })}
    </div>
  );
};

export default SuggestedCaregivers;
