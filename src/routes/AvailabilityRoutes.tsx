import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import Availability from '@pages/Availability';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const AvailabilityPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Availability">
      <Availability />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  AvailabilityPage,
};
