import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import { StateCreator } from 'zustand';

export interface DoctorsSlice {
  doctors: Doctor[];
  loadingDoctors: boolean;
  setDoctors: (doctors: Doctor[]) => void;
  setLoadingDoctors: (loading: boolean) => void;
}

export const createDoctorsSlice: StateCreator<DoctorsSlice> = (set) => ({
  doctors: [],
  loadingDoctors: false,
  setDoctors: (doctors) => set({ doctors }),
  setLoadingDoctors: (loading) => set({ loadingDoctors: loading }),
});
