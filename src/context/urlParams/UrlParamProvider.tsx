import { createContext, ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';

function getParamsFromUrl(): Record<string, string> {
  const params: Record<string, string> = {};
  const searchParams = new URL(window.location.href, window.location.origin).searchParams;
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  return params;
}

interface UrlParamContextType {
  setParams: (
    params: Record<string, string | number | boolean | undefined | null>,
    options?: { replace?: boolean; isTableParam?: boolean }
  ) => void;
  removeParams: (keys: string[], options?: { replace?: boolean; isTableParam?: boolean }) => void;
  params: Record<string, string>;
}

const UrlParamContext = createContext<UrlParamContextType | undefined>(undefined);

interface UrlParamProviderProps {
  children: ReactNode;
  initializeParams?: (
    setParams: UrlParamContextType['setParams'],
    removeParams: UrlParamContextType['removeParams'],
    params: Record<string, string>
  ) => void;
}

export const UrlParamProvider = ({ children, initializeParams }: UrlParamProviderProps) => {
  const [params, setParamsState] = useState<Record<string, string>>(() => {
    const initial = getParamsFromUrl();
    console.log('[URLPARAM] [INIT] UrlParamProvider mounted with params:', initial);
    return initial;
  });

  // Track if this is the first navigation to the current route
  const isFirstNavigationToRoute = useRef(true);

  useEffect(() => {
    let currentPath = window.location.pathname;

    const updateParams = () => {
      const updated = getParamsFromUrl();
      const newPath = window.location.pathname;

      if (newPath !== currentPath) {
        console.log('[URLPARAM] [PATH CHANGE] Detected path change. Resetting params.');
        currentPath = newPath;
        isFirstNavigationToRoute.current = true; // Reset flag on route change
        setParamsState(updated);
        return;
      }

      console.log('[URLPARAM] [URL CHANGE] Params updated from URL:', updated);
      setParamsState(updated);
    };

    window.addEventListener('popstate', updateParams);

    const originalPushState = window.history.pushState;
    const originalReplaceState = window.history.replaceState;

    window.history.pushState = function (...args) {
      const result = originalPushState.apply(this, args);
      updateParams();
      return result;
    };

    window.history.replaceState = function (...args) {
      const result = originalReplaceState.apply(this, args);
      updateParams();
      return result;
    };

    return () => {
      window.removeEventListener('popstate', updateParams);
      window.history.pushState = originalPushState;
      window.history.replaceState = originalReplaceState;
    };
  }, []);

  const setParams = useCallback(
    (
      newParams: Record<string, string | number | boolean | undefined | null>,
      options: { replace?: boolean; isTableParam?: boolean } = {}
    ) => {
      const url = new URL(window.location.href);
      const urlSearchParams = new URLSearchParams(url.search);

      Object.entries(newParams).forEach(([key, value]) => {
        if (value === undefined || value === null || value === '') {
          urlSearchParams.delete(key);
        } else {
          urlSearchParams.set(key, String(value));
        }
      });

      url.search = urlSearchParams.toString();
      const newUrl = url.toString();

      console.log('[URLPARAM] [SET PARAMS] Applying new params:', Object.fromEntries(urlSearchParams.entries()));
      console.log('[URLPARAM] [SET PARAMS] New URL:', newUrl);

      // Determine whether to use replace or push
      const shouldReplace = options.replace || options.isTableParam || !isFirstNavigationToRoute.current;

      if (shouldReplace) {
        console.log('[URLPARAM] [SET PARAMS] Using replaceState to avoid history pollution');
        window.history.replaceState({}, '', newUrl);
      } else {
        console.log('[URLPARAM] [SET PARAMS] Using pushState for first navigation');
        window.history.pushState({}, '', newUrl);
        isFirstNavigationToRoute.current = false;
      }
    },
    []
  );

  const removeParams = useCallback((keys: string[], options: { replace?: boolean; isTableParam?: boolean } = {}) => {
    const url = new URL(window.location.href);
    const urlSearchParams = new URLSearchParams(url.search);

    keys.forEach((key) => urlSearchParams.delete(key));

    url.search = urlSearchParams.toString();
    const newUrl = url.toString();

    console.log('[URLPARAM] [REMOVE PARAMS] Removing keys:', keys);
    console.log('[URLPARAM] [REMOVE PARAMS] New URL:', newUrl);

    // Same logic as setParams for consistency
    const shouldReplace = options.replace || options.isTableParam || !isFirstNavigationToRoute.current;

    if (shouldReplace) {
      window.history.replaceState({}, '', newUrl);
    } else {
      window.history.pushState({}, '', newUrl);
      isFirstNavigationToRoute.current = false;
    }
  }, []);

  useEffect(() => {
    if (initializeParams) {
      console.log('[URLPARAM] [INIT PARAMS HOOK] Running initializeParams with:', params);
      initializeParams(setParams, removeParams, params);
    }
  }, [initializeParams, setParams, removeParams, params]);

  return <UrlParamContext.Provider value={{ setParams, removeParams, params }}>{children}</UrlParamContext.Provider>;
};

export function useUrlParams() {
  const context = useContext(UrlParamContext);
  if (!context) {
    throw new Error('useUrlParams must be used within a UrlParamProvider');
  }
  return context;
}
