import { ExtendedTableColumnType } from '@app/types/table.types';

function getStoredColumns<T>(localStorageKey: string): ExtendedTableColumnType<T>[] {
  try {
    const stored = localStorage.getItem(localStorageKey);
    if (!stored) return [];

    const parsed = JSON.parse(stored);

    // Optional: add validation logic here if needed
    if (Array.isArray(parsed)) {
      return parsed as ExtendedTableColumnType<T>[];
    }

    return [];
  } catch (error) {
    console.warn(`Failed to parse localStorage for key: ${localStorageKey}`, error);
    return [];
  }
}

export { getStoredColumns };
