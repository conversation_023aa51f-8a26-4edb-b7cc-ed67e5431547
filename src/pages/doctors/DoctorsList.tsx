import { ApiClient } from '@api/api-configuration';
import { Doctor } from '@api/READ_ONLY/doctors_api/Api';
import DoctorTable from '@app/features/doctors/components/DoctorTable/DoctorTable';
import { getDoctorTableColumns } from '@app/features/doctors/components/DoctorTable/doctorTableColumns.config';
import { ExtendedTableColumnType } from '@app/types/table.types';
import { TableProvider } from '@context/table/TableProvider';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useCallback } from 'react';

const initialFilters = {
  searchTerm: '',
  sorting: '',
};

export const DoctorsList = () => {
  const columns: () => ExtendedTableColumnType<Doctor>[] = useCallback(() => getDoctorTableColumns(), []);
  const setDoctors = useSchedulingStore((state) => state.setDoctors);
  const setLoading = useSchedulingStore((state) => state.setLoadingDoctors);

  return (
    <TableProvider
      storageKey="doctorTable"
      storageKeyColumns="doctorTableColumns"
      initialFilters={initialFilters}
      initialStatusFilters={[]}
      fetchData={async ({ searchTerm }) => {
        setLoading(true);
        try {
          const response = await ApiClient.doctorsApi.doctors.searchDoctorsDoctorsSearchGet({
            data: searchTerm,
          });
          setDoctors(response.data.data);
        } catch (error) {
          console.error('Error fetching doctors:', error);
        } finally {
          setLoading(false);
        }
      }}
      cols={columns()}
    >
      <DoctorTable />
    </TableProvider>
  );
};
