import { ApiClient } from '@api/api-configuration';
import { ClientUpdate } from '@api/READ_ONLY/client_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { Button } from 'antd';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { ChangeEmailForm, ChangeEmailFormValues } from './ChangeEmailForm';

export const ChangeEmailFormWrapper: React.FC = () => {
  const { id } = useParams();
  const { openNotification } = useNotifications();

  const methods = useForm<ChangeEmailFormValues>({
    defaultValues: {
      new_email: '',
      confirm_email: '',
    },
  });

  const onSubmit: SubmitHandler<ChangeEmailFormValues> = async (data) => {
    console.log('dataaaaa', data);
    try {
      if (data.new_email === data.confirm_email) {
        if (id) {
          await ApiClient.clientApi.clients.updateClientClientsClientIdPut(Number(id), {
            email: data.new_email,
          } as ClientUpdate);
          openNotification('topRight', {
            title: 'Email change',
            description: 'Email updated successfully.',
            type: 'Success',
          });
        }
      } else {
        openNotification('topRight', {
          title: 'Email change',
          description: 'Emails do not match',
          type: 'Warning',
        });
      }
    } catch (err) {
      console.error('Updating email error', err);
      openNotification('topRight', {
        title: 'Email change',
        description: 'Email change failed.',
        type: 'Warning',
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <ChangeEmailForm />
      <div className="z-50 justify-end flex w-full">
        <Button onClick={() => onSubmit(methods.getValues())} type="primary" htmlType="submit" size="large">
          Change Email
        </Button>
      </div>
    </FormProvider>
  );
};
