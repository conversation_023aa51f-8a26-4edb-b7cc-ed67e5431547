import { useAuthContext } from '@context/auth/useAuthContext';
import useNotifications from '@context/notifications/useNotificationContext';
import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Callback = () => {
  const {
    // setAuthToken,
    removeAuthentication,
  } = useAuthContext();
  const { openNotification } = useNotifications();
  const [loading, setLoading] = useState(true);

  const state = localStorage.getItem('nonce');
  const navigate = useNavigate();

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
      navigate('/dashboard', { replace: true });
    }, 1000);
  }, [navigate, removeAuthentication, openNotification, state]);
  return (
    <div className="justify-center items-center w-screen h-screen flex">
      <Spin spinning={loading} />
    </div>
  );
};

export default Callback;
