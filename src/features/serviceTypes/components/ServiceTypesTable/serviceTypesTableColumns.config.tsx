import { ServiceType } from '@api/READ_ONLY/services_api/Api';
import { Typography } from 'antd';

export type ServiceTypeTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: string, record?: T) => React.ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getServiceTypesTableColumns = (): ServiceTypeTableColumnType<ServiceType>[] => [
  {
    dataIndex: 'name',
    key: 'name',
    title: 'Name',
    width: 120,
    render: (name: string) => <Typography>{name}</Typography>,
  },
  {
    dataIndex: 'description',
    key: 'description',
    title: 'Description',
    width: 120,
    render: (name: string) => <Typography>{name}</Typography>,
  },
];
