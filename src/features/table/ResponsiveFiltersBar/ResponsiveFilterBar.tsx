import { FilterOutlined, PlusOutlined, RedoOutlined, SettingOutlined } from '@ant-design/icons';
import CButton from '@app/components/ui/Button/Button';
import { Button, Drawer, Dropdown, Popover, Space, Tooltip } from 'antd';
import { ReactNode, useState } from 'react';
import { BsFilter } from 'react-icons/bs';
import { GoColumns } from 'react-icons/go';
import TableColumnDropDown from '../TableColumnsList/TableColumnDropDown';

interface ResponsiveFiltersBarProps {
  meModeFilter?: ReactNode;
  assigneesFilter?: ReactNode;
  dropdownFilter?: ReactNode;
  hasAdd?: boolean;
  Export?: ReactNode;
  addNavigate?: () => void;
  handleReset?: () => void;
  openCustomisation?: boolean;
  setOpenCustomisation?: (open: boolean) => void;
  columnFilters?: boolean;
}

export const ResponsiveFiltersBar = ({
  meModeFilter,
  assigneesFilter,
  dropdownFilter,
  hasAdd,
  Export,
  addNavigate = () => {},
  handleReset = () => {},
  openCustomisation = false,
  setOpenCustomisation = () => {},
  columnFilters = true,
}: ResponsiveFiltersBarProps) => {
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  return (
    <>
      {/* Mobile layout */}
      <div className="md:hidden ">
        <Button type="text" size="large" icon={<FilterOutlined />} onClick={() => setShowMobileFilters(true)}></Button>

        <Drawer title="Filters" placement="bottom" onClose={() => setShowMobileFilters(false)} open={showMobileFilters}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* Section: Quick Filters */}
            {(meModeFilter || assigneesFilter) && (
              <div className="flex flex-col gap-2">
                {meModeFilter && (
                  <div className="p-3 border flex flex-row items-center justify-between rounded-md bg-gray-50">
                    <span className="block text-xs font-medium text-gray-500 mb-1">Me Mode</span>
                    {meModeFilter}
                  </div>
                )}

                {assigneesFilter && (
                  <div className="p-3  flex flex-row items-center justify-between border rounded-md bg-gray-50">
                    <span className="block text-xs font-medium text-gray-500 mb-1">Assignees</span>
                    {assigneesFilter}
                  </div>
                )}
              </div>
            )}

            {/* Table Customisation */}
            {columnFilters && (
              <div className="p-3  flex flex-row items-center justify-between border rounded-md bg-gray-50">
                <span className="block text-xs font-medium text-gray-500 mb-1">Table Customisation</span>
                <Button
                  size={'small'}
                  icon={<SettingOutlined />}
                  onClick={() => setOpenCustomisation(!openCustomisation)}
                />
              </div>
            )}

            {/* Status Filter */}
            {dropdownFilter && (
              <div className="p-3  flex flex-row items-center justify-between border rounded-md bg-gray-50">
                <span className="block text-xs font-medium text-gray-500 mb-1">Status</span>
                {dropdownFilter}
              </div>
            )}

            {/* Reset */}
            <div className="p-3 border  flex flex-row items-center justify-between rounded-md bg-gray-50">
              <span className="block text-xs font-medium text-gray-500 mb-1">Reset Filters</span>
              <Button size={'small'} icon={<RedoOutlined />} onClick={handleReset} />
            </div>

            {/* Export */}
            {Export && (
              <div className="p-3  flex flex-row items-center justify-between border rounded-md bg-gray-50">
                <span className="block text-xs font-medium text-gray-500 mb-1">Export</span>
                {Export}
              </div>
            )}

            {/* Add New */}
            {hasAdd && (
              <Tooltip title={'Add'}>
                <div className="p-3  flex flex-row items-center justify-between border rounded-md bg-gray-50">
                  <span className="block text-xs font-medium text-gray-500 mb-1">Add New Record</span>
                  <div>
                    <CButton icon={'add'} click={() => addNavigate()} />
                  </div>
                </div>
              </Tooltip>
            )}
          </Space>
        </Drawer>
      </div>

      {/* Desktop layout */}
      <div className="md:flex hidden flex-row  gap-1 items-center">
        {meModeFilter && (
          <>
            {meModeFilter}
            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </>
        )}
        {assigneesFilter && (
          <>
            {assigneesFilter}
            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </>
        )}

        {columnFilters && (
          <Popover
            open={openCustomisation}
            onOpenChange={setOpenCustomisation}
            trigger="click"
            placement="bottom"
            content={<TableColumnDropDown />}
          >
            <Tooltip title={'Toggle Columns'}>
              <Button size="large" type="text" icon={<GoColumns />} />
            </Tooltip>
            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </Popover>
        )}

        {dropdownFilter && (
          <>
            <Dropdown
              trigger={['click']}
              popupRender={() => (
                <div className="flex flex-col gap-2 p-2 bg-white border border-solid border-slate-200 rounded-xl shadow-md  min-w-48 max-w-96">
                  {dropdownFilter}
                </div>
              )}
            >
              <Tooltip title="Filter">
                <Button size={'large'} type="text" icon={<BsFilter />} />
              </Tooltip>
            </Dropdown>

            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </>
        )}

        {Export && (
          <>
            {Export}
            <div className=" h-1/2 border border-solid  border-neutral-200 last-of-type:hidden "></div>
          </>
        )}
        {hasAdd && (
          <Tooltip title={'Add'}>
            <Button
              type="primary"
              size="large"
              onClick={() => addNavigate()}
              icon={<PlusOutlined />}
              iconPosition="start"
              style={{ fontWeight: 'medium' }}
            />
          </Tooltip>
        )}
      </div>
    </>
  );
};
