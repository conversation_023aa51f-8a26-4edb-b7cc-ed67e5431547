"use strict";
(() => {
  // src/service-workers/firebase-messaging-sw.ts
  console.log("SERVICE WORKER FB STARTED");
  importScripts("https://cdn.jsdelivr.net/npm/idb-keyval@6/dist/umd.js");
  importScripts("https://www.gstatic.com/firebasejs/9.10.0/firebase-app-compat.js");
  importScripts("https://www.gstatic.com/firebasejs/9.10.0/firebase-messaging-compat.js");
  var NOTIFICATION_KEY = "notification";
  var img = `${self.location.origin}/media/logos/homecarelogo_sm.png`;
  console.log("img", img);
  self.addEventListener("notificationclick", (event) => {
    console.log("Click notification", event);
    const notificationData = event.notification || {};
    const notificationPayload = {
      title: notificationData.title || "N/A",
      body: notificationData.body || "N/A"
    };
    event.waitUntil(routing(notificationPayload, event));
  });
  async function routing(notificationData, event) {
    const windowClients = await self.clients.matchAll({ includeUncontrolled: true, type: "window" });
    console.log("clients", { clients: self.clients, windowClients });
    const postMessage = {
      type: "NOTIFICATION_CLICK",
      ...notificationData
    };
    console.log({ postMessage });
    const lastClient = windowClients.length > 0 && windowClients[windowClients.length - 1];
    console.log({ lastClient });
    if (lastClient) {
      console.log("inside lastClient", { lastClient });
      lastClient.focus();
      lastClient.postMessage({
        ...postMessage,
        msg: "from background - foreground - tab is there but not focused"
      });
      event.notification.close();
      return Promise.resolve(true);
    }
    await saveNotifToDb(postMessage);
    await self.clients.openWindow("/dashboard");
    event.notification.close();
    return Promise.resolve(true);
  }
  async function saveNotifToDb(notif) {
    console.log("saving notification to db", notif);
    return await idbKeyval.set(NOTIFICATION_KEY, notif);
  }
  var firebaseConfig = {
    apiKey: "AIzaSyClHnd6BzHefbhJg-arg6yqsP9mb39p4b8",
    authDomain: "homecare-2498a.firebaseapp.com",
    projectId: "homecare-2498a",
    storageBucket: "homecare-2498a.firebasestorage.app",
    messagingSenderId: "1063363203978",
    appId: "1:1063363203978:web:ee1a9263e338d61fa6ae73"
  };
  firebase.initializeApp(firebaseConfig);
  firebase.messaging();
  self.addEventListener("push", (event) => {
    console.log("Received push event", event);
    if (event.data) {
      const payload = event.data.json();
      console.log("Push data received", payload);
      if (!("data" in payload)) {
        console.error("No data in payload. Wrong notification schema", payload);
        return;
      }
      const notificationData = payload.notification;
      const title = notificationData?.title || "N/A";
      const body = notificationData?.body || "N/A";
      const notificationTitle = title;
      const notificationOptions = {
        body,
        icon: img
      };
      console.log("Generating notification", notificationOptions);
      event.waitUntil(self.registration.showNotification(notificationTitle, notificationOptions));
    }
  });
})();
