import { Client } from '@api/READ_ONLY/client_api/Api';
import { capitalizeString } from '@app/utils/fullNameCapilatizeFirst';
import { ClientDisplay } from '@feat-scheduling/components/VisitCalendarEvent/ClientDisplay';
import { Typography } from 'antd';
import { ReactNode } from 'react';

export type ClientTableColumnType<T> = {
  dataIndex?: string | string[];
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (cell: unknown, record: T) => ReactNode;
  default?: boolean;
  fixed?: 'left' | 'right';
};

export const getClientTableColumns = (): ClientTableColumnType<Client>[] => [
  {
    key: 'Name',
    title: 'Name',
    width: 200,
    render: (_, client) => <ClientDisplay firstName={client.firstName || ''} lastName={client.lastName || ''} />,
  },
  {
    dataIndex: 'dateOfBirth',
    key: 'dateOfBirth',
    title: 'Date of Birth',
    width: 150,
    render: (date) => (typeof date === 'string' || date instanceof Date ? new Date(date).toLocaleDateString() : '-'),
  },
  // {
  //   dataIndex: 'gender',
  //   key: 'gender',
  //   title: 'Gender',
  //   width: 100,
  //   align: 'center',
  //   render: (gender) => <Typography>{typeof gender === 'string' ? gender : '-'}</Typography>,
  // },

  {
    dataIndex: 'phone',
    key: 'phone',
    title: 'Phone',
    width: 150,
    render: (phone) => <Typography>{(phone as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'email',
    key: 'email',
    title: 'Email',
    width: 200,
    render: (email) => <Typography>{(email as string) || '-'}</Typography>,
  },
  {
    dataIndex: 'username',
    key: 'username',
    title: 'Username',
    width: 140,
    render: (username) => <Typography>{(username as string) || '-'}</Typography>,
  },

  {
    dataIndex: ['street'],
    key: 'street',
    title: 'Street',
    width: 200,
    render: (_, record) => (
      <Typography>{capitalizeString(`${record.street} ${record.city} ${record.postalCode}`).trim() || '-'}</Typography>
    ),
  },
  // {
  //   dataIndex: ['geoLocation', 'lat'],
  //   key: 'address.geoLocation.lat',
  //   title: 'Lat',
  //   width: 100,
  //   render: (_, record) => <Typography>{record.address?.geoLocation?.lat ?? '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['address', 'geoLocation', 'lng'],
  //   key: 'address.geoLocation.lng',
  //   title: 'Lng',
  //   width: 100,
  //   render: (_, record) => <Typography>{record.address?.geoLocation?.lng ?? '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'medicalHistory',
  //   key: 'medicalHistory',
  //   title: 'Medical History',
  //   width: 180,
  //   render: (mh) => <Typography>{(mh as string) || '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'medications',
  //   key: 'medications',
  //   title: 'Medications',
  //   width: 180,
  //   render: (meds) =>
  //     Array.isArray(meds) && meds.length ? (meds as string[]).map((m) => <Tag key={m}>{m}</Tag>) : '-',
  // },
  // {
  //   dataIndex: 'allergies',
  //   key: 'allergies',
  //   title: 'Allergies',
  //   width: 180,
  //   render: (allergies) =>
  //     Array.isArray(allergies) && allergies.length ? (allergies as string[]).map((a) => <Tag key={a}>{a}</Tag>) : '-',
  // },
  // {
  //   dataIndex: 'mobilityNotes',
  //   key: 'mobilityNotes',
  //   title: 'Mobility Notes',
  //   width: 180,
  //   render: (notes) => <Typography>{(notes as string) || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['emergencyContact', 'name'],
  //   key: 'emergencyContact.name',
  //   title: 'Emergency Contact Name',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.emergencyContact?.name || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['emergencyContact', 'relationship'],
  //   key: 'emergencyContact.relationship',
  //   title: 'Emergency Contact Relationship',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.emergencyContact?.relationship || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['emergencyContact', 'phone'],
  //   key: 'emergencyContact.phone',
  //   title: 'Emergency Contact Phone',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.emergencyContact?.phone || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['secondaryContact', 'name'],
  //   key: 'secondaryContact.name',
  //   title: 'Secondary Contact Name',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.secondaryContact?.name || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['secondaryContact', 'relationship'],
  //   key: 'secondaryContact.relationship',
  //   title: 'Secondary Contact Relationship',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.secondaryContact?.relationship || '-'}</Typography>,
  // },
  // {
  //   dataIndex: ['secondaryContact', 'phone'],
  //   key: 'secondaryContact.phone',
  //   title: 'Secondary Contact Phone',
  //   width: 160,
  //   render: (_, record) => <Typography>{record.secondaryContact?.phone || '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'favoriteProviders',
  //   key: 'favoriteProviders',
  //   title: 'Favorite Providers',
  //   width: 180,
  //   render: (providers) =>
  //     Array.isArray(providers) && providers.length ? (providers as string[]).map((p) => <Tag key={p}>{p}</Tag>) : '-',
  // },
  // {
  //   dataIndex: 'notes',
  //   key: 'notes',
  //   title: 'Notes',
  //   width: 180,
  //   render: (notes) => <Typography>{(notes as string) || '-'}</Typography>,
  // },
  // {
  //   dataIndex: 'roles',
  //   key: 'roles',
  //   title: 'Roles',
  //   width: 120,
  //   render: (roles) =>
  //     Array.isArray(roles) && roles.length ? (roles as string[]).map((r) => <Tag key={r}>{r}</Tag>) : '-',
  // },
  {
    dataIndex: 'active',
    key: 'active',
    title: 'Active',
    width: 100,
    align: 'center',
    render: (active) => {
      return <>{active ? 'Yes' : 'No'}</>;
    },
  },
  // {
  //   dataIndex: 'createdAt',
  //   key: 'createdAt',
  //   title: 'Created At',
  //   width: 180,
  //   render: (date) => (typeof date === 'string' || date instanceof Date ? new Date(date).toLocaleString() : '-'),
  // },
  // {
  //   dataIndex: 'updatedAt',
  //   key: 'updatedAt',
  //   title: 'Updated At',
  //   width: 180,
  //   render: (date) => (typeof date === 'string' || date instanceof Date ? new Date(date).toLocaleString() : '-'),
  // },
];
