import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { ScrollProps } from '@app/types/ui.types';
import SearchFilter from '@feat-scheduling/components/VisitsList/shared/SearchFilter';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { Empty } from 'antd';
import { memo, useEffect, useRef, useState } from 'react';
import SimpleBar from 'simplebar-react';
import { VisitItem } from './VisitItem';
import { VisitListHeader } from './VisitListHeader';

type VisitProps = {
  visits: VisitPopulated[];
  scrollProps: ScrollProps;
  onSearchTermChange?: (searchTerm: string) => void;
};

/**
 * Main component that displays a list of visits with search functionality
 * Features timeline view, expandable visit details, and smooth scrolling to active visit
 */
const VisitsList = ({ visits, scrollProps, onSearchTermChange }: VisitProps) => {
  const activeVisit = useSchedulingStore((state) => state.activeVisit);
  const setActiveVisit = useSchedulingStore((state) => state.setActiveVisit);

  // Refs for scrolling to active visit
  const visitRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // Local state for filtered visits (if needed for local filtering)
  const [filteredVisits, setFilteredVisits] = useState<VisitPopulated[]>(visits);

  // Scroll to active visit when it changes
  useEffect(() => {
    const ref = visitRefs.current[String(activeVisit?.id)];
    if (ref) {
      ref.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, [activeVisit]);

  // Toggle visit selection
  const toggleVisit = (id: number) => {
    if (activeVisit?.id === id) {
      setActiveVisit(null);
      return;
    }
    setActiveVisit(visits.find((v) => v.id === id) || null);
  };

  // Update filtered visits when visits prop changes
  useEffect(() => {
    setFilteredVisits([...visits]);
  }, [visits]);
  return (
    <div className="flex flex-col h-full min-h-0 overflow-hidden gap-2">
      {/* Search Header */}
      <SearchFilter
        onValueChange={onSearchTermChange}
        title={<VisitListHeader visits={visits} />}
        placeholder={'Search visits'}
      />

      {/* Visits List */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {filteredVisits.length > 0 ? (
          <SimpleBar
            forceVisible={scrollProps.forceVisible}
            autoHide={scrollProps.autoHide}
            className={`max-h-full ${scrollProps.className || ''}`}
          >
            <div className="flex flex-col gap-[2px] h-full pr-1">
              {filteredVisits.map((visit, index) => (
                <VisitItem
                  key={visit.id}
                  ref={(el) => {
                    visitRefs.current[String(visit.id)] = el;
                  }}
                  visit={visit}
                  index={index}
                  totalVisits={visits.length}
                  isActive={activeVisit?.id === visit.id}
                  onToggle={toggleVisit}
                />
              ))}
            </div>
          </SimpleBar>
        ) : (
          <div className="h-full flex items-center justify-center">
            <Empty description="No visits found" />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(VisitsList);
