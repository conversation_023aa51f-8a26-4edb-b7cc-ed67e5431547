import { MenuStructure, RoutesStructure, RoutesStructureWithStringKeys } from '@app/types/routing.types';
import { createContext } from 'react';
import { RouteObject } from 'react-router-dom';

interface RoutingContextProps {
  menuStructure: MenuStructure;
  filteredMenuStructure: MenuStructure;
  getIndexRouteKey: string | null;
  permissions: string[];
  getParentKey: (key: string) => string | null;
  RouteConstruct: RoutesStructure;
  RouteConstructStrKeys: RoutesStructureWithStringKeys;
  toReactRouter: RouteObject[]; // React Router v6 format
  getAllPaths: () => string[];
}

export const RoutingContext = createContext<RoutingContextProps | undefined>(undefined);
RoutingContext.displayName = 'Routing Context';
