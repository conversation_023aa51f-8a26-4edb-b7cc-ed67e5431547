import dayjs from 'dayjs';

type VisitHeaderProps = {
  visits: Array<{ startTime: string }>;
};

/**
 * Component that displays the header with date and visit count
 * Shows the day/month from first visit and total count
 */
export function VisitListHeader({ visits }: VisitHeaderProps) {
  return (
    <div className="flex justify-between">
      {visits.length > 0 && (
        <div>
          <strong>{dayjs(visits[0].startTime).format('D') + ' '}</strong>
          {dayjs(visits[0].startTime).format('MMMM')}
        </div>
      )}
      <div>
        There are <strong>{visits.length}</strong> visits today
      </div>
    </div>
  );
}
