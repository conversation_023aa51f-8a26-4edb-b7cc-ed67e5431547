import { ApiClient } from '@api/api-configuration';
import { CaregiverShift, CaregiverWithServices, ShiftCreate } from '@api/READ_ONLY/caregiver_api/Api';
import DateNavigator from '@app/components/ui/DateNavigator/DateNavigator';
import ShiftsBoard from '@feat-shifts/components/ShiftsBoard';
import dayjs, { Dayjs } from 'dayjs';
import qs from 'qs';
import { useCallback, useEffect, useState } from 'react';

export type ShiftMap = {
  [caregiverId: number]: {
    [date: string]: CaregiverShift[];
  };
};

export type UpdateAction =
  | {
      type: 'create' | 'update';
      caregiverId: number;
      date: string;
      shifts: CaregiverShift[];
    }
  | {
      type: 'delete';
      caregiverId: number;
      date: string;
      shiftId: number;
    };

export const ShiftsContainer = () => {
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());
  const [caregiversList, setCaregiversList] = useState<CaregiverWithServices[]>([]);
  const [shiftMap, setShiftMap] = useState<ShiftMap>({});

  const weekStart = currentDate.startOf('week');
  const daysOfWeek = Array.from({ length: 7 }, (_, i) => weekStart.add(i, 'day'));
  const dateStrings = daysOfWeek.map((d) => d.format('YYYY-MM-DD'));

  // Fetch caregivers on mount
  useEffect(() => {
    const fetchCaregivers = async () => {
      try {
        const response = await ApiClient.caregiverApi.caregivers.getCaregiversCaregiversGet();
        setCaregiversList(response.data.data);
      } catch (error) {
        console.error('Failed to fetch caregivers:', error);
      }
    };
    fetchCaregivers();
  }, []);

  // Fetch shifts when weekStart changes
  const fetchShiftsForWeek = useCallback(async () => {
    console.log('Fetching', dateStrings);

    const newShiftMap: ShiftMap = {};
    try {
      const response = await ApiClient.caregiverApi.caregivers.getShiftsByDatesCaregiversShiftsByDatesGet(
        {
          dates: dateStrings,
        },
        {
          paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
        }
      );

      console.log('RESPONSE', response);

      const shiftData = response.data.data;
      for (const shift of shiftData) {
        const caregiverId = shift.caregiverId!;
        const shiftDate = dayjs(shift.periodFrom).format('YYYY-MM-DD');

        if (!newShiftMap[caregiverId]) {
          newShiftMap[caregiverId] = {};
        }
        if (!newShiftMap[caregiverId][shiftDate]) {
          newShiftMap[caregiverId][shiftDate] = [];
        }

        const start = dayjs(shift.periodFrom).format('HH:mm');
        const end = dayjs(shift.periodTo).format('HH:mm');
        const enrichedShift = {
          ...shift,
          periodFrom: start,
          periodTo: end,
        };

        newShiftMap[caregiverId][shiftDate].push(enrichedShift);
      }
      setShiftMap(newShiftMap);
    } catch (error) {
      console.error('Failed to fetch shifts:', error);
    }
    //TODO if we add here the dep we get infite rerendering
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchShiftsForWeek();
  }, [currentDate, fetchShiftsForWeek]);

  const onCreate = async (selectedCaregiver: number, selectedDate: string, shifts: ShiftCreate[]) => {
    return Promise.all(
      shifts.map(async (shift) => {
        const periodFrom = dayjs(`${selectedDate}T${shift.periodFrom}`).toISOString();
        const periodTo = dayjs(`${selectedDate}T${shift.periodTo}`).toISOString();

        const data = { notes: shift.notes, periodFrom, periodTo };

        const res = await ApiClient.caregiverApi.caregivers.addShiftCaregiversCaregiverIdShiftsPost(
          selectedCaregiver,
          data
        );
        return res;
      })
    );
  };

  const onEdit = async (selectedCaregiver: number, selectedDate: string, shifts: CaregiverShift[]) => {
    return Promise.all(
      shifts.map(async (shift) => {
        const periodFrom = dayjs(`${selectedDate}T${shift.periodFrom}`).toISOString();
        const periodTo = dayjs(`${selectedDate}T${shift.periodTo}`).toISOString();

        const data = { notes: shift.notes || null, periodFrom, periodTo };

        const res = await ApiClient.caregiverApi.caregivers.updateShiftCaregiversCaregiverIdShiftsShiftIdPut(
          selectedCaregiver,
          shift.caregiverShiftId,
          data
        );
        return res;
      })
    );
  };

  const onDelete = async (selectedCaregiver: number, shift_id: number) => {
    const res = await ApiClient.caregiverApi.caregivers.deleteShiftCaregiversCaregiverIdShiftsShiftIdDelete(
      selectedCaregiver,
      shift_id
    );
    return res;
  };

  const updateShiftMap = (action: UpdateAction) => {
    setShiftMap((prev) => {
      const updated = { ...prev };

      switch (action.type) {
        case 'create':
        case 'update': {
          if (!updated[action.caregiverId]) updated[action.caregiverId] = {};
          updated[action.caregiverId][action.date] = action.shifts;
          break;
        }
        case 'delete': {
          if (!updated[action.caregiverId]) return prev;
          updated[action.caregiverId][action.date] = updated[action.caregiverId][action.date].filter(
            (shift) => shift.caregiverShiftId !== action.shiftId
          );
          break;
        }
      }

      return updated;
    });

    if (action.type === 'create') {
      fetchShiftsForWeek();
    }
  };

  return (
    <ShiftsBoard
      shifts={shiftMap}
      caregivers={caregiversList}
      daysOfWeek={daysOfWeek}
      dateNavigator={<DateNavigator mode="week" currentDate={currentDate} setCurrentDate={setCurrentDate} />}
      onCreate={onCreate}
      onEdit={onEdit}
      onDelete={onDelete}
      onLocalUpdate={updateShiftMap}
    />
  );
};
