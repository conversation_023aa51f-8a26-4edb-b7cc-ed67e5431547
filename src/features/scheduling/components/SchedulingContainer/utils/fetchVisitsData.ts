import { ApiClient } from '@api/api-configuration';
import dayjs, { Dayjs } from 'dayjs';

/**
 * Fetches visits data for a specific date
 * @param currentDate - The date to fetch visits for
 * @returns Promise with visits data or undefined if error
 */
export const fetchVisitsData = async (currentDate: Dayjs) => {
  const start_time_from = dayjs(currentDate).startOf('day').toISOString(); // today 00:00
  const start_time_to = dayjs(currentDate).endOf('day').toISOString(); // today 23:59

  try {
    const visits = await ApiClient.visitsApi.visits.listVisitsVisitsGet({
      start_time_from: start_time_from,
      start_time_to: start_time_to,
    });
    return visits;
  } catch (error) {
    console.error('Error fetching scheduling data', error);
    return undefined;
  }
};

/**
 * Searches visits with a query term for a specific date
 * @param searchTerm - The search term to filter visits
 * @param currentDate - The date to search within
 * @returns Promise with search results
 */
export const searchVisitsData = async (searchTerm: string, currentDate: Dayjs) => {
  const params = {
    query: String(searchTerm).trim(),
    from_date: dayjs(currentDate).startOf('day').toISOString(),
    to_date: dayjs(currentDate).endOf('day').toISOString(),
  };

  return await ApiClient.visitsApi.visits.searchVisitsVisitsSearchGet(params);
};
