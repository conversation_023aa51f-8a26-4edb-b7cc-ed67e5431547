{"openapi": "3.1.0", "info": {"title": "notifications", "description": "\nMicroservice for managing home care notifications.\n\n## Features\n* Register tokens for users and send notifications\n* Manage services and attachments\n* Register device tokens and send push notifications\n\n## Authentication\nAll endpoints require authentication using JWT tokens.\n", "version": "1.0.0"}, "paths": {"/health": {"get": {"tags": ["Health"], "summary": "Health check endpoint", "description": "Returns the health status and version of the service", "operationId": "health_check_health_get", "responses": {"200": {"description": "Service health status and version information", "content": {"application/json": {"schema": {}}}}}}}, "/notifications/register": {"post": {"tags": ["NotificationToken"], "summary": "Register a new notification token", "description": "This endpoint allows a user to register a new notification token.", "operationId": "register_notifications_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTokenCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationToken"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/{token_id}": {"get": {"tags": ["NotificationToken"], "summary": "Get a notification token by its ID", "description": "This endpoint retrieves a notification token by its ID.", "operationId": "get_token_notifications__token_id__get", "parameters": [{"name": "token_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Token Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationToken"}}}}, "404": {"description": "Token not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/user/{user_id}": {"get": {"tags": ["NotificationToken"], "summary": "Get all notification tokens for a user", "description": "This endpoint retrieves all notification tokens for a specific user.", "operationId": "get_tokens_notifications_user__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationToken"}, "title": "Response Get Tokens Notifications User  User Id  Get"}}}}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/send/{user_id}": {"post": {"tags": ["Notifications"], "summary": "Send a notification to a user", "description": "Sends a push notification to a specific user and logs it to the DB.", "operationId": "send_notification_notifications_send__user_id__post", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationSendRequest"}}}}, "responses": {"200": {"description": "Notification queued/sent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationSendResponse"}}}}, "404": {"description": "User or tokens not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/user/{user_id}/all": {"get": {"tags": ["Notifications"], "summary": "List notifications for a user", "description": "Retrieve a paginated list of notifications for a specific user.", "operationId": "list_user_notifications_paginated_notifications_user__user_id__all_get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of items to skip", "default": 0, "title": "Offset"}, "description": "Number of items to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Max items to return", "default": 100, "title": "Limit"}, "description": "Max items to return"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationGetAllPaginationResponse"}}}}, "400": {"description": "Invalid pagination parameters"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/user/{user_id}/search-minified": {"get": {"tags": ["Notifications"], "summary": "Search notifications (minified) for a user", "description": "Retrieve a paginated, minified list of notifications for a specific user. Supports free-text search and filters.", "operationId": "search_user_notifications_minified_notifications_user__user_id__search_minified_get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of items to skip", "default": 0, "title": "Offset"}, "description": "Number of items to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Max items to return", "default": 100, "title": "Limit"}, "description": "Max items to return"}, {"name": "q", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Free-text search over title, message, id, action_value", "title": "Q"}, "description": "Free-text search over title, message, id, action_value"}, {"name": "is_read", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by read status", "title": "<PERSON>"}, "description": "Filter by read status"}, {"name": "action_values", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"enum": ["accept", "reject", "null"], "type": "string"}}, {"type": "null"}], "description": "Filter by one or more action values. Repeat the param for multiple.", "title": "Action Values"}, "description": "Filter by one or more action values. Repeat the param for multiple."}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationGetAllMinifiedPaginationResponse"}}}}, "400": {"description": "Invalid parameters"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/item/{notification_id}": {"get": {"tags": ["Notifications"], "summary": "Get a notification by ID", "description": "This endpoint retrieves a specific notification by its ID.", "operationId": "get_notification_notifications_item__notification_id__get", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Notifications"], "summary": "Updates a notification", "description": "Updates a notification", "operationId": "update_user_notification_notifications_item__notification_id__put", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUpdate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Notifications"], "summary": "Deletes a notification", "description": "Deletes a notification", "operationId": "delete_user_notification_notifications_item__notification_id__delete", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "responses": {"204": {"description": "Successful response"}, "404": {"description": "Not found"}, "500": {"description": "Internal server error"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/notifications/{notification_id}/action": {"post": {"tags": ["Notifications"], "summary": "Record action on a notification (accept/reject) and send notification to admins", "description": "Updates the notification's `action_value` and `action_at`. If the value is **reject**, unassigns caregivers from the given visit and sends a broadcast notification to all administrators.", "operationId": "act_on_notification_notifications__notification_id__action_post", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationActionRequest"}}}}, "responses": {"200": {"description": "Action result and side-effects summary", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationActionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Notification": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "title": {"type": "string", "title": "Title"}, "message": {"type": "string", "title": "Message"}, "type": {"type": "string", "enum": ["push", "email", "in_app"], "title": "Type"}, "isRead": {"type": "boolean", "title": "Isread", "default": false}, "sentAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Sentat"}, "readAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Readat"}, "targetUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "actionAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actionat", "description": "Timestamp the caregiver clicked approve/reject"}, "actionValue": {"anyOf": [{"type": "string", "enum": ["accept", "reject"]}, {"type": "null"}], "title": "Actionvalue", "description": "Decision value for actionable notification"}, "notificationId": {"type": "integer", "title": "Notificationid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["userId", "title", "message", "type", "notificationId", "createdAt"], "title": "Notification"}, "NotificationActionMetadata": {"properties": {"address": {"type": "string", "title": "Address"}, "clientFirstName": {"type": "string", "title": "Clientfirstname"}, "clientLastName": {"type": "string", "title": "Clientlastname"}, "caregiverFirstName": {"type": "string", "title": "Caregiverfirstname"}, "caregiverLastName": {"type": "string", "title": "Caregiverlastname"}, "visitStartTime": {"type": "string", "title": "Visitstarttime"}, "visitEndTime": {"type": "string", "title": "Visitendtime"}, "clientId": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "visitId": {"type": "integer", "title": "Visitid"}, "caregiverId": {"type": "integer", "title": "Caregiverid"}}, "type": "object", "required": ["address", "clientFirstName", "clientLastName", "caregiverFirstName", "caregiverLastName", "visitStartTime", "visitEndTime", "clientId", "visitId", "caregiverId"], "title": "NotificationActionMetadata"}, "NotificationActionRequest": {"properties": {"actionValue": {"type": "string", "enum": ["accept", "reject"], "title": "Actionvalue"}, "visitId": {"type": "integer", "title": "Visitid"}, "caregiverId": {"type": "integer", "title": "Caregiverid"}, "data": {"anyOf": [{"$ref": "#/components/schemas/NotificationActionMetadata"}, {"type": "null"}], "description": "Metadata for push notifications"}}, "type": "object", "required": ["actionValue", "visitId", "caregiverId"], "title": "NotificationActionRequest"}, "NotificationActionResponse": {"properties": {"notificationId": {"type": "integer", "title": "Notificationid"}, "action": {"type": "string", "enum": ["accept", "reject"], "title": "Action"}, "visitId": {"type": "integer", "title": "Visitid"}, "actionAt": {"type": "string", "format": "date-time", "title": "Actionat"}, "adminsNotified": {"type": "integer", "title": "Adminsnotified"}}, "type": "object", "required": ["notificationId", "action", "visitId", "actionAt", "adminsNotified"], "title": "NotificationActionResponse"}, "NotificationGetAllMinifiedPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/NotificationMinified"}, "type": "array", "title": "Data", "description": "Paged data"}, "totalUnread": {"type": "integer", "title": "Totalunread"}}, "type": "object", "required": ["total", "offset", "limit", "data", "totalUnread"], "title": "NotificationGetAllMinifiedPaginationResponse"}, "NotificationGetAllPaginationResponse": {"properties": {"total": {"type": "integer", "title": "Total", "description": "Total number of items matching the filters"}, "offset": {"type": "integer", "title": "Offset", "description": "Current offset in the result set"}, "limit": {"type": "integer", "title": "Limit", "description": "Maximum number of items returned"}, "data": {"items": {"$ref": "#/components/schemas/Notification"}, "type": "array", "title": "Data", "description": "Paged data"}, "totalUnread": {"type": "integer", "title": "Totalunread"}}, "type": "object", "required": ["total", "offset", "limit", "data", "totalUnread"], "title": "NotificationGetAllPaginationResponse"}, "NotificationMinified": {"properties": {"notificationId": {"type": "integer", "title": "Notificationid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "title": {"type": "string", "title": "Title"}, "message": {"type": "string", "title": "Message"}, "isRead": {"type": "boolean", "title": "Isread", "default": false}, "actionValue": {"anyOf": [{"type": "string", "enum": ["accept", "reject"]}, {"type": "null"}], "title": "Actionvalue", "description": "Decision value for actionable notification"}}, "type": "object", "required": ["notificationId", "createdAt", "title", "message"], "title": "NotificationMinified"}, "NotificationSendRequest": {"properties": {"title": {"type": "string", "title": "Title", "description": "Notification title"}, "body": {"type": "string", "title": "Body", "description": "Notification body"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data", "description": "Extra metadata for deep-links or client handling"}}, "type": "object", "required": ["title", "body"], "title": "NotificationSendRequest"}, "NotificationSendResponse": {"properties": {"sent": {"type": "integer", "title": "<PERSON><PERSON>", "description": "Number of successfully sent push messages"}, "responses": {"items": {"type": "string"}, "type": "array", "title": "Responses", "description": "FCM message IDs"}}, "type": "object", "required": ["sent"], "title": "NotificationSendResponse"}, "NotificationToken": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "deviceToken": {"type": "string", "minLength": 1, "title": "Devicetoken", "description": "FCM/APNs/Web push token"}, "platform": {"anyOf": [{"type": "string", "enum": ["ios", "android", "web"]}, {"type": "null"}], "title": "Platform", "description": "Platform type: ios, android, or web"}, "deviceInfo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deviceinfo", "description": "Optional device info, e.g., \"iPhone 14, iOS 17.3\""}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}, "lastUsedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Lastusedat"}, "notificationTokenId": {"type": "integer", "title": "Notificationtokenid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updatedat"}}, "type": "object", "required": ["userId", "deviceToken", "notificationTokenId", "createdAt"], "title": "NotificationToken"}, "NotificationTokenCreate": {"properties": {"userId": {"type": "integer", "title": "Userid"}, "deviceToken": {"type": "string", "minLength": 1, "title": "Devicetoken", "description": "FCM/APNs/Web push token"}, "platform": {"anyOf": [{"type": "string", "enum": ["ios", "android", "web"]}, {"type": "null"}], "title": "Platform", "description": "Platform type: ios, android, or web"}, "deviceInfo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deviceinfo", "description": "Optional device info, e.g., \"iPhone 14, iOS 17.3\""}, "isActive": {"type": "boolean", "title": "Isactive", "default": true}, "lastUsedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Lastusedat"}}, "type": "object", "required": ["userId", "deviceToken"], "title": "NotificationTokenCreate"}, "NotificationUpdate": {"properties": {"isRead": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isread"}, "readAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Readat"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "actionAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actionat"}, "actionValue": {"anyOf": [{"type": "string", "enum": ["accept", "reject"]}, {"type": "null"}], "title": "Actionvalue"}}, "type": "object", "title": "NotificationUpdate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"oauth2": {"type": "oauth2", "flows": {"authorizationCode": {"authorizationUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/auth", "tokenUrl": "https://ids.konnecta.io/realms/Homecare/protocol/openid-connect/token", "scopes": {}}}}}}, "servers": [{"url": "/api/v1/notifications-api/"}], "security": [{"oauth2": []}]}