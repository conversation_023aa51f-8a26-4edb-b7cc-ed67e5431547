import { ApiClient } from '@api/api-configuration';
import { Client, ClientCreate, ClientUpdate } from '@api/READ_ONLY/client_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { extractErrorData, isErrorWithDetail } from '@app/utils/isErrorwithErrors';
import useNotifications from '@context/notifications/useNotificationContext';
import { clientFormConfig } from './form.config';

type Props = {
  data?: Client;
  onSubmited?: (data: Client) => void;
};
export function ClientForm({ data, onSubmited = () => {} }: Props) {
  const { openNotification } = useNotifications();
  const onSubmit = async (formData: ClientCreate | ClientUpdate) => {
    let state = 'create';
    try {
      const payload: ClientCreate | ClientUpdate = {
        ...formData,
      };

      let respClient: Client;
      //TODO: Remove this . fast fix

      if (formData.dateOfBirth) {
        payload.dateOfBirth = new Date(formData.dateOfBirth).toISOString().split('T')[0];
      }
      if (formData.email) {
        delete payload.email;
        payload.email = formData.email;
      } else {
        delete payload.email;
      }
      if (!data) {
        const res = await ApiClient.clientApi.clients.createClientClientsPost(payload as ClientCreate);
        respClient = res.data;
      } else {
        const res = await ApiClient.clientApi.clients.updateClientClientsClientIdPut(
          data.clientId,
          payload as ClientUpdate
        );
        respClient = res.data;
        state = 'update';
      }

      openNotification('topRight', {
        title: `Client`,
        description: `Client ${state === 'update' ? 'updated' : 'created'} successfully.`,
        type: 'Success',
      });

      onSubmited(respClient);
    } catch (error: unknown) {
      console.log('error', isErrorWithDetail(error));
      const errorData = extractErrorData(error);
      if (isErrorWithDetail(errorData)) {
        openNotification('topRight', {
          title: `Client`,
          description: `Client ${state === 'update' ? 'update' : 'creation'} failed. ` + errorData.detail,
          type: 'Warning',
        });
      }
    }
  };

  return <FormWrapper defaultValues={data as ClientCreate} fields={clientFormConfig} onSubmit={onSubmit} />;
}
