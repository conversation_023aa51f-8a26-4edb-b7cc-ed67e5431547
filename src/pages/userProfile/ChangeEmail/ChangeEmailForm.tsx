import { Input, Typography } from 'antd';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

const { Text } = Typography;

export type ChangeEmailFormValues = {
  new_email: string;
  confirm_email: string;
};

export const ChangeEmailForm: React.FC = () => {
  const { control } = useFormContext<ChangeEmailFormValues>();

  return (
    <div className="flex flex-col justify-center items-center gap-2 ">
      <div className="flex flex-col gap-8 w-1/2">
        {(['new_email', 'confirm_email'] as const).map((field) => (
          <Controller
            key={field}
            name={field}
            control={control}
            rules={{ required: `${field === 'new_email' ? 'New Email' : 'Confirm New Email'} is required` }}
            render={({ field: controllerField, fieldState }) => (
              <Input
                {...controllerField}
                placeholder={field === 'new_email' ? 'New Email' : 'Confirm New Email'}
                status={fieldState.error ? 'error' : undefined} // This adds the red border
              />
            )}
          />
        ))}
      </div>

      <div className="flex flex-col mt-4 w-1/2">
        <Text style={{ color: 'rgba(58, 53, 65, 0.6)' }}>Email Requirements</Text>
        <Text style={{ color: 'rgba(58, 53, 65, 0.6)' }}>
          <ul style={{ gap: 4 }}>
            <li>Must be a valid email address</li>
            <li>Both fields must match</li>
          </ul>
        </Text>
      </div>
    </div>
  );
};
