import FullLoader from '@app/components/ui/FullLoader';
import HeaderLayout from '@app/layouts/HeaderLayout';
import { Shifts } from '@pages/Shifts';
import { Suspense } from 'react';

// Lazy Load DashboardPage Component

// Route Component
const ShiftsPage = () => (
  <Suspense fallback={<FullLoader />}>
    <HeaderLayout title="Shifts">
      <Shifts />
    </HeaderLayout>
  </Suspense>
);

// Export Lazy-Loaded Route
export default {
  ShiftsPage,
};
