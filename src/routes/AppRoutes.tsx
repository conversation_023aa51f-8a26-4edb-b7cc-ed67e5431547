import { Error404 } from '@app/features/errors/components/Error404';
import { ErrorsLayout } from '@app/features/errors/ErrorsLayout';
import PrivateRoutes from '@app/routes/PrivateRoutes';
import { useAuthContext } from '@context/auth/useAuthContext';
import { RoutingProvider } from '@context/routing/RoutingProvider';
import Callback from '@pages/auth/Callback';
import { FC } from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';
import { App } from '../App';
import AuthRoutes from './AuthRoutes';

const AppRoutes: FC = () => {
  const { auth, permissions } = useAuthContext();
  if (permissions.length === 0) {
    console.error('No permissions found. Ensure you are authenticated and have the necessary permissions.');
  }
  const location = useLocation();
  return (
    <Routes>
      <Route element={<App />}>
        <Route path="/" element={<Navigate to="/dashboard" />} />
        {auth && permissions.length > 0 && (
          <>
            <Route path="/" element={<Navigate to="/dashboard" />} />
            <Route
              path="/*"
              element={
                <RoutingProvider>
                  <PrivateRoutes />
                </RoutingProvider>
              }
            />
          </>
        )}
        <Route path="auth/callback/*" element={<Callback />} />
        <Route path="auth/*" element={<AuthRoutes />} />
        <Route path="/*" element={<Navigate to="/auth" state={{ from: location }} />} />
        <Route path="/" element={<ErrorsLayout />}>
          <Route path="404" element={<Error404 />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default AppRoutes;
