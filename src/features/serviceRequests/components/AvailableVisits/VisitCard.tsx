import dayjs from 'dayjs';

interface VisitCardProps {
  date: string;
  isAvailable: boolean;
  numberOfAvailableCaregivers: number;
}

const VisitCard = ({ date, isAvailable, numberOfAvailableCaregivers }: VisitCardProps) => {
  const formattedDate = dayjs(date).isValid() ? dayjs(date).format('DD/MM/YYYY') : date;

  return (
    <div
      className={[
        'relative flex flex-col items-center justify-center text-center gap-2',
        'w-full px-3 py-3 rounded-xl',
        'bg-white border shadow-sm hover:shadow-md transition-all duration-200',
        isAvailable ? 'border-emerald-200 hover:border-emerald-300' : 'border-rose-200 hover:border-rose-300',
        'hover:bg-gray-50 active:scale-[0.99]',
      ].join(' ')}
      aria-label={isAvailable ? `having caregivers available` : 'No caregivers available'}
    >
      <span
        aria-hidden
        className={[
          'absolute left-0 top-1/2 -translate-y-1/2 h-8 w-1 rounded-r',
          isAvailable ? 'bg-emerald-500' : 'bg-rose-500',
        ].join(' ')}
      />

      <span className="truncate whitespace-nowrap tabular-nums text-sm font-semibold text-gray-900">
        {formattedDate}
      </span>

      {isAvailable && (
        <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[11px] font-semibold ring-1 bg-emerald-50 text-emerald-700 ring-emerald-200">
          <span aria-hidden className="h-1.5 w-1.5 rounded-full bg-emerald-500" />
          {numberOfAvailableCaregivers} available
        </span>
      )}
    </div>
  );
};

export default VisitCard;
