import CaregiversList from '@feat-scheduling/components/CaregiversList/CaregiversList';
import CustomDragLayer from '@feat-scheduling/components/CaregiversList/CustomDragLayer';
import SchedulingCalendar from '@feat-scheduling/components/SchedulingCalendar/SchedulingCalendar';
import VisitsList from '@feat-scheduling/components/VisitsList/VisitsList';
import { memo, useRef } from 'react';
import { useCaregiverManagement } from './hooks/useCaregiverManagement';
import { useVisitData } from './hooks/useVisitData';
import { useVisitSearch } from './hooks/useVisitSearch';

/**
 * Main scheduling container component
 * Orchestrates the three main sections: visits list, calendar, and caregivers list
 *
 * Features:
 * - Displays visits for the selected date
 * - <PERSON><PERSON> visit search with debouncing
 * - Shows available caregivers for selected visit
 * - Provides client-side caregiver filtering
 */
const SchedulingContainer = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Manage visit data and date selection
  const { visits, currentDate, setCurrentDate } = useVisitData();

  // Handle visit search functionality
  const { onSearchTermChange } = useVisitSearch(currentDate);

  // Manage caregiver data and filtering
  const { availableCaregivers, filteredCaregivers, onCaregiverChange, activeVisit } = useCaregiverManagement();
  return (
    <div ref={containerRef} className="flex h-full gap-2 overflow-hidden">
      <CustomDragLayer />

      {/* Visits List Section */}
      <div className="flex-none w-full max-w-[400px] h-full overflow-hidden">
        <VisitsList
          visits={visits}
          onSearchTermChange={onSearchTermChange}
          scrollProps={{
            forceVisible: true,
            autoHide: true,
            className: 'pr-2',
          }}
        />
      </div>

      {/* Calendar Section */}
      <div className="flex-1 min-w-80 pr-2">
        <SchedulingCalendar currentDate={currentDate} setCurrentDate={setCurrentDate} visits={visits} />
      </div>

      {/* Caregivers List Section */}
      <div className="flex-none w-full max-w-[300px] h-full overflow-hidden">
        <CaregiversList
          onSearchTermChange={onCaregiverChange}
          caregivers={availableCaregivers}
          filteredCaregivers={filteredCaregivers}
          noDataText={
            activeVisit
              ? 'No caregivers found for the selected visit'
              : 'No caregivers available. Select a visit first.'
          }
          scrollProps={{
            forceVisible: true,
            autoHide: true,
            className: 'pr-2',
          }}
        />
      </div>
    </div>
  );
};

export default memo(SchedulingContainer);
