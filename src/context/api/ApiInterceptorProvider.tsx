import { ApiClient } from '@api/api-configuration';
import { ApiInterceptorContext } from '@context/api/ApiInterceptorContext';
import { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import React, { ReactNode, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface ApiInterceptorProviderProps {
  children: ReactNode;
}

export const ApiInterceptorProvider: React.FC<ApiInterceptorProviderProps> = ({ children }) => {
  const navigate = useNavigate();

  useEffect(() => {
    const attachInterceptors = (instance: { instance: AxiosInstance }) => {
      instance.instance.interceptors.response.use(
        (response: AxiosResponse) => response,
        (error: AxiosError) => {
          const status = error?.response?.status;

          if (status === 401) {
            localStorage.removeItem('jwt_token');
            navigate('/login');
          } else {
            console.error('API Error', error);
            // You can plug in a toast or logger here
          }

          return Promise.reject(error);
        }
      );
    };

    attachInterceptors(ApiClient.clientApi);
    attachInterceptors(ApiClient.caregiverApi);
  }, [navigate]);

  return <ApiInterceptorContext.Provider value={{}}>{children}</ApiInterceptorContext.Provider>;
};
