import { ApiClient } from '@api/api-configuration';
import { Client } from '@api/READ_ONLY/client_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { ClientForm } from '@feat-clients/components/ClientForm/ClientForm';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

export const EditClient = () => {
  const { state } = useLocation();
  const { id } = useParams(); // e.g., from /clients/:id/edit
  const [data, setData] = useState<Client>();
  const { openNotification } = useNotifications();

  useEffect(() => {
    const fetchCaregiver = async () => {
      try {
        if (id) {
          const response = await ApiClient.clientApi.clients.getClientClientsClientIdGet(Number(id));
          setData(response.data);
        }
      } catch (err) {
        openNotification('topRight', {
          title: `Caregiver`,
          description: 'Failed to fetch caregiver',
          type: 'Warning',
        });
      }
    };
    console.log('state', state);

    fetchCaregiver();
  }, [id, openNotification, state]);
  const clientData = data;

  if (!clientData) return <div>Loading...</div>;
  if (!clientData) return <div>Error loading client</div>;

  return <ClientForm data={clientData} />;
};
