import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { forwardRef } from 'react';
import { VisitCard } from './VisitCard';
import { VisitTimeColumn } from './VisitTimeColumn';
import { VisitTimeline } from './VisitTimeline';

type VisitItemProps = {
  visit: VisitPopulated;
  index: number;
  totalVisits: number;
  isActive: boolean;
  onToggle: (id: number) => void;
};

/**
 * Component that renders a single visit item with timeline, time, and card
 * This is the main building block for each visit in the list
 */
export const VisitItem = forwardRef<HTMLDivElement, VisitItemProps>(
  ({ visit, index, totalVisits, isActive, onToggle }, ref) => {
    const { startTime, endTime } = visit;

    return (
      <div className="relative flex w-full min-h-full items-stretch" ref={ref}>
        {/* Time Column */}
        <VisitTimeColumn startTime={startTime} endTime={endTime} />

        {/* Timeline Visual */}
        <VisitTimeline index={index} totalVisits={totalVisits} />

        {/* Visit Card */}
        <VisitCard visit={visit} isActive={isActive} onToggle={onToggle} />
      </div>
    );
  }
);

VisitItem.displayName = 'VisitItem';
