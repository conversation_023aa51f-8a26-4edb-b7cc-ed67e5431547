import { Client } from '@api/READ_ONLY/client_api/Api';
import { CaregiverSearch } from '@app/components/ui/CaregiverSearch';
import { ServicesSearch } from '@app/components/ui/ServicesSearch';
import { PatientSearch } from '@feat-service-requests/components/SearchDropdowns/PatientSearch/PatientSearch';
import { Input, InputNumber, Space } from 'antd';
import { Dispatch, SetStateAction } from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

type Props = {
  duration: number;
  setDuration: Dispatch<SetStateAction<number>>;
};

const FiltersPanel = ({ duration, setDuration }: Props) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  // Watch the patient and caregiver values
  const selectedPatient: Client = useWatch({ control, name: 'patient' });
  const selectedCaregiver = useWatch({ control, name: 'caregiver' });
  const selectedServices = useWatch({ control, name: 'service' });
  const errorStyle = { border: '1px solid red', borderRadius: 10 };

  // Determine address to display: prioritize patient, fallback to caregiver
  const addressValue = selectedPatient ? selectedPatient?.street + ',' + selectedPatient?.city : 'Address';
  console.log('selected');
  return (
    <>
      <div className="w-full mb-4 flex flex-wrap gap-2">
        {/* Patient */}
        <div className="flex-1 min-w-[220px]">
          <Controller
            name="patient"
            rules={{ required: 'Patient is required' }}
            control={control}
            render={({ field }) => (
              <PatientSearch
                disabled={false}
                value={field.value}
                setValue={field.onChange}
                style={errors.patient ? errorStyle : {}}
              />
            )}
          />
        </div>

        {/* Address */}
        <div className="flex-1 min-w-[220px]">
          <Input
            size="large"
            value={addressValue}
            placeholder="Address"
            disabled
            readOnly
            style={errors.address ? errorStyle : {}}
          />
        </div>

        {/* Caregiver */}
        <div className="flex-1 min-w-[220px]">
          <Controller
            name="caregiver"
            rules={{ required: 'Caregiver is required' }}
            control={control}
            render={({ field }) => (
              <CaregiverSearch
                selectedServices={selectedServices}
                disabled={false}
                value={field.value}
                setValue={field.onChange}
                style={errors.caregiver ? errorStyle : {}}
              />
            )}
          />
        </div>

        {/* Duration */}
        <div className="flex-1 min-w-[150px]">
          <Space.Compact block className="w-full">
            <label className="text-base rounded-md rounded-r-none bg-app-secondary-extra-light p-2 flex-1">
              Duration
            </label>
            <InputNumber
              size="large"
              value={duration}
              step={15}
              min={30}
              onChange={(value) => setDuration(Number(value))}
              style={errors.duration ? errorStyle : {}}
              className="border-l border-solid border-white flex-1"
              placeholder="Mins"
            />
          </Space.Compact>
        </div>
      </div>

      {/* Service */}
      <div className="flex-1 min-w-[220px]">
        <Controller
          rules={{ required: 'Service is required' }}
          name="service"
          control={control}
          render={({ field }) => (
            <ServicesSearch
              selectedCaregiver={selectedCaregiver}
              value={field.value ?? []}
              setValue={field.onChange}
              style={errors.service ? errorStyle : {}}
            />
          )}
        />
      </div>
    </>
  );
};

export default FiltersPanel;
