import { CaregiverWithServices } from '@api/READ_ONLY/caregiver_api/Api';

/**
 * Filters caregivers based on search term
 * Searches in firstName, lastName, and full name
 * @param caregivers - Array of caregivers to filter
 * @param searchTerm - The search term to filter by
 * @returns Filtered array of caregivers
 */
export const filterCaregivers = (caregivers: CaregiverWithServices[], searchTerm: string): CaregiverWithServices[] => {
  if (!searchTerm.trim()) {
    return caregivers;
  }

  const searchLower = searchTerm.toLowerCase();

  return caregivers.filter((caregiver) => {
    const fullName = `${caregiver.firstName || ''} ${caregiver.lastName || ''}`.toLowerCase();

    return (
      (caregiver.firstName && caregiver.firstName.toLowerCase().includes(searchLower)) ||
      (caregiver.lastName && caregiver.lastName.toLowerCase().includes(searchLower)) ||
      fullName.includes(searchLower)
    );
  });
};
