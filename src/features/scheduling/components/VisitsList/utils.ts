/**
 * Utility function to calculate vertical line styling for visit timeline
 * @param index - Current visit index in the list
 * @param length - Total number of visits
 * @returns CSS classes for the vertical line
 */
export function getVerticalLineStyling(index: number, length: number): string {
  const isFirst = index === 0;
  const isLast = index === length - 1;

  if (isFirst) {
    return 'h-1/2 bottom-0';
  } else if (isLast) {
    return 'h-1/2 top-0';
  } else {
    return 'h-full top-0';
  }
}
