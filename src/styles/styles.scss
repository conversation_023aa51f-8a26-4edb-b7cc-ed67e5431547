@use 'tailwindcss';
@use 'simplebar-react/dist/simplebar.min.css';
@import url('https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700&display=swap');

@theme {
  --color-app-white: var(--color-neutral-50);

  --color-app-gray-light: var(--color-neutral-100);
  --color-app-gray: var(--color-neutral-200);
  --color-app-gray-dark: var(--color-neutral-300);

  --color-app-primary: var(--color-sky-600);

  --color-app-primary-100: color-mix(in srgb, var(--color-app-primary) 10%, white 90%);
  --color-app-primary-100: color-mix(in srgb, var(--color-app-primary) 20%, white 80%);
  --color-app-primary-200: color-mix(in srgb, var(--color-app-primary) 40%, white 60%);
  --color-app-primary-300: color-mix(in srgb, var(--color-app-primary) 60%, white 40%);
  --color-app-primary-400: color-mix(in srgb, var(--color-app-primary) 80%, white 20%);
  --color-app-primary-500: var(--color-app-primary); /* Base shade */
  --color-app-primary-600: color-mix(in srgb, var(--color-app-primary) 80%, black 20%);
  --color-app-primary-700: color-mix(in srgb, var(--color-app-primary) 60%, black 40%);
  --color-app-primary-800: color-mix(in srgb, var(--color-app-primary) 40%, black 60%);
  --color-app-primary-900: color-mix(in srgb, var(--color-app-primary) 20%, black 80%);
  --color-app-primary-950: color-mix(in srgb, var(--color-app-primary) 10%, black 90%);

  --color-app-secondary: var(--color-indigo-600);
  --color-app-secondary-light: var(--color-indigo-300);
  --color-app-secondary-extra-light: var(--color-indigo-100);
  --color-app-secondary-extra-lightest: var(--color-indigo-50);

  --color-app-text-dark: var(--color-neutral-700);
  --color-app-text: var(--color-neutral-600);
  --color-app-text-light: var(--color-neutral-500);

  --color-app-danger: var(--color-rose-500);
  --color-app-warning: var(--color-amber-500);
  --color-app-info: var(--color-cyan-500);
  --color-app-success: var(--color-emerald-500);
}
html,
body,
#root {
  height: 100%;
  overflow: hidden;
}

#table .ant-table-body {
  scrollbar-color: rgb(161, 159, 163) rgb(251, 251, 251) !important;
  scrollbar-width: thin !important;
}
.simplebar-scrollbar::before {
  background-color: rgb(161, 159, 163) !important;
  width: 6px;
}
