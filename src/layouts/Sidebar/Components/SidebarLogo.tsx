import { toAbsoluteUrl } from '@app/utils/toAbsoluteUrl';
import { MutableRefObject } from 'react';
import { Link } from 'react-router-dom';

type PropsType = {
  sidebarRef?: MutableRefObject<HTMLDivElement | null>;
  collapsed: boolean;
};

const SidebarLogo = (props: PropsType) => {
  return (
    <div className="app-sidebar-logo flex flex-col items-center w-full justify-center">
      <Link to="/dashboard" className="px-8">
        {!props.collapsed ? (
          <img
            data-testid="logoLg"
            alt="Logo"
            src={toAbsoluteUrl('media/logos/logo_light.png')}
            className="w-full h-auto"
          />
        ) : (
          <>
            <img
              data-testid="logoSm"
              alt="Logo"
              src={toAbsoluteUrl('media/logos/logo_light-sm.png')}
              className="max-w-12 app-sidebar-logo-minimize"
            />
          </>
        )}
      </Link>
    </div>
  );
};

export { SidebarLogo };
