import { Input } from 'antd';
import { JSX } from 'react';
import { FiSearch } from 'react-icons/fi';
import { CustomFilterIcon } from './CustomFilterIcon';

type SearchFilterProps = {
  title: JSX.Element;
  placeholder: string;
  value?: string;
  onValueChange?: (text: string) => void;
  onOpenFilters?: () => void;
};

const SearchFilter = ({ title, placeholder, value, onValueChange, onOpenFilters }: SearchFilterProps) => {
  return (
    <div className="max-w-md flex flex-col ">
      <div className="text-center p-2 text-sm rounded-t-md bg-app-secondary-extra-light">{title}</div>
      <Input
        size="large"
        placeholder={placeholder}
        value={value}
        onChange={(e) => (onValueChange ? onValueChange(e.target.value) : undefined)}
        prefix={<FiSearch className="text-color-app-text mr-2 " />}
        suffix={onOpenFilters && <CustomFilterIcon onClick={onOpenFilters} />}
        style={{ borderRadius: '0 0 6px 6px' }}
      />
    </div>
  );
};

export default SearchFilter;
