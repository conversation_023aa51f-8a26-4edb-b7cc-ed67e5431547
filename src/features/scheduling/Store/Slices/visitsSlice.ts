import { Caregiver } from '@api/READ_ONLY/caregiver_api/Api';
import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import { StateCreator } from 'zustand';

export type VisitsSlice = {
  visits: VisitPopulated[];
  activeVisit: VisitPopulated | null;
  setVisits: (visits: VisitPopulated[]) => void;
  setActiveVisit: (visit: VisitPopulated | null) => void;
  assignCaregiver: (visitId: number, caregiver: Caregiver) => void;
  updateVisit: (updatedVisit: Partial<VisitPopulated> & { id: number }) => void;
};

export const createVisitsSlice: StateCreator<VisitsSlice> = (set) => ({
  visits: [],
  activeVisit: null,

  setVisits: (visits) => set({ visits }),

  setActiveVisit: (visit) => {
    set({ activeVisit: visit || null });
  },

  assignCaregiver: (visitId, caregiver) =>
    set((state) => ({
      visits: state.visits.map((visit) =>
        Number(visit.id) === visitId ? { ...visit, caregiver: caregiver.caregiverId } : visit
      ),
    })),

  updateVisit: (updatedVisit) =>
    set((state) => ({
      visits: state.visits.map((visit) =>
        Number(visit.id) === updatedVisit.id ? { ...visit, ...updatedVisit } : visit
      ),
      activeVisit:
        state.activeVisit && Number(state.activeVisit.id) === updatedVisit.id
          ? { ...state.activeVisit, ...updatedVisit }
          : state.activeVisit,
    })),
});
