import { APP_PREFIX, CLIENT_PAGE, EDIT_CLIENT } from '@app/routes/urls';
import { ClientForm } from '@feat-clients/components/ClientForm/ClientForm';
import { useNavigate } from 'react-router-dom';

export const CreateClient = () => {
  const navigate = useNavigate();

  return (
    <ClientForm
      onSubmited={(client) => {
        const path = `/${APP_PREFIX}/${CLIENT_PAGE}/${EDIT_CLIENT.replace(':id', `${client.clientId}`)}`;
        navigate(path);
      }}
    />
  );
};
