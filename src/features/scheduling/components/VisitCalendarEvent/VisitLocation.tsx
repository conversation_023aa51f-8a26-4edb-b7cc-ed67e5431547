import { capitalizeString } from '@app/utils/fullNameCapilatizeFirst';

type VisitLocationProps = {
  street: string;
  city: string;
  postalCode: string;
};

/**
 * Component that displays visit location information
 */
export function VisitLocation({ street, city, postalCode }: VisitLocationProps) {
  const hasLocation = street || city || postalCode;

  if (!hasLocation) {
    return <div className="text-sm">Χωρίς διεύθυνση</div>;
  }

  return (
    <div className="text-sm flex gap-1">
      {street && <span>{capitalizeString(street)},</span>}
      {city && <span>{capitalizeString(city)},</span>}
      {postalCode && <span>{postalCode}</span>}
    </div>
  );
}
