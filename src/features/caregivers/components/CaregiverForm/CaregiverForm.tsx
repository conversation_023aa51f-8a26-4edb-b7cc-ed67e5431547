import { Caregiver, CaregiverCreate } from '@api/READ_ONLY/caregiver_api/Api';
import { FormWrapper } from '@app/features/form/Components/FormWrapper/FormWrapper';
import { caregiverFormConfig } from './form.config';

type Props = {
  data?: Caregiver;
  onSubmit?: (formData: Caregiver | CaregiverCreate) => Promise<void>;
};

export function CaregiverForm({ data, onSubmit }: Props) {
  return <FormWrapper<Caregiver> defaultValues={data} fields={caregiverFormConfig} onSubmit={onSubmit} />;
}
