import { AddressValue, ParsedAddress } from './types';

/**
 * Helper to get a component value by type from Google Maps address_components
 */
export function getAddressComponent(components: unknown[], type: string): string {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const component = components?.find((comp: any) => comp.types?.includes(type));
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
  return (component as any)?.long_name || '';
}

/**
 * Parse Google Maps place result into our address format
 */
export function parseGooglePlace(place: unknown): ParsedAddress {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const placeData = place as any;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  const components = placeData.address_components || [];

  // Address line: street_number + route, fallback to formatted_address
  const streetNumber = getAddressComponent(components, 'street_number');
  const route = getAddressComponent(components, 'route');
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  const address = [streetNumber, route].filter(Boolean).join(' ').trim() || placeData.formatted_address || '';

  // City priority: locality → postal_town → sublocality_level_1 → sublocality → administrative_area_level_2 → administrative_area_level_1
  const city =
    getAddressComponent(components, 'locality') ||
    getAddressComponent(components, 'postal_town') ||
    getAddressComponent(components, 'sublocality_level_1') ||
    getAddressComponent(components, 'sublocality') ||
    getAddressComponent(components, 'administrative_area_level_2') ||
    getAddressComponent(components, 'administrative_area_level_1') ||
    '';

  // Country
  const country = getAddressComponent(components, 'country');

  // ZIP
  const zip = getAddressComponent(components, 'postal_code');

  return {
    address,
    city,
    country,
    zip,
  };
}

/**
 * Normalize coordinates to LatLngLiteral format
 */
export function toLatLngLiteral(input: unknown): { lat: number; lng: number } {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const coords = input as any;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
  if (coords && typeof coords.lat === 'function') {
    // google.maps.LatLng object
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
    return { lat: coords.lat(), lng: coords.lng() };
  }
  // Already a literal or similar
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  return { lat: coords?.lat || 0, lng: coords?.lng || 0 };
}

/**
 * Create complete AddressValue from place and coordinates
 */
export function createAddressValue(place: unknown, coordinates: { lat: number; lng: number }): AddressValue {
  const parsed = parseGooglePlace(place);
  return {
    ...parsed,
    ...coordinates,
  };
}

/**
 * Default center (Athens, Greece)
 */
export const DEFAULT_CENTER = { lat: 37.9838, lng: 23.7275 };
