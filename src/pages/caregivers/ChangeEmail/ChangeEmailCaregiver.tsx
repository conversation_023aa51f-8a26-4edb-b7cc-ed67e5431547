import { ApiClient } from '@api/api-configuration';
import { ClientUpdate } from '@api/READ_ONLY/client_api/Api';
import useNotifications from '@context/notifications/useNotificationContext';
import { ChangeEmailForm, ChangeEmailFormValues } from '@pages/userProfile/ChangeEmail/ChangeEmailForm';
import { Button } from 'antd';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';

export const ChangeEmailCaregiver: React.FC = () => {
  const { id } = useParams();
  const { openNotification } = useNotifications();

  const methods = useForm<ChangeEmailFormValues>({
    defaultValues: {
      new_email: '',
      confirm_email: '',
    },
  });

  const onSubmit: SubmitHandler<ChangeEmailFormValues> = async (data) => {
    try {
      if (data.new_email === data.confirm_email) {
        if (id) {
          await ApiClient.caregiverApi.caregivers.updateCaregiverCaregiversCaregiverIdPut(Number(id), {
            email: data.new_email,
          } as ClientUpdate);
          openNotification('topRight', {
            title: 'Email change',
            description: 'Email updated successfully.',
            type: 'Success',
          });
        }
      } else {
        openNotification('topRight', {
          title: 'Email change',
          description: 'Emails do not match',
          type: 'Warning',
        });
      }
    } catch (err) {
      console.error('Updating email error', err);
      openNotification('topRight', {
        title: 'Email change',
        description: 'Email change failed.',
        type: 'Warning',
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <ChangeEmailForm />
      <div className="fixed bottom-6 right-6 z-50">
        <Button onClick={() => onSubmit(methods.getValues())} type="primary" htmlType="submit" size="large">
          Save
        </Button>
      </div>
    </FormProvider>
  );
};
