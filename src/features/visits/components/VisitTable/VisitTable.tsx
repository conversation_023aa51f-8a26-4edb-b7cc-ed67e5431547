import { VisitPopulated } from '@api/READ_ONLY/visits_api/Api';
import Table from '@app/features/table/Table';
import { APP_PREFIX, SCHEDULING_PAGE } from '@app/routes/urls';
import { useSchedulingStore } from '@feat-scheduling/Store/schedulingStore';
import { useNavigate } from 'react-router-dom';

const VisitTable = () => {
  const navigate = useNavigate();
  const visits = useSchedulingStore((state) => state.visits);
  const loading = useSchedulingStore((state) => state.loadingVisits);

  return (
    <Table
      rowKey="id"
      data={visits}
      totalData={visits.length}
      loadingDataFetch={loading}
      addNavigate={() => navigate(`/${APP_PREFIX}/${SCHEDULING_PAGE}`)}
      exportComponent={<></>}
      localStorageKey="VisitTable"
      onRowClick={(record: VisitPopulated) => {
        // Navigate to visit details or edit page
        console.log('Visit clicked:', record);
      }}
    />
  );
};

export default VisitTable;
