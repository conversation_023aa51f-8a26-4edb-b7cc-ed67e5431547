/* eslint-disable react-hooks/exhaustive-deps */
import { ExtendedTableColumnType } from '@app/types/table.types';
import { applyDefaultWidthToColumns } from '@app/utils/applyWidthColumns';
import { isNullOrUndefined } from '@app/utils/isNullOrUndefined';
import { useTable } from '@context/table/TableProvider';
import { Checkbox, Drawer, Input, Switch, Tooltip, Typography } from 'antd';
import React, { Key, memo, useEffect, useState } from 'react';
import { getStoredColumns } from './helpers';

type Props = {
  openCustomisation: boolean;
  setOpenCustomisation: (d: boolean) => void;
  localStorageKey: string;
};

const TableColumnsList = <T,>({ setOpenCustomisation, openCustomisation, localStorageKey }: Props) => {
  const { columns, handleColumnsCustomisation } = useTable();

  const colsRef = React.useRef<ExtendedTableColumnType<unknown>[]>(columns);
  const [checkedList, setCheckedList] = useState<string[]>([]);

  const [options, setOptions] = useState<{ label: unknown; value: Key | undefined; defaultVal: boolean | undefined }[]>(
    columns.map((col) => ({
      label: col.title,
      value: col.key,
      defaultVal: col.default,
    }))
  );

  useEffect(() => {
    const prevColumns = getStoredColumns<T>(localStorageKey);

    const cols = columns.map((col) => {
      const prevColumn = prevColumns.find((item) => item.key === col.key) || {};
      const hidden = !isNullOrUndefined(prevColumn.hidden) ? prevColumn.hidden : col.hidden;
      return { ...col, hidden };
    });

    colsRef.current = applyDefaultWidthToColumns(cols);
    setCheckedList(cols.filter((t) => !t.hidden).map((t) => t.key as string));

    //TODO: we need to listen for columns changes but what happens if the columns ref changes and this cause infinite loop
  }, [setCheckedList]);
  useEffect(() => {
    const updatedColumns = columns.map((item) => ({
      ...item,
      hidden: !checkedList.includes(item.key as string),
    }));
    handleColumnsCustomisation(updatedColumns);
    localStorage.setItem(localStorageKey, JSON.stringify(updatedColumns));
  }, [checkedList]);
  // useEffect(() => {
  //   if (checkedList.length == 0) return;
  //   const updatedColumns = colsRef.current.map((item) => ({
  //     ...item,
  //     hidden: !checkedList.includes(item.key as string),
  //   }));

  //   colsRef.current = updatedColumns;

  //   setColumns(updatedColumns as any);
  //   localStorage.setItem(localStorageKey, JSON.stringify(updatedColumns));
  // }, [checkedList]);

  const handleSearchOptions = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchText = event.target.value.toLowerCase();
    if (searchText) {
      setOptions(
        options.filter((opt) => {
          if (opt.label && opt.value) {
            const valueString = typeof opt.value === 'string' ? opt.value : String(opt.value);
            const labelString = typeof opt.label === 'string' ? opt.label : String(opt.label);
            const lowercasedSearchText = searchText.toLowerCase();

            return (
              valueString.toLowerCase().includes(lowercasedSearchText) ||
              labelString.toLowerCase().includes(lowercasedSearchText)
            );
          }
        })
      );
    } else {
      setOptions(
        columns.map((col) => ({
          label: col.title,
          value: col.key,
          defaultVal: col.default,
        }))
      );
    }
  };
  return (
    <Drawer width={300} title="Columns" onClose={() => setOpenCustomisation(false)} open={openCustomisation}>
      <div className="flex gap-4 flex-col">
        <div>
          <Input placeholder="Search..." onChange={(text) => handleSearchOptions(text)} allowClear />
        </div>
        <Checkbox.Group
          value={checkedList}
          onChange={(value) => {
            setCheckedList(value as string[]);
          }}
          key={'12'}
          className="flex justify-center  flex-col gap-4"
        >
          <div className="flex flex-row justify-between">
            <Typography style={{ color: '#6e6e6e', fontSize: 12 }}>Shown</Typography>
            {checkedList.length > 0 && (
              <Typography
                className="cursor-pointer"
                style={{ fontSize: 12 }}
                onClick={() => setCheckedList(columns.filter((opt) => opt.default).map((t) => t.key as string))}
              >
                Hide all
              </Typography>
            )}
          </div>
          {options.map((opt) => {
            const valueString = typeof opt.value === 'string' ? opt.value : String(opt.value);
            const labelString = typeof opt.label === 'string' ? opt.label : String(opt.label);
            if (checkedList.includes(valueString))
              return (
                <Tooltip key={`opt-${opt.value}`} title={opt.defaultVal && 'Default column'}>
                  <div className="flex flex-row justify-between" key={opt.value}>
                    <div>
                      <Typography>{labelString}</Typography>
                    </div>
                    <Switch
                      style={{ background: '#7f77f1' }}
                      size={'small'}
                      disabled={opt.defaultVal}
                      checked={checkedList.includes(valueString)}
                      onChange={(value) => {
                        if (!opt.defaultVal) {
                          if (value) {
                            setCheckedList([...checkedList, valueString]);
                          } else {
                            setCheckedList(checkedList.filter((item) => item !== opt.value));
                          }
                        }
                      }}
                    />
                  </div>
                </Tooltip>
              );
          })}

          <div className="flex justify-between">
            <Typography style={{ color: '#6e6e6e', fontSize: 12 }}>Hidden</Typography>
            {checkedList.length !== columns.length && (
              <Typography
                className="cursor-pointer"
                style={{ fontSize: 12 }}
                onClick={() => {
                  const storedData = window.localStorage.getItem(localStorageKey);
                  if (storedData) {
                    const data = JSON.parse(storedData) as ExtendedTableColumnType<T>[];
                    if (data) {
                      setCheckedList(data.map((t) => t.key as string));
                    }
                  } else {
                    setCheckedList(columns.map((c) => c.key) as string[]);
                  }
                }}
              >
                Show all
              </Typography>
            )}
          </div>
          {options.map((opt) => {
            const valueString = typeof opt.value === 'string' ? opt.value : String(opt.value);
            const labelString = typeof opt.label === 'string' ? opt.label : String(opt.label);
            // Convert searchText to lowercase
            if (!checkedList.includes(valueString))
              return (
                <div className="flex flex-row justify-between" key={opt.value}>
                  <Typography>{labelString}</Typography>
                  <Switch
                    size={'small'}
                    checked={checkedList.includes(valueString)}
                    onChange={(value) => {
                      if (!opt.defaultVal) {
                        if (value) {
                          setCheckedList([...checkedList, valueString]);
                        } else {
                          setCheckedList(checkedList.filter((item) => item !== opt.value));
                        }
                      }
                    }}
                  />
                </div>
              );
          })}
        </Checkbox.Group>
      </div>
    </Drawer>
  );
};

export default memo(TableColumnsList);
