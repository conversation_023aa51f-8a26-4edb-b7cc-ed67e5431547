pipeline {
    agent none

    environment {
        CONTAINER_IMAGE = "${env.LOOM_CI_REGISTRY}/${env.LOOM_CI_IMAGENAME}"
        DOCKER_BUILDKIT = "1"
    }

    stages {
        stage('Install') {
            agent { docker { image 'node:20.12.0-alpine' } }
            when {
                anyOf {
                    branch 'develop'
                    branch 'staging'
                    expression { return env.TAG_NAME }
                }
            }
            steps {
                sh 'npm ci'
                stash includes: 'node_modules/**', name: 'node_modules'
            }
        }

        stage('Lint') {
            agent { docker { image 'node:20.12.0-alpine' } }
            when {
                anyOf {
                    branch 'develop'
                    branch 'staging'
                    expression { return env.TAG_NAME }
                }
            }
            steps {
                unstash 'node_modules'
                sh 'npm run lint'
            }
        }

        stage('Test') {
            agent { docker { image 'node:20.12.0-alpine' } }
            when {
                anyOf {
                    branch 'develop'
                    branch 'staging'
                    expression { return env.TAG_NAME }
                }
            }
            steps {
                unstash 'node_modules'
                sh 'npm run test'
            }
        }

        stage('Build Container - Develop') {
            agent {
                docker {
                    image 'docker:latest'
                    args '--privileged'
                }
            }
            when {
                branch 'develop'
            }
            environment {
                DOCKER_HOST = 'tcp://localhost:2375'
            }
            steps {
                sh '''
                    docker login $LOOM_CI_REGISTRY -u $LOOM_CI_REGISTRY_USER -p $LOOM_CI_REGISTRY_PASS
                    docker pull $CONTAINER_IMAGE:develop || true
                    echo "$CI_COMMIT_MESSAGE" >> VERSION
                    docker build \
                        --build-arg NODE_ENV=develop \
                        --build-arg APP_VERSION=develop \
                        --build-arg BUILDKIT_INLINE_CACHE=1 \
                        --cache-from=$CONTAINER_IMAGE:develop \
                        -t $CONTAINER_IMAGE:develop .
                    docker push $CONTAINER_IMAGE:develop
                '''
            }
        }

        stage('Build Container - Tag') {
            agent {
                docker {
                    image 'docker:latest'
                    args '--privileged'
                }
            }
            when {
                buildingTag()
            }
            steps {
                sh '''
                    docker login $LOOM_CI_REGISTRY -u $LOOM_CI_REGISTRY_USER -p $LOOM_CI_REGISTRY_PASS
                    docker pull $CONTAINER_IMAGE:$TAG_NAME || true
                    echo "$CI_COMMIT_MESSAGE" >> VERSION
                    docker build \
                        --build-arg NODE_ENV=production \
                        --build-arg APP_VERSION=$TAG_NAME \
                        --cache-from=$CONTAINER_IMAGE:$TAG_NAME \
                        -t $CONTAINER_IMAGE:$TAG_NAME .
                    docker push $CONTAINER_IMAGE:$TAG_NAME
                '''
            }
        }

        stage('Deploy Develop') {
            agent { docker { image 'curlimages/curl:latest' } }
            when {
                branch 'develop'
            }
            steps {
                sh '''
                    curl --location --request POST 'https://loom.konnecta.io:9443/api/webhooks/b1ac5cf8-254f-43f3-8f09-f11606d8fdb0?tag=develop'
                '''
            }
        }
    }
}
